package com.stpl.tech.master.core.config.attribute.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.master.core.config.attribute.dao.ConfigAttributeDao;
import com.stpl.tech.master.core.config.attribute.service.ConfigAttributeService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.ConfigAttributeValueData;
import com.stpl.tech.master.domain.model.ConfigAttributeValue;

@Service
public class ConfigAttributeServiceImpl implements ConfigAttributeService {
	
	@Autowired
	private ConfigAttributeDao attributeDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ConfigAttributeValue> getCompanyAttributes(String applicationName) {
		List<ConfigAttributeValueData> valueDatas = attributeDao.getCompanyAttributes(applicationName);
		List<ConfigAttributeValue> values = new ArrayList<ConfigAttributeValue>();
		for (ConfigAttributeValueData data : valueDatas) {
			values.add(MasterDataConverter.convert(data));
		}
		return values;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ConfigAttributeValue> getAllAttributeValues() {
		List<ConfigAttributeValueData> valueDatas = attributeDao.getAllAttributeValues();
		List<ConfigAttributeValue> values = new ArrayList<ConfigAttributeValue>();
		for (ConfigAttributeValueData data : valueDatas) {
			values.add(MasterDataConverter.convert(data));
		}
		return values;
	}

}
