{"name": "kettle2.0", "author": "Kettle 2.0", "version": "4.0.18", "description": "Javascript Version", "homepage": ".", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject", "lint": "eslint --ext .js,.jsx .", "lint:fix": "eslint --fix --ext .js,.jsx .", "prettier": "prettier --write 'src/**/*.{js,jsx}'", "clear-all": "rm -rf build node_modules", "re-start": "rm -rf build node_modules && yarn install && yarn start", "re-build": "rm -rf build node_modules && yarn install && yarn build", "build:dev": "env-cmd -f .env.dev npm run build", "build:stage": "env-cmd -f .env.stage npm run build", "build:sprod": "env-cmd -f .env.sprod npm run build", "build:newstage": "env-cmd -f .env.newStage npm run build"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@auth0/auth0-spa-js": "^2.0.2", "@emotion/cache": "^11.10.5", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@fullcalendar/common": "^5.11.3", "@fullcalendar/core": "^6.0.3", "@fullcalendar/daygrid": "^6.0.3", "@fullcalendar/interaction": "^6.0.3", "@fullcalendar/list": "^6.0.3", "@fullcalendar/react": "^6.0.4", "@fullcalendar/timegrid": "^6.0.3", "@fullcalendar/timeline": "^6.0.3", "@hello-pangea/dnd": "^16.2.0", "@hookform/resolvers": "^2.9.10", "@iconify/react": "^4.0.1", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.116", "@mui/material": "^5.11.10", "@mui/system": "^5.11.5", "@mui/x-data-grid": "^5.17.19", "@mui/x-date-pickers": "^5.0.14", "@react-pdf/renderer": "^3.1.5", "@reduxjs/toolkit": "^1.9.1", "amazon-cognito-identity-js": "^6.1.2", "apexcharts": "^3.36.3", "apisauce": "^3.0.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.3.5", "buffer": "^6.0.3", "change-case": "^4.1.2", "date-fns": "^2.29.3", "env-cmd": "^10.1.0", "express": "^4.18.2", "firebase": "^9.15.0", "framer-motion": "^9.0.4", "highlight.js": "^11.7.0", "history": "^5.3.0", "i18next": "^22.4.9", "i18next-browser-languagedetector": "^7.0.1", "jsrsasign": "^10.8.6", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "mapbox-gl": "^2.12.0", "moment": "^2.29.4", "mqtt": "^4.3.7", "mui-one-time-password-input": "^1.1.0", "notistack": "^2.0.8", "nprogress": "^0.2.0", "numeral": "^2.0.6", "prop-types": "^15.8.1", "qz-tray": "^2.2.2", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.42.1", "react-i18next": "^12.1.4", "react-idle-timer": "^5.7.2", "react-lazy-load-image-component": "^1.5.6", "react-map-gl": "^7.0.21", "react-markdown": "^8.0.5", "react-material-ui-carousel": "^3.4.2", "react-organizational-chart": "^2.2.0", "react-quill": "^2.0.0-beta.4", "react-redux": "^8.0.5", "react-router": "^6.6.2", "react-router-dom": "^6.6.2", "react-scripts": "^5.0.0", "react-slick": "^0.29.0", "recharts": "^2.7.2", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "rehype-highlight": "^6.0.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "simplebar-react": "^3.1.0", "slick-carousel": "^1.8.1", "stylis": "^4.1.3", "stylis-plugin-rtl": "^2.0.1", "web-vitals": "^3.1.1", "worker-loader": "^3.0.8", "yet-another-react-lightbox": "^2.4.2", "yup": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/eslint-parser": "^7.19.1", "eslint": "^8.32.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.6.0", "eslint-config-react-app": "^7.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.1", "eslint-plugin-react-hooks": "^4.6.0", "grunt-bump": "^0.8.0", "prettier": "^2.8.3", "typescript": "^4.9.4"}, "overrides": {"nth-check": "2.1.1"}}