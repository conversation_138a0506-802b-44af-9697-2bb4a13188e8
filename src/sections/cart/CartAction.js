import moment from 'moment/moment';
import { setRightSideBar } from '../../pages/home/<USER>';
import { dispatch, store } from '../../redux/store';
// eslint-disable-next-line import/no-cycle
import { ProductService } from '../../services/ProductService';
import RestService from '../../services/RestService';
import UtilityService from '../../services/UtilityService';
import APIConstants from '../../utils/APIConstants';
import Constants from '../../utils/Constants';
import OrderDataModel from '../../utils/OrderDataModel';
import {
  isSubscriptionInCart,
  setComboInCart,
  setServiceChargeItem,
  setSpectateCustomization,
  updateCartItems,
  updateLoyalTeaInCart,
  updateProductsQty,
  updateTransactionDetail,
} from './CartSlice';
// eslint-disable-next-line import/no-cycle
import { emitMessage } from '../../cafeAppCommunication/cafeAppCommunication';
// eslint-disable-next-line import/no-cycle
import CommunicationService from '../../cafeAppCommunication/communicationService';
import {
  setChaayosCashData,
  setCustomerBasicInfo,
  setFreeLoyaltyTea,
  setLatestWalletBalance,
  setOtpStartTime,
  setOtpVerificationDetails,
  setPurchasedChaayosSelectDetails,
} from '../../components/customerInfo/CustomerSlice';
import {
  setCashbackAwardedAmount,
  setClaimedRecomProductData,
  setCurrentWalletSuggestion,
  setExploreMoreOptionCartIndexMap,
  setExploreMoreOptionsProducts,
  setIsWalletOrder,
  setServiceChargeRemovedKey,
  updateNextOffer,
} from '../../components/order/OrderSlice';
import ProgressTabButtonsAction from '../../components/progressTabButtons/ProgressTabButtonsAction';
import { NavigateViaCode } from '../../components/progressTabButtons/ProgressTabNavigation';
import { ComplOrderProgressTabBarConstant } from '../../pages/complimentry-orders/ComplimentryOrderHeaderData';
import { setCheckoutOrder } from '../../pages/complimentry-orders/ComplimentryOrderSlice';
import {
  BookWastageprogressTabBarConstant,
  UnstatisfiedCustomerprogressTabBarConstant,
  progressTabBarConstant,
} from '../../pages/home/<USER>';
import { printOnBilling } from '../../services/PrintService';
// eslint-disable-next-line import/no-cycle
import {
  setCreWalletSuggestionResponse,
  setCustomerAvailedWalletOffer,
  setPaymentEndTime,
  setRemainingAmount,
  setTotalAmount,
} from '../../components/payment/PaymentSlice';
import { setOrderStartTimer } from '../../redux/MetadataSlice';
import { SuccessMetricActions } from '../../components/success-metric/SuccessMetricActions';

// eslint-disable-next-line import/no-cycle
import OfferAction from '../coupon/OffersAction';
import { setAppliedCoupon, setShowSubscriptionCoupon } from '../coupon/OffersSlice';
// eslint-disable-next-line import/no-cycle
import { pushMessageToClevertap } from '../../services/ClevertapService';
import { setMembershipOptionDialogue } from '../../components/popups/PopupsSlice';

class CartAction {
  static addItemToCart = async (
    orderPromise,
    LoyalTea = false,
    comingFromPreviousOrder = false,
    callBack = () => {},
    sendMessage = true
  ) => {
    const orderItem = await orderPromise;
    const customer = store.getState().customerSlice.customerBasicInfo;
    const selectedComplReason =
      store.getState().complimentaryOrdersSlice.selectedComplimentryReason;
    const { productInventory, recomOfferData, catIdMap, unitSubscriptionProducts } =
      store.getState().orderSlice;
    const currentCart = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
    const { taxData } = store.getState().metadataSlice;
    const { eligibleForFreeSecondChai } = store.getState().customerSlice;
    const { complimentaryCodes } = store.getState().metadataSlice;
    let { productsQty } = JSON.parse(JSON.stringify(store.getState().cartSlice.cart));
    let emoIndexMap = { ...store.getState().orderSlice.exploreMoreOptionsCartIndexMap };

    if (UtilityService.checkEmpty(productsQty[orderItem.productId])) {
      productsQty[orderItem.productId] = 0;
    }
    if (
      !UtilityService.checkEmpty(productInventory) &&
      !UtilityService.checkEmpty(productInventory[orderItem.productId]) &&
      productInventory[orderItem.productId].quantity < productsQty[orderItem.productId] + 1
    ) {
      store.dispatch(setRightSideBar({ open: false }));
      store.dispatch(
        UtilityService.showSnackBar({
          open: true,
          snackType: Constants.SNACK_TYPE.ERROR,
          message: 'No more inventory available for the product',
        })
      );
      return;
    }

    const loyalTeaInCart = JSON.parse(
      JSON.stringify(store.getState().cartSlice.cart.loyalTeaInCart)
    );
    let discountObj = {
      discount: { percentage: 0, value: 0 },
      discountCode: null,
      discountReason: null,
      promotionalOffer: 0,
      totalDiscount: 0,
    };
    let currentCartItems = [];
    if (LoyalTea) {
      currentCart.forEach((cartItem) => {
        if (UtilityService.checkEmpty(unitSubscriptionProducts[cartItem.productId])) {
          if (cartItem.hasBeenRedeemed) {
            currentCartItems.push(cartItem);
          } else {
            const taxes = CartAction.getTotalTax(
              cartItem.amount,
              cartItem.amount,
              taxData[cartItem.code]
            );
            currentCartItems.push({
              ...cartItem,
              taxes: taxes.data,
              tax: taxes.value,
              amount: cartItem.totalAmount,
              discountDetail: discountObj,
              isRecomOfferApplied: false,
              loyaltyBurnPoints: 0,
              loyaltyBurned: false,
            });
          }
        }
      });
    } else {
      currentCartItems = currentCart;
    }
    let hasBeenRedeemed = false;
    const freeLoyaltyTea = JSON.parse(
      JSON.stringify(store.getState().customerSlice.freeLoyaltyTea)
    );
    if (
      Constants.LOYALTEA_CHAIS.includes(orderItem.productId) &&
      freeLoyaltyTea > loyalTeaInCart &&
      loyalTeaInCart < 5 &&
      LoyalTea
    ) {
      let discountReason = Constants.COUPONS.LOYALTEA;
      if (eligibleForFreeSecondChai) {
        discountReason = complimentaryCodes.SIGNUP_OFFER.code;

        // Clevertap event for second free chai redeemed
        const eventObj = {};
        eventObj['Redeemed By'] = Constants.ackSrcCre;
        pushMessageToClevertap(Constants.clevertapEventName.secFreeChaiRedeemed, eventObj);
      }
      discountObj = {
        discount: { percentage: 0, value: 0 },
        discountCode: null,
        discountReason,
        promotionalOffer: orderItem.price * orderItem.quantity,
        totalDiscount: 0,
      };

      hasBeenRedeemed = true;
      store.dispatch(updateLoyalTeaInCart(loyalTeaInCart + 1));
      // dispatch(setFreeLoyaltyTea(freeLoyaltyTea-1));
    }
    let sameItemIndex =
      (CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER &&
      selectedComplReason.id === 2110) || orderItem?.loyaltyBurned
        ? -1
        : this.getSameItemIndex(orderItem, currentCartItems);
    let newOrderItem = {};
    if (sameItemIndex > -1) {
      if (!UtilityService.checkEmpty(unitSubscriptionProducts[orderItem.productId])) {
        store.dispatch(
          UtilityService.showSnackBar({
            open: true,
            message: 'Subscription Product already added in cart',
            autoHideDuration: 2000,
          })
        );
        return;
      }

      newOrderItem = {
        ...currentCartItems[sameItemIndex],
        discountDetail: discountObj,
        hasBeenRedeemed,
      };

      let savedChaiComponent = null;

      if (!UtilityService.checkEmpty(orderItem.saveChaiName)) {
        savedChaiComponent = {
          isSavedChai: orderItem.isSavedChai,
          hasPreference: orderItem.hasPreference,
          saveChaiName: orderItem.saveChaiName,
          previousTagType: orderItem.previousTagType,
          preferenceDetail: orderItem.preferenceDetail,
        };
        newOrderItem = {
          ...newOrderItem,
          ...savedChaiComponent,
        };
      }

      newOrderItem.quantity = currentCartItems[sameItemIndex].quantity + orderItem.quantity;
      newOrderItem.amount =
        currentCartItems[sameItemIndex].amount + currentCartItems[sameItemIndex].price;
      const newItemTax = this.getTotalTax(
        newOrderItem.totalAmount,
        newOrderItem.amount,
        taxData[currentCartItems[sameItemIndex].code]
      );
      newOrderItem.taxes = newItemTax.data;
      newOrderItem.tax = newItemTax.value;
      currentCartItems.splice(sameItemIndex, 1);
    } else {
      newOrderItem = { ...orderItem, discountDetail: discountObj, hasBeenRedeemed };
      sameItemIndex = 0;
    }
    CartAction.updateProductQty(orderItem.productId, 1);
    // if (newOrderItem.code !== Constants.TAX_CODE.COMBO) {
    this.updatePriceInfo(newOrderItem);
    // }
    newOrderItem = this.getSpecialOrderItem(newOrderItem);
    currentCartItems = [newOrderItem, ...currentCartItems];
    const comboInCart = this.isComboInCart(currentCartItems);
    const isServiceChargeApplicable=store.getState().orderSlice.isServiceChargeApplied;
    store.dispatch(setComboInCart(comboInCart));
    store.dispatch(isSubscriptionInCart(this.checkSubscriptioninCart(currentCartItems)));
    store.dispatch(updateCartItems(currentCartItems));
    store.dispatch(updateTransactionDetail(this.getFinalTransactionDetail(currentCartItems,isServiceChargeApplicable)));
    const val = hasBeenRedeemed ? 1 : 0;
    productsQty = store.getState().cartSlice.cart.productsQty;
    // CartAction.eligibleLoyalTeas(newOrderItem,sameItemIndex,newOrderItem.itemKey)
    const { claimedRecomProductData } = store.getState().orderSlice;

    if (sendMessage) {
      emitMessage({
        POS_ADDED_ITEM: {
          cartItems: currentCartItems,
          loyalTeaInCart: loyalTeaInCart + val,
          productsQtyMap: productsQty,
          lastUpdatedCartItem: orderItem.itemKey,
          recomOfferClaimedData: claimedRecomProductData,
        },
      });
    }

    if (comboInCart) {
      dispatch(
        OfferAction.clearCouponDetails((transactionDetail) => {
          // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
        }),
        true
      );
    }

    // Clevertap Event Push
    try {
      CartAction.pushClvEvents(orderItem, catIdMap);
    } catch (e) {
      console.error('Error while pushing clevertap events', e);
    }

    if (!comingFromPreviousOrder) {
      store.dispatch(setRightSideBar({ open: false, code: '', data: {} }));
    }
    const { transactionDetail } = store.getState().cartSlice.cart;
    const { subscriptionInCart } = store.getState().cartSlice;
    const currentAppliedCoupon = store.getState().offersSlice.appliedCoupon;

    const c1 = !UtilityService.checkEmpty(claimedRecomProductData) && loyalTeaInCart + val === 0;
    const c2 =
      UtilityService.checkEmpty(unitSubscriptionProducts[orderItem.productId]) &&
      orderItem.productId !== Constants.getGoonjId();
    const c3 =
      !UtilityService.checkEmpty(claimedRecomProductData) &&
      productsQty[claimedRecomProductData?.productId] > 0 &&
      !subscriptionInCart &&
      UtilityService.checkEmpty(currentAppliedCoupon);
    const c4 =  (
        !UtilityService.checkEmpty(claimedRecomProductData) &&
        transactionDetail.totalAmount - claimedRecomProductData.productPrice >=
        recomOfferData?.resultAPC
      ) ||
      (
        UtilityService.checkEmpty(claimedRecomProductData) &&
          transactionDetail.totalAmount >=
          recomOfferData?.resultAPC
      );
    const c5 = c1 && c2 && c3 && c4;
    if (newOrderItem.isRecomOfferApplied || c5) {
      store.dispatch(OfferAction.handleApply(Constants.COUPONS.RECOMMENDATION_OFFER));
    }

    if (orderItem.isexploreMoreOptionsProduct) {
      const exploreMoreOptionsProduct = [...store.getState().orderSlice.exploreMoreOptionsProducts];
      const updateProductList = [];
      exploreMoreOptionsProduct.forEach((item) => {
        if (item.productId === orderItem.productId) {
          item = { ...item, quantity: item.quantity + 1 };
        }
        updateProductList.push(item);
      });

      store.dispatch(setExploreMoreOptionsProducts(updateProductList));

      currentCartItems.forEach((item, index) => {
        if (
          item.isexploreMoreOptionsProduct ||
          !UtilityService.checkEmpty(emoIndexMap[item.productId])
        ) {
          emoIndexMap = { ...emoIndexMap, [item.productId]: index };
        }
      });
      store.dispatch(setExploreMoreOptionCartIndexMap(emoIndexMap));
    }

    if (callBack && typeof callBack === 'function') {
      callBack();
    }
  };

  static getCartQuantity = () => {
    let itemsCount = 0;
    const { cartItems } = store.getState().cartSlice.cart;
    cartItems?.forEach((item) => {
      if (this.isEligibleToShowInCart(item)) {
        itemsCount += item.quantity;
      }
    });
    return itemsCount;
  };

  static isComboInCart = (cartItems) => {
    let check = false;
    cartItems.forEach((item) => {
      if (item.code === Constants.TAX_CODE.COMBO) {
        check = true;
      }
    });
    return check;
  };

  // static isSelectInCart = (cartItems) => {
  //   let check = false;
  //   cartItems.forEach((item) => {
  //     if (item.productId === Constants.PRODUCTID.CHAAYOS_SELECT) {
  //       check = true;
  //     }
  //   });
  //   return check;
  // };

  static checkSubscriptioninCart = (cartItems) => {
    let check = false;
    const { unitSubscriptionProducts } = store.getState().orderSlice;
    cartItems.forEach((item) => {
      if (!UtilityService.checkEmpty(unitSubscriptionProducts[item.productId])) {
        check = true;
      }
    });
    return check;
  };

  static pushClvEvents(orderItem, productCategory) {
    const { unitSubscriptionProducts } = store.getState().orderSlice;
    if (
      !this.isEligibleToShowInCart(orderItem) ||
      !UtilityService.checkEmpty(unitSubscriptionProducts[orderItem.productId])
    ) {
      return;
    }

    if (orderItem.hasBeenRedeemed === true) {
      const redemptionObj = {};
      redemptionObj['Action By'] = Constants.ackSrcCre;
      redemptionObj.Action = 'Redeemed';
      redemptionObj['Product Id'] = orderItem.productId;
      redemptionObj['Product Name'] = orderItem.productName;
      pushMessageToClevertap(Constants.clevertapEventName.hotRedeemed, redemptionObj);
    }

    const eventObj = {};
    eventObj['Product Name'] = orderItem.productName;
    eventObj['Product Id'] = orderItem.productId;
    eventObj['Is Saved Chai'] = orderItem.isSavedChai != null ? orderItem.isSavedChai : false;
    eventObj['Is Redeemed'] = orderItem.hasBeenRedeemed != null ? orderItem.hasBeenRedeemed : false;
    eventObj['Added By'] = Constants.ackSrcCre;
    eventObj['Is Quick Pick'] = 'No';
    eventObj.Dimension = orderItem.dimension;
    eventObj['Product Category'] =
      productCategory != null ? productCategory[orderItem.catId]?.categoryName : null;

    const customisationParameters = UtilityService.isProductCustomised(orderItem);
    if (customisationParameters != null) {
      eventObj['Is Customised'] =
        customisationParameters.doodhCustomised ||
        customisationParameters.variantCustomised ||
        customisationParameters.freeAddonsCustomised ||
        customisationParameters.paidAddonsCustomised;
    }
    pushMessageToClevertap(Constants.clevertapEventName.productAddedToCart, eventObj);

    if (!UtilityService.checkEmpty(Constants.MILK_SELECTION[orderItem.productId])) {
      // dc customised
      const dcEventObj = {};
      dcEventObj['Is Milk'] = customisationParameters.doodhCustomised;
      dcEventObj['Is Sugar'] = customisationParameters.sugarCustomised;
      dcEventObj['Is Patti'] = customisationParameters.pattiCustomised;
      dcEventObj['Is Addon'] = customisationParameters.freeAddonsCustomised;
      dcEventObj['Is Paid Addon'] = customisationParameters.paidAddonsCustomised;
      dcEventObj['Customised By'] = Constants.ackSrcCre;
      dcEventObj['Is Saved Chai'] = orderItem.isSavedChai != null ? orderItem.isSavedChai : false;
      pushMessageToClevertap(Constants.clevertapEventName.desiChaiCustomised, dcEventObj);
    }

    if (orderItem.recProd || orderItem.isexploreMoreOptionsProduct) {
      // const products = store.getState().orderSlice.recommendedProductData;
      const products = store.getState().orderSlice.allExploreMoreOptionsProductList;
      const recomEveObj = {};
      recomEveObj['Added By'] = Constants.ackSrcCre;
      recomEveObj['Product Name'] = orderItem.productName;
      recomEveObj['Product Id'] = orderItem.productId;
      recomEveObj['Recommendation Reason'] = products[orderItem.productId]?.recomReason;
      recomEveObj['Has Offer'] = orderItem.isRecomOfferApplied;
      recomEveObj['Added From'] = orderItem.recProd ? 'cart' : 'em_suggestive';
      pushMessageToClevertap(Constants.clevertapEventName.recommendationAdded, recomEveObj);
    }
  }

  static updateProductQty(productId, qty) {
    let { productsQty } = store.getState().cartSlice.cart;
    if (UtilityService.checkEmpty(productsQty)) {
      dispatch(updateProductsQty({ [productId]: qty }));
      return;
    }
    productsQty = JSON.parse(JSON.stringify(productsQty));
    if (!UtilityService.checkEmpty(productsQty[productId])) {
      productsQty[productId] += qty;
      productsQty[productId] = Math.max(productsQty[productId], 0);
    } else {
      productsQty[productId] = Math.max(qty, 0);
    }
    dispatch(updateProductsQty(productsQty));
  }

  static getSameItemIndex = (orderItem) => {
    const currentCartItems = store.getState().cartSlice.cart.cartItems;
    let itemIndex = -1;
    // eslint-disable-next-line
    currentCartItems.map((item, index) => {
      if (item.itemKey === orderItem.itemKey) {
        itemIndex = index;
      }
    });
    return itemIndex;
  };

  // static getCartItemsOnProductAdd=(currentCartItems, newOrderItem)=>{
  //   let index =-1;
  //   // eslint-disable-next-line
  //   currentCartItems.map((item,index)=>{
  //     if(item.productId)
  //   })
  // }

  static updateCartItems = async (orderPromise, index, itemKey) => {
    const orderItem = await orderPromise;
    // CartAction.eligibleLoyalTeas(orderItem,index,itemKey)
    let currentCartItems = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
    index = this.getNewIndexOfItem(itemKey, currentCartItems);
    const oldQuantity = currentCartItems[index].quantity;
    const productsQty = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.productsQty));
    const exploreMoreOptionsCartIndexMap = {
      ...store.getState().orderSlice.exploreMoreOptionsCartIndexMap,
    };

    // eslint-disable-next-line
    await Promise.all(
      orderItem?.composition?.options.map(async (item, opIndex) => {
        if (oldQuantity > orderItem.quantity) {
          currentCartItems = this.removePaidAddon(item.id, oldQuantity - orderItem.quantity);
        } else if (oldQuantity < orderItem.quantity) {
          await this.addPaidAddons(item.id, orderItem.quantity - oldQuantity);
        }
      })
    );
    currentCartItems = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
    index = this.getNewIndexOfItem(itemKey, currentCartItems);
    const sameItemIndex = this.getSameItemIndex(orderItem, currentCartItems);
    this.handleProductQtyForComboMenuProduct(
      productsQty,
      currentCartItems[sameItemIndex],
      orderItem
    );

    if (orderItem.quantity === 0) {
      currentCartItems.splice(index, 1);
      if (orderItem.isexploreMoreOptionsProduct) {
        this.deleteExploreMoreOption(currentCartItems, orderItem.productId);
      }
      if(orderItem?.loyaltyBurned){
        dispatch(
          OfferAction.clearCouponDetails((transactionDetail) => {
            // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
            },!CartAction.isClaimedRecommOfferProductInCart()
          ),
        );
      }
    } else if (sameItemIndex > -1 && index !== sameItemIndex) {
      currentCartItems[sameItemIndex].quantity += orderItem.quantity;
      currentCartItems[sameItemIndex].isRecomOfferApplied = orderItem.isRecomOfferApplied;
      currentCartItems[sameItemIndex] = this.updatePriceInfo(currentCartItems[sameItemIndex]);
      currentCartItems.splice(index, 1);
      if (orderItem.isexploreMoreOptionsProduct) {
        this.deleteExploreMoreOption(currentCartItems, orderItem.productId);
      }
    } else {
      currentCartItems[index] = orderItem;
      this.updatePriceInfo(orderItem);
    }
    store.dispatch(updateCartItems(currentCartItems));
    const comboInCart = this.isComboInCart(currentCartItems);
    store.dispatch(setComboInCart(comboInCart));
    store.dispatch(isSubscriptionInCart(this.checkSubscriptioninCart(currentCartItems)));
    store.dispatch(updateTransactionDetail(this.getFinalTransactionDetail(currentCartItems,true)));
    const { appliedCoupon } = store.getState().offersSlice;
    const { unitSubscriptionProducts } = store.getState().orderSlice;

    if (comboInCart) {
      dispatch(
        OfferAction.clearCouponDetails((transactionDetail) => {
          // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
        })
      );
    }

    const loyalTeaInCart = JSON.parse(
      JSON.stringify(store.getState().cartSlice.cart.loyalTeaInCart)
    );
    const { claimedRecomProductData } = store.getState().orderSlice;

    currentCartItems = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
    emitMessage({
      POS_ADDED_ITEM: {
        cartItems: currentCartItems,
        loyalTeaInCart,
        productsQtyMap: productsQty,
        lastUpdatedCartItem: orderItem.itemKey,
        recomOfferClaimedData: claimedRecomProductData,
      },
    });
    store.dispatch(setRightSideBar({ open: false, code: '', data: {} }));
    const { transactionDetail } = store.getState().cartSlice.cart;
    const { recomOfferData } = store.getState().orderSlice;
    const { subscriptionInCart } = store.getState().cartSlice;
    const currentAppliedCoupon = store.getState().offersSlice.appliedCoupon;
    const c1 = !UtilityService.checkEmpty(claimedRecomProductData) && loyalTeaInCart === 0;
    const c2 =
      UtilityService.checkEmpty(unitSubscriptionProducts[orderItem.productId]) &&
      orderItem.productId !== Constants.getGoonjId();
    const c3 =
      productsQty[claimedRecomProductData?.productId] > 0 &&
      !subscriptionInCart &&
      !comboInCart &&
      UtilityService.checkEmpty(currentAppliedCoupon);
    const c4 = (
        !UtilityService.checkEmpty(claimedRecomProductData) &&
        transactionDetail.totalAmount - claimedRecomProductData.productPrice >=
        recomOfferData?.resultAPC
      ) ||
      (
        UtilityService.checkEmpty(claimedRecomProductData) &&
          transactionDetail.totalAmount >=
          recomOfferData?.resultAPC
      );
    const c5 = c1 && c2 && c3 && c4;
    if (orderItem?.isRecomOfferApplied || c5) {
      store.dispatch(OfferAction.handleApply(Constants.COUPONS.RECOMMENDATION_OFFER));
    } else {
      // dispatch(OfferAction.handleApply(currentAppliedCoupon));
    }
  };

  static handleProductQtyForComboMenuProduct = (productsQty, oldItem, newItem) => {
    if (UtilityService.checkEmpty(oldItem) || oldItem.code !== Constants.TAX_CODE.COMBO) {
      return;
    }
    if (oldItem?.quantity !== newItem?.quantity) {
      oldItem?.composition?.menuProducts?.forEach((pItem) => {
        productsQty[pItem.productId] += newItem.quantity - oldItem.quantity;
      });
      dispatch(updateProductsQty(productsQty));
      return;
    }
    oldItem?.composition?.menuProducts?.forEach((pItem) => {
      let isFound = false;
      newItem?.composition?.menuProducts?.forEach((cItem) => {
        if (pItem.productId === cItem.productId) {
          isFound = true;
        }
      });
      if (!isFound) {
        productsQty[pItem.productId] -= oldItem.quantity;
      }
    });
    newItem?.composition?.menuProducts?.forEach((pItem) => {
      let isFound = false;
      oldItem?.composition?.menuProducts?.forEach((cItem) => {
        if (pItem.productId === cItem.productId) {
          isFound = true;
        }
      });
      if (!isFound) {
        productsQty[pItem.productId] += oldItem.quantity;
      }
    });
    dispatch(updateProductsQty(productsQty));
  };

  static setSpectatorModeCustomization = (data) => {
    dispatch(setSpectateCustomization(data));
  };

  static getNewIndexOfItem = (itemKey, cartItems) => {
    const currentCartItems = store.getState().cartSlice.cart.cartItems;
    let index = 0;
    // eslint-disable-next-line
    currentCartItems.forEach((item, itemIndex) => {
      if (item.itemKey === itemKey) {
        index = itemIndex;
      }
    });
    return index;
  };

  static getUpdatedCartItems = () =>
    JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));

  static checkout = (ccFlatCouponCode, callback) => {
    const { currentWalletSuggestion, isSelectOrder, isWalletOrder, unitSubscriptionProducts, productPrice } =
      store.getState().orderSlice;
    const { isRechargingWallet, walletRechargeData } = store.getState().paymentSlice;
    const { subscriptionInCart } = store.getState().cartSlice;
    const { cartItems } = store.getState().cartSlice.cart;
    const { customerBasicInfo, chaayosCashbackData } = store.getState().customerSlice;
    const { showSubscriptionCoupon } = store.getState().offersSlice;
    const pushJson = store.getState().progressTabButtonsSlice.pushJson;
    console.log('GOAL TRACKING pushjson', pushJson);
    let orderDomain = { order: this.getOrderInfo(), includeReceipts: true };

    if (UtilityService.checkEmpty(orderDomain.order?.offerCode)) {
      orderDomain.order.offerCode = ccFlatCouponCode;
    }

    if (isWalletOrder) {
      orderDomain = {
        ...orderDomain,
        walletOrder: this.getWalletOrderData(currentWalletSuggestion),
      };
    }
    if (isRechargingWallet) {
      orderDomain = {
        ...orderDomain,
        order: { ...orderDomain.order, orders: [] },
        walletOrder: this.getWalletRechargeData(walletRechargeData),
      };
    }

    if (productPrice) {
       orderDomain = { ...orderDomain,
          order: {...orderDomain.order, unitPriceProfile: productPrice.unitPriceProfile }
       };
    }

    let createOrderApi = APIConstants.getEndpoints().order.createOrder;
    if (subscriptionInCart) {
      createOrderApi = APIConstants.getEndpoints().order.createSubscriptionOrder;
    }
    let reqObj = orderDomain;
    if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER) {
      createOrderApi = APIConstants.getEndpoints().order.createComplimentaryOrder;
      reqObj = orderDomain.order;
    }

    // CustomerAction.saveChai(cartItems)

    RestService.postJSON(createOrderApi, {
      ...reqObj,
    })
      .then((orderResponse) => {
        if (
          !UtilityService.checkEmpty(orderResponse) &&
          !UtilityService.checkEmpty(orderResponse.body)
        ) {
          if (callback !== null && callback !== undefined) {
            callback(true);
          }
          dispatch(setPaymentEndTime(UtilityService.getCurrentTime()));
          if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER) {
            NavigateViaCode(BookWastageprogressTabBarConstant.CONFIRMATION);
            for (let i = 0; i < orderResponse.body.length; i += 1) {
              printOnBilling(orderResponse.body[i].printString, orderResponse.body[i].printType);
            }
          } else if (
            CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER
          ) {
            dispatch(setCheckoutOrder(false));
            NavigateViaCode(ComplOrderProgressTabBarConstant.CHECKOUT);
            printOnBilling(orderResponse.body.receipts[1], orderResponse.body.printType);
            if (!UtilityService.checkEmpty(orderResponse.body.additionalReceipts)) {
              printOnBilling(orderResponse.body.additionalReceipts[1], 'RAW');
            }
          } else if (
            CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER
          ) {
            NavigateViaCode(UnstatisfiedCustomerprogressTabBarConstant.CONFIRMATION);
            printOnBilling(orderResponse.body.receipts[0], orderResponse.body.printType);
            if (!UtilityService.checkEmpty(orderResponse.body.additionalReceipts)) {
              printOnBilling(orderResponse.body.additionalReceipts[0], 'RAW');
            }
          } else {
            NavigateViaCode(progressTabBarConstant.CONFIRMATION);
            try {
              SuccessMetricActions.postSuccessMetric(reqObj, orderResponse.body);
            } catch (error) {
              console.log('Error in success metric');
            }
            try {
              SuccessMetricActions.postWalletSuggestedEvent(reqObj, orderResponse.body);
            } catch (error) {
              console.log('Error in saving event');
            }
            dispatch(updateNextOffer(orderResponse.body.nextOffer));
            const cashBackAwarded = UtilityService.checkEmpty(
              orderResponse?.body?.order?.cashBackAwarded
            )
              ? 0
              : orderResponse?.body?.order?.cashBackAwarded;
            if (cashBackAwarded > 0) {
              dispatch(setCashbackAwardedAmount(orderResponse.body.order.cashBackAwarded));
            }
            let walletBalance = customerBasicInfo.walletBalance;
            const cashCardPurchaseAmt = orderResponse.body.cashCardPurchaseAmt || 0;
            const cashCardExtraAmt = orderResponse.body.cashCardExtraAmt || 0;
            const cashCardDeductedAmt = orderResponse.body.cashCardDeductedAmt || 0;
            walletBalance =
              walletBalance + cashCardPurchaseAmt + cashCardExtraAmt - cashCardDeductedAmt;
            const finalCashbackOfCustomer = UtilityService.checkEmpty(
              orderResponse.body?.customer?.chaayosCash
            )
              ? 0
              : orderResponse.body.customer.chaayosCash + cashBackAwarded;
            dispatch(
              setCustomerBasicInfo({
                ...customerBasicInfo,
                // walletBalance,
                loyalityPoints: orderResponse.body?.customer?.loyaltyPoints,
                chaayosCash: finalCashbackOfCustomer,
              })
            );
            dispatch(
              setChaayosCashData({ ...chaayosCashbackData, totalCashback: finalCashbackOfCustomer })
            );
            dispatch(setLatestWalletBalance(walletBalance));
            dispatch(setOrderStartTimer(Constants.STATUS.IN_ACTIVE));
            dispatch(setFreeLoyaltyTea(Math.trunc(orderResponse.body.customer.loyaltyPoints / 60)));
            dispatch(setPurchasedChaayosSelectDetails(orderResponse?.body?.subscriptionPlan));
            emitMessage({
              AMOUNT_SETTLED: {
                orderId:orderResponse?.body?.order?.orderId,
                nextOffer: orderResponse.body.nextOffer,
                walletBalance,
                isWalletOrder: isRechargingWallet || isWalletOrder,
                isMembershipPurchased: subscriptionInCart,
                isRegularOrder:
                  !(subscriptionInCart && cartItems.length === 1) && !isRechargingWallet,
                membershipValidity: {
                  ...orderResponse?.body?.subscriptionPlan,
                  pId: showSubscriptionCoupon?.productId,
                },
                updatedCustomerDetails: orderResponse?.body?.customer,
                loyaltyAwarded: orderResponse?.body?.loyaltyAwarded,
                chaayosCashEarned: 0,
                ...(!UtilityService.checkEmpty(orderResponse?.body?.order?.cashBackAwarded) && {
                  chaayosCashEarned: orderResponse?.body?.order?.cashBackAwarded,
                }),
              },
            });

            printOnBilling(orderResponse.body.receipts[0], 'RAW');
            if (!UtilityService.checkEmpty(orderResponse.body.additionalReceipts)) {
              printOnBilling(orderResponse.body.additionalReceipts[0], 'RAW');
            }
            try {
              if (CommunicationService.getOrderType() === 'order') {
                ProgressTabButtonsAction.pushGoalTrackingData(
                  orderResponse.body.order.status,
                  orderResponse.body.order.orderId,
                  orderResponse.body.customer.id,
                  orderResponse.body.channelPartner.id
                );
              }
            } catch (e) {
              console.error('Error while pushing Goal Data to Analytics', e);
            }
          }
        } 
        // else {
        //   dispatch(
        //     UtilityService.showSnackBar({
        //       open: true,
        //       snackType: Constants.SNACK_TYPE.ERROR,
        //       message: 'Order Failed due to technical error, Retry!',
        //     })
        //   );
        //   if (callback !== null && callback !== undefined) {
        //     callback(false);
        //   }
        // }
      })
      .catch((error) => {
        dispatch(
          UtilityService.showSnackBar({
            open: true,
            snackType: Constants.SNACK_TYPE.ERROR,
            message: 'Order Failed due to technical error, Retry!',
            // No autoHideDuration specified - uses default 5000ms
          })
        );
        if (callback !== null && callback !== undefined) {
          callback(false);
        }
      });
  };

  static getOrderInfo = () => {
    const { orderSource, cashRedeemed } = store.getState().cartSlice;
    const { cartItems, transactionDetail, remark } = store.getState().cartSlice.cart;
    const { orderTimeDetail, serviceChargeRemovedKey } = store.getState().orderSlice;
    const { awardLoyalty, bypassLoyateaAward } = store.getState().offersSlice;
    const { payments } = store.getState().paymentSlice;
    const { employeeLoginDetail, unitDetail } = store.getState().metadataSlice;
    const { customerBasicInfo, eligibleForFreeSecondChai, loyaltyBurnOfferDetails } = store.getState().customerSlice;
    const customerLinkedOrderId = store.getState().orderSummarySlice.linkedOrderId;
    const newCartItems = JSON.parse(JSON.stringify(cartItems));
    const { tableDetail } = store.getState().metadataSlice;
    const selectedBuzzer = store.getState().homeSlice.selectedBuzzer;
    const appHangingPayment = payments.filter((payment) => payment.id === 12);
    const noOfPax = store.getState().homeSlice.noOfPax;
    const serviceChargeItem = store.getState().cartSlice.cart.serviceChargeItem;
    let loyaltyBurnPoints = 0;
    let loyaltyBurned = false;
    newCartItems.forEach((cartItem, idx) => {
      const options = [];
      if (!UtilityService.checkEmpty(cartItem.composition?.options)) {
        cartItem?.composition?.options.forEach((paidAddon) => {
          if (!UtilityService.checkEmpty(paidAddon?.name)) {
            options.push(paidAddon?.name);
          }
        });
        cartItem.composition.options = options;
      }
      if (!UtilityService.checkEmpty(cartItem.composition?.menuProducts)) {
        if (cartItem?.composition?.menuProducts) {
          cartItem?.composition?.menuProducts.forEach((product, parentIndex) => {
            if (product?.composition?.options) {
              const paidAddonNames = [];
              product?.composition?.options.forEach((paidAddon, index) => {
                if (!UtilityService.checkEmpty(paidAddon?.name)) {
                  paidAddonNames.push(paidAddon.name);
                }
              });
              cartItem.composition.menuProducts[parentIndex].composition.options = JSON.parse(
                JSON.stringify(paidAddonNames)
              );
            }
          });
        }
      }
      loyaltyBurnPoints += cartItem.loyaltyBurnPoints;
      if(!loyaltyBurned){
        loyaltyBurned = cartItem?.loyaltyBurned
      }
    });
    const loyalTeaInCart = JSON.parse(
      JSON.stringify(store.getState().cartSlice.cart.loyalTeaInCart)
    );
    let pointsRedeemed = 0;
    if (!eligibleForFreeSecondChai && loyalTeaInCart > 0) {
        pointsRedeemed = -(loyalTeaInCart * 60);
    }
    else {
        pointsRedeemed = - loyaltyBurnPoints
    }

    const offerCode = transactionDetail?.discountDetail?.discountReason;

    let tableNumber = null;

    if (tableDetail.tableService) {
      // if (tableDetail.tableServiceType === 0) {
        tableNumber = selectedBuzzer;
      // }
    }
    let channelPartner = 1;
    if (orderSource === Constants.ORDER_SOURCE.TAKE_AWAY) {
      channelPartner = 9;
    }
    let offerAccountType = null;
    if(loyaltyBurnOfferDetails?.code === offerCode){
      offerAccountType = Constants.RIGTHSIDE_BAR.LOYALTY_OFFER
    }
    const order = {
      ...OrderDataModel.orderInfo,
      externalOrderId:
        appHangingPayment.length > 0 ? appHangingPayment[0].extraData.externalOrderId : null,
      sourceId: store.getState().paymentSlice.refrenceNumber,
      orderType: CommunicationService.getOrderType(),
      source: orderSource,
      channelPartner,
      linkedOrderId:
        CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER
          ? customerLinkedOrderId
          : null,
      employeeId: employeeLoginDetail.user.id,
      orders: newCartItems,
      unitId: unitDetail.unitId,
      terminalId: UtilityService.getTerminalId(),
      settlements: this.getSettlementData(payments),
      ...orderTimeDetail,
      customerId: customerBasicInfo.id,
      pointsRedeemed,
      currentWalletAmount: customerBasicInfo?.walletBalance,
      customerName: customerBasicInfo.name,
      newCustomer: customerBasicInfo.newCustomer,
      containsSignupOffer: eligibleForFreeSecondChai && (loyalTeaInCart > 0 || loyaltyBurned),
      offerCode,
      orderRemark: remark || null,
      billingServerTime: UtilityService.getCurrentTime(),
      billCreationSeconds: moment
        .duration(moment().diff(orderTimeDetail.billStartTime))
        .asSeconds(),
      billCreationTime: UtilityService.getCurrentTime(),
      settlementType: Constants.SETTLEMENT_TYPE.DEBIT,
      // eslint-disable-next-line
      generateOrderId: parseInt(Math.random() * ***********).toString(),
      transactionDetail,
      tableNumber,
      paymentDetailId:
        appHangingPayment.length > 0 ? appHangingPayment[0].extraData.orderPaymentDetailId : null,
      cashRedeemed: UtilityService.checkEmpty(cashRedeemed) ? 0 : cashRedeemed,
      awardLoyalty,
      bypassLoyateaAward,
      offerAccountType,
      noOfPax,
      serviceChargeItem,
      serviceChargeRemovedKey
    };
    return order;
  };

  static getWalletOrderData(data) {
    return {
      rechargeAmount: data.suggestedAmount - data.preSuggestedAmount,
      deductFromWallet: true,
      suggestedAmount: data.preSuggestedAmount,
      offerAmount: data.bonusSuggestion,
      extraAmount: data.extraBonus,
      customerPayableAmount: data.payableAmount,
    };
  }

  static getWalletRechargeData(data) {
    return {
      rechargeAmount: data.rechargeAmount,
      deductFromWallet: true,
      suggestedAmount: 0,
      offerAmount: 0,
      extraAmount: data.bonus,
    };
  }

  static getProcessedItem = async (data, quantity, itemIndex, orderItemRemark = null, comboItemQty) => {
    // let customer = store.getState().customerSlice.customerBasicInfo;
    console.log("item.qty",quantity);
    const { taxData } = store.getState().metadataSlice;
    const { productPrice } = store.getState().orderSlice;
    const { productBasicDetail, recomOfferData } = store.getState().orderSlice;
    const { productCustomization, claimedRecomProductData } = store.getState().orderSlice;
    const { complimentaryCodes } = store.getState().metadataSlice;
    const { eligibleForFreeSecondChai } = store.getState().customerSlice;
    const { cartItems } = store.getState().cartSlice.cart;
    const {
      productData,
      selectedMilkType,
      selectedDimension,
      selectedFreeAddons,
      selectedPaidAddons,
      selectedVariants,
      isCustomisable,
      loyalTea,
      loyaltyBurn,
      recProd,
      takeAway,
      itemAmount,
      isComboItem,
      itemId,
      savedChaiItem,
      previousOrder,
      isexploreMoreOptionsProduct,
    } = data;
    let { previousComposition } = data;
    console.log('data=', data);
    const selectedComplimentryReason =
      store.getState().complimentaryOrdersSlice.selectedComplimentryReason;
    let { comingFromSpectatorMode } = data;
    if (UtilityService.checkEmpty(comingFromSpectatorMode)) {
      comingFromSpectatorMode = false;
    }

    // if (!customer.otpVerified && loyalTea) {
    //   store.dispatch(setRightSideBar({ open: false, code: '', data: {} }));
    //   emitMessage({ LOYALTY_REDEEMED: true });
    //   dispatch(setOtpStartTime(UtilityService.getCurrentTime()))
    //     store.dispatch(
    //       setOtpVerificationDetails({
    //         otpLoader: true,
    //         preOtpVerifiedData: {data, quantity, itemIndex, orderItemRemark},
    //         reason: 'ok',
    //         heading: 'Loyalty Redemption',
    //       })
    //     );
    //   await new Promise((resolve) => {
    //   // Wait until otploader value becomes false
    //   const interval = setInterval(() => {
    //     const otpLoader = store.getState().customerSlice.otpVerificationDetails.otpLoader;
    //     if (otpLoader === false) {
    //       clearInterval(interval);
    //     resolve(true);
    //     }
    //   }, 100); // Adjust the interval as per your requirement
    // })
    // }
    // customer = store.getState().customerSlice.customerBasicInfo;

    // if (!customer.otpVerified && loyalTea) {
    //   return null;
    // }

    let selectedProduct = productData.productDetail;
    // eslint-disable-next-line
    quantity = quantity ? quantity : 1;
    const productTaxData = taxData[selectedProduct?.taxCode];
    const selectedProductPrice =
      productPrice?.prices[selectedProduct?.id?.id]?.prices[selectedDimension];
    let selectedProductCustomization = productCustomization[selectedProduct?.id?.id];
    const price = UtilityService.getToDecimalValue(selectedProductPrice?.price, 2);
    let amount = UtilityService.getToDecimalValue(itemAmount, 2);
    let totalAmount = UtilityService.getToDecimalValue(itemAmount, 2);
    const productAliasName = selectedProduct?.productAliasName;
    const addons = [];
    const options = [];
    const variants = [];
    const milkOptions = [];
    const menuProducts = [];
    const customizationKeys = [];
    const parentIdForMilkSelection = UtilityService.getParentIdForMilkSelection(selectedMilkType);
    const defaultDimension = ProductService.getDefaultDimension(
      productCustomization[selectedProduct.id.id]
    );
    if (UtilityService.checkEmpty(itemAmount)) {
      amount = UtilityService.getToDecimalValue(selectedProductPrice.price * quantity, 2);
      totalAmount = amount;
    }
    // eslint-disable-next-line prefer-const
    let discountDetail = {
      discount: { percentage: 0, value: 0 },
      discountCode: null,
      discountReason: null,
      promotionalOffer: 0,
      totalDiscount: 0,
    };

    if (defaultDimension !== selectedDimension) {
      customizationKeys.push(selectedDimension);
    }
    if (takeAway) {
      customizationKeys.push('takeAway');
    }
    if (!UtilityService.checkEmpty(selectedMilkType) && parentIdForMilkSelection > -1) {
      selectedProduct = productBasicDetail.products[selectedMilkType];
      selectedProductCustomization = productCustomization[selectedMilkType];
    }
    if (!UtilityService.checkEmpty(selectedMilkType) && parentIdForMilkSelection > -1) {
      milkOptions.push({
        name: Constants.DOODH_SELECTION[parentIdForMilkSelection][selectedMilkType],
        id: selectedProduct.id.id,
      });
      // customizationKeys.push(selectedProduct.name);
    }
    if (!UtilityService.checkEmpty(selectedFreeAddons)) {
      Object.keys(selectedFreeAddons).forEach((key) => {
        addons.push(selectedFreeAddons[key]);
        customizationKeys.push(selectedFreeAddons[key].name);
      });
    }
    if (!UtilityService.checkEmpty(selectedPaidAddons)) {
      Object.keys(selectedPaidAddons).forEach((key) => {
        const paidAddonDim = ProductService.getDefaultDimension(
          productCustomization[selectedPaidAddons[key].id]
        );
        const paidAddonPrice =
          productPrice.prices[selectedPaidAddons[key].id].prices[paidAddonDim].price * quantity;
        customizationKeys.push(selectedPaidAddons[key].name);
        // options.push({...selectedPaidAddons[key], name : `${selectedPaidAddons[key].name} (\u20B9 ${paidAddonPrice})`});
        options.push(selectedPaidAddons[key]);
      });
    }

    if (UtilityService.checkEmpty(isComboItem) && !isComboItem && !comingFromSpectatorMode) {
      await this.handlePaidAddon(options, cartItems, itemIndex, quantity);
    }

    if (!UtilityService.checkEmpty(selectedVariants)) {
      Object.keys(selectedVariants).forEach((key) => {
        variants.push({ ...selectedVariants[key], alias: selectedVariants[key].name });
        customizationKeys.push(selectedVariants[key].name);
      });
    }
    let complimentaryDetail = {
      reasonCode: null,
      isComplimentary: false,
      reason: null,
    };

    customizationKeys.sort();

    if (loyalTea) {
      if (eligibleForFreeSecondChai) {
        complimentaryDetail = {
          reasonCode: complimentaryCodes.SIGNUP_OFFER.id,
          isComplimentary: true,
          reason: complimentaryCodes.SIGNUP_OFFER.code,
        };
      } else {
        complimentaryDetail = {
          reasonCode: complimentaryCodes.Loyalty.id,
          isComplimentary: true,
          reason: complimentaryCodes.Loyalty.code,
        };
      }
      customizationKeys.push('LoyalTea');
    }
    if(loyaltyBurn){
      customizationKeys.push('LoyaltyBurn');
    }

    if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER) {
      complimentaryDetail = {
        reasonCode: complimentaryCodes.UnsatisfiedCustomer.id,
        isComplimentary: true,
        reason: complimentaryCodes.UnsatisfiedCustomer.code,
      };
    }

    if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER) {
      complimentaryDetail = {
        reasonCode: complimentaryCodes['Cafe Wastage'].id,
        isComplimentary: true,
        reason: complimentaryCodes['Cafe Wastage'].code,
      };
    }

    if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER) {
      complimentaryDetail = {
        reasonCode: complimentaryCodes[selectedComplimentryReason.code].id,
        isComplimentary: true,
        reason: complimentaryCodes[selectedComplimentryReason.code].code,
      };
    }
    const composition = {
      addons,
      options,
      variants,
      milkOptions,
      menuProducts,
      others: [],
      products: [],
    };
    if(UtilityService.checkEmpty(previousComposition)){
      previousComposition = composition;
    }
    let savedChaiComponent = {};
    if (!UtilityService.checkEmpty(savedChaiItem)) {
      savedChaiComponent = {
        isSavedChai: true,
        hasPreference: true,
        saveChaiName: savedChaiItem.tagType,
        previousTagType: savedChaiItem.tagType,
        preferenceDetail: {
          preferenceName: savedChaiItem.tagType,
          preferenceId: savedChaiItem.customizationId,
          preferenceType: Constants.SAVED_CHAI.PREFERENCE,
        },
      };
    }
    // eslint-disable-next-line
    const orderItemTax = CartAction.getTotalTax(amount, amount, productTaxData);
    const orderItemOriginalTax = CartAction.getTotalTax(price, price, productTaxData);
    let itemKey = `${selectedProduct?.name}_${customizationKeys.join('_')}`;
    if (selectedProduct?.type === 12) {
      itemKey = `FROM_OTHERS#${itemKey}`;
    }

    let isRecomOfferApplied = CartAction.isEligibleForRecommendationOffer(selectedProduct?.id?.id);
    // const couponApplied = store.getState().offersSlice.appliedCoupon;
    if (
      !UtilityService.checkEmpty(productData?.selectedItem) &&
      productData?.selectedItem?.isRecomOfferApplied
    ) {
      isRecomOfferApplied = true;
    }
    if (loyalTea) {
      const { loyalTeaInCart } = store.getState().cartSlice.cart;
      const { appliedCoupon } = store.getState().offersSlice;
      if (loyalTeaInCart === 0 && !UtilityService.checkEmpty(appliedCoupon)) {
        dispatch(OfferAction.clearCouponDetails());
      }
      isRecomOfferApplied = false;
    }
    if (isRecomOfferApplied) {
      store.dispatch(
        setClaimedRecomProductData({
          productId: selectedProduct?.id?.id,
          itemKey,
          productPrice: price,
        })
      );
      //   let promotionalOffer = 0;
      //   if(recomOfferData.discountType === Constants.COMBO_DISCOUNT_TYPE.FIXED)
      //   {
      //     promotionalOffer = recomOfferData.discountValue;
      //   }
      //   else if(recomOfferData.discountType === Constants.COMBO_DISCOUNT_TYPE.PERCENTAGE){
      //     promotionalOffer = UtilityService.getToDecimalValue((recomOfferData.discountValue*price)/100,2);
      //   }
      //   discountDetail =  {
      //   discount:{ percentage: 0, value: 0 },
      //   discountCode: null,
      //   discountReason: Constants.COUPONS.RECOMMENDATION_OFFER,
      //   promotionalOffer,
      //   totalDiscount: 0,
      // }

      store.dispatch(setAppliedCoupon(Constants.COUPONS.RECOMMENDATION_OFFER));
    }
    const orderItem = {
      isCustomisable,
      code: selectedProduct?.taxCode,
      complimentaryDetail,
      dimension: selectedDimension,
      composition,
      previousComposition,
      billType: 'NET_PRICE',
      amount,
      brandId: 1,
      discountDetail,
      hasBeenRedeemed: false || loyalTea,
      loyaltyBurnPoints: false || loyaltyBurn,
      loyaltyBurned: (loyaltyBurn || false) && true,
      isCombo: false || selectedProduct?.taxCode === Constants.TAX_CODE.COMBO,
      itemId: !UtilityService.checkEmpty(itemId) ? itemId : 1,
      isRecomOfferApplied,
      orderItemRemark,
      originalPrice: price,
      originalTax: orderItemOriginalTax.value,
      price,
      productAliasName,
      productId: selectedProduct?.id?.id,
      productName: selectedProduct?.name,
      productType: selectedProduct?.type,
      quantity,
      qtyAddedByCustomer: 0,
      recProd: recProd || false,
      recipeId: selectedProductCustomization.prices[selectedDimension].recipeId,
      totalAmount,
      takeAway: false || takeAway,
      tax: orderItemTax.value,
      taxes: orderItemTax.data,
      itemKey,
      catId: selectedProduct?.catId,
      ...savedChaiComponent,
      previousOrder,
      isexploreMoreOptionsProduct: isexploreMoreOptionsProduct || false,
      sourceCategory : !UtilityService.checkEmpty(data?.categoryData?.categoryName) ? data?.categoryData?.categoryName : null,
      sourceSubCategory : !UtilityService.checkEmpty(data?.categoryData?.subCategoryName) ? data?.categoryData?.subCategoryName : null,
      comboItemQty: comboItemQty != null ? comboItemQty : null,
      ruleNumber: !UtilityService.checkEmpty(data.ruleNumber) ? data.ruleNumber : null
    };
    return orderItem;
  };

  static processComboItem = async (parentProduct, itemMap, priceMap, takeAway) => {
    const { productBasicDetail } = store.getState().orderSlice;
    const { productCustomization } = store.getState().orderSlice;
    const menuProducts = [];
    let totalAmount = 0;
    let originalAmount = 0;
    let itemKey = '';

    const comboOriginalPrice = CartAction.getProductPrice(parentProduct?.id?.id);
    console.log("comboOriginalPrice::::::::",comboOriginalPrice)
    let totalComboPrice = 0.0;
    Object.values(itemMap).forEach((item) => {
      totalComboPrice += CartAction.getProductPrice(item?.id, item?.size)*item.qty;
    });

    console.log("totalcomboPrice::::::::",totalComboPrice)

    await Promise.all(
      Object.keys(itemMap).map(async (key) => {
        const item = itemMap[key];
        const productDetail = productBasicDetail.products[item.id];
        const customization = productCustomization[item.id];
        if (!CartAction.isInventoryAvalable(item.id)) {
          return;
        }
        const sizeAndId = `${item.id}_${item.size}`;
        let itemAmount = priceMap[sizeAndId];
        console.log("itemAmount",itemAmount);
        // for combos other than super combo
        if (parentProduct.subType !== Constants.COMBO_TYPE.SUPER_COMBO) {
          const itemDiscountedPrice = (comboOriginalPrice * itemAmount) / totalComboPrice;
          itemAmount = (itemDiscountedPrice*item.qty).toFixed(2);
        }

        const processedItem = await this.getProcessedItem(
          {
            productData: { productDetail, customization },
            recProd: false,
            isCustomisable: ProductService.isProductCustomisable(customization, item.size),
            selectedDimension: item.size,
            itemAmount,
            isComboItem: true,
            selectedVariants: this.setDefaultVariant(customization, item.size),
            selectedMilkType: !UtilityService.checkEmpty(
              Constants.DOODH_SELECTION[UtilityService.getParentIdForMilkSelection(item.id)]?.[
                item.id
              ]
            )
              ? item.id
              : null,
            takeAway,
          },
          item.qty,
          null,
          null,
          item.qty
        );
        console.log("processedItem::::::::", processedItem);
        totalAmount += processedItem.totalAmount;
        originalAmount += processedItem.price;
        itemKey += processedItem.itemKey;
        menuProducts.push(processedItem);
      })
    );
    if (Object.keys(itemMap).length > menuProducts.length) {
      store.dispatch(
        UtilityService.showSnackBar({
          open: true,
          type: Constants.SNACK_TYPE.ERROR,
          message: 'Few items of combo are out of stock!',
        })
      );
      return;
    }

    const parentProductDetail = productBasicDetail.products[parentProduct.id.id];
    const parentCustomization = productCustomization[parentProduct.id.id];
    const parentDefaultDimension = ProductService.getDefaultDimension(parentCustomization);
    const orderItem = await this.getProcessedItem({
      productData: { productDetail: parentProductDetail, customization: parentCustomization },
      recProd: false,
      isCustomisable: ProductService.isProductCustomisable(
        parentCustomization,
        parentDefaultDimension
      ),
      selectedDimension: parentDefaultDimension,
      itemAmount: totalAmount,
      takeAway,
    });
    orderItem.composition.menuProducts = menuProducts;
    orderItem.price = totalAmount;
    orderItem.originalPrice = originalAmount;
    orderItem.itemKey += `_${itemKey}`;
    menuProducts.forEach((item) => {
      this.updateProductQty(item.productId, item.quantity);
    });
    await this.addItemToCart(orderItem);
  };

  static getUpateComboItem = async (parentProduct, itemMap, priceMap, takeAway,oldmenuProducts) => {
    const { productBasicDetail } = store.getState().orderSlice;
    const { productCustomization } = store.getState().orderSlice;
    const menuProducts = [];
    let totalAmount = 0;
    let originalAmount = 0;
    let itemKey = '';

    const comboOriginalPrice = CartAction.getProductPrice(parentProduct?.id?.id);
    let totalComboPrice = 0.0;
    Object.values(itemMap).forEach((item) => {
      totalComboPrice += CartAction.getProductPrice(item?.id, item?.size)*item.qty;
    });

    await Promise.all(
      Object.keys(itemMap).map(async (key) => {
        const item = itemMap[key];
        const productDetail = productBasicDetail.products[item.id];
        const customization = productCustomization[item.id];
        const oldMenuItem = oldmenuProducts.find(i => i.productId === item.id);
        if (UtilityService.checkEmpty(oldMenuItem) && !CartAction.isInventoryAvalable(item.id)) {
          return;
        }

        const sizeAndId = `${item.id}_${item.size}`;
        let itemAmount = priceMap[sizeAndId];

        const orderRemark = oldMenuItem?.orderItemRemark ?oldMenuItem?.orderItemRemark:null ;

        // for combos other than super combo
        if (parentProduct.subType !== Constants.COMBO_TYPE.SUPER_COMBO) {
          const itemDiscountedPrice = (comboOriginalPrice * itemAmount) / totalComboPrice;
          itemAmount = (itemDiscountedPrice * item.qty).toFixed(2);
        }

        const processedItem = await this.getProcessedItem(
          {
            productData: { productDetail, customization },
            recProd: false,
            isCustomisable: ProductService.isProductCustomisable(customization, item.size),
            selectedDimension: item.size,
            itemAmount,
            isComboItem: true,
            selectedVariants: this.setDefaultVariant(customization, item.size),
            selectedMilkType: !UtilityService.checkEmpty(
              Constants.DOODH_SELECTION[UtilityService.getParentIdForMilkSelection(item.id)]?.[
                item.id
              ]
            )
              ? item.id
              : null,
            takeAway,
          },
          item.qty,
          null,
          orderRemark,
          item.qty
        );

        if(!UtilityService.checkEmpty(oldMenuItem)){
          processedItem.composition = oldMenuItem.composition;
        }

        totalAmount += processedItem.totalAmount;
        originalAmount += processedItem.price;
        itemKey += processedItem.itemKey;
        menuProducts.push(processedItem);
      })
    );
    if (Object.keys(itemMap).length > menuProducts.length) {
      store.dispatch(
        UtilityService.showSnackBar({
          open: true,
          type: Constants.SNACK_TYPE.ERROR,
          message: 'Few items of combo are out of stock!',
        })
      );
      return {};
    }

    return {
      totalAmount,
      originalAmount,
      itemKey,
      menuProducts
    }
  }


  static setDefaultVariant = (customization, dim) => {
    const data = {};
    customization.prices[dim]?.variant?.forEach((variant) => {
      data[variant.name] = { ...variant.options[0], variantName: variant.name };
    });
    return data;
  };

  static isInventoryAvalable = (productId, childItemQty = 1) =>
    {
    const { productInventory } = store.getState().orderSlice;
    const { productsQty } = store.getState().cartSlice.cart;
    const { productBasicDetail } = store.getState().orderSlice;
    const { unitDetail } = store.getState().metadataSlice;
    let isAvailable = false;
    // checking if product inventory is available of not
    if (
      UtilityService.checkEmpty(productInventory) ||
      UtilityService.checkEmpty(productInventory[productId]) ||
      productInventory[productId].quantity >= productsQty[productId] + childItemQty
    ) {
      isAvailable = true;
    } else {
      isAvailable = false;
    }

    // if inventory is available check for milk
    if (
      unitDetail?.milkTrackingEnabled &&
      productBasicDetail.products[productId]?.milkBasedProduct
    ) {
      return (
        isAvailable &&
        (UtilityService.checkEmpty(productInventory) ||
          UtilityService.checkEmpty(productInventory[Constants.SCM_MILK_ID]) ||
          productInventory[Constants.SCM_MILK_ID].quantity > 1)
      );
    }
    return isAvailable;
  };

  static handlePaidAddon = async (options, cartItems, itemIndex, quantity, oldOptions = []) => {
    if (UtilityService.checkEmpty(oldOptions) && !UtilityService.checkEmpty(itemIndex)) {
      oldOptions = cartItems[itemIndex]?.composition?.options;
    }
    // eslint-disable-next-line
    oldOptions?.map(async (item, i) => {
      let found = false;
      // eslint-disable-next-line
      options.map((opt, j) => {
        if (options[j].name === oldOptions[i].name) {
          found = true;
        }
      });
      if (!found) {
        // todo remove 1*quantity addon
        this.removePaidAddon(item.id, quantity);
      }
    });
    // eslint-disable-next-line
    await Promise.all(
      options.map(async (item, i) => {
        let found = false;
        // eslint-disable-next-line
        oldOptions?.map((opt, j) => {
          if (options[i].name === oldOptions[j].name) {
            found = true;
          }
        });
        if (!found) {
          // todo add 1*quantity addon
          await this.addPaidAddons(options[i].id, quantity);
        }
      })
    );
    return oldOptions;
  };

  static updatePaidAddonsForCombos = (current, prev) => {
    // eslint-disable-next-line
    current.composition.menuProducts.forEach((item, index) => {
      const prevMenuProducts = prev.composition.menuProducts[index];
      if (prevMenuProducts.productId !== item.productId) {
        // eslint-disable-next-line
        prevMenuProducts.composition?.options?.forEach((paidAddon) => {
          CartAction.removePaidAddon(paidAddon.id, prev.quantity*prevMenuProducts.comboItemQty);
        });
      } else if (current.quantity > prev.quantity) {
        // eslint-disable-next-line
        prevMenuProducts.composition?.options?.forEach((paidAddon) => {
          CartAction.addPaidAddons(paidAddon.id, (current.quantity - prev.quantity)*prevMenuProducts.comboItemQty);
        });
      } else if (current.quantity < prev.quantity) {
        // eslint-disable-next-line
        prevMenuProducts.composition?.options?.forEach((paidAddon) => {
          CartAction.removePaidAddon(paidAddon.id, (prev.quantity - current.quantity)*prevMenuProducts.comboItemQty);
        });
      }
    });
  };

  static getTotalTax = (OrderTotalAmount, OrderAmount, taxData) => {
    // total Amount is before discount
    // order amount is after discount
    const cgstTax = (OrderAmount * [taxData?.state?.cgst]) / 100;
    const sgstTax = (OrderAmount * [taxData?.state?.sgst]) / 100;
    const orderItemTaxDetail = {
      value: cgstTax + sgstTax,
      data: [
        {
          type: 'GST',
          code: 'CGST',
          percentage: taxData?.state?.cgst,
          value: cgstTax,
          total: OrderTotalAmount,
          taxable: OrderAmount,
        },
        {
          type: 'GST',
          code: 'SGST/UTGST',
          percentage: taxData?.state?.sgst,
          value: sgstTax,
          total: OrderTotalAmount,
          taxable: OrderAmount,
        },
      ],
    };
    return orderItemTaxDetail;
  };

  static updatePriceInfo = (orderItem) => {
    const { taxData } = store.getState().metadataSlice;
    const productTaxData = taxData[orderItem.code];
    if (
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER ||
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER ||
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER
    ) {
      orderItem.totalAmount = 0;
      orderItem.discountDetail.promotionalOffer = null;
      orderItem.amount = 0;
      const orderItemOriginalTax = CartAction.getTotalTax(
        orderItem.totalAmount,
        orderItem.totalAmount,
        productTaxData
      );
      const orderItemTax = CartAction.getTotalTax(
        orderItem.totalAmount,
        orderItem.amount,
        productTaxData
      );
      orderItem.originalTax = UtilityService.getToDecimalValue(orderItemOriginalTax.value, 2);
      orderItem.tax = UtilityService.getToDecimalValue(orderItemTax.value, 2);
      orderItem.taxes = orderItemTax.data;
    } else {
      orderItem.totalAmount = UtilityService.getToDecimalValue(
        orderItem.price * orderItem.quantity,
        2
      );
      if (orderItem.hasBeenRedeemed) {
        const { complimentaryCodes } = store.getState().metadataSlice;
        const { eligibleForFreeSecondChai } = store.getState().customerSlice;
        if (eligibleForFreeSecondChai) {
          orderItem.discountDetail.discountReason = complimentaryCodes.SIGNUP_OFFER.code;
        } else {
          orderItem.discountDetail.discountReason = Constants.COUPONS.LOYALTEA;
        }
        orderItem.discountDetail.promotionalOffer = UtilityService.getToDecimalValue(
          orderItem.totalAmount,
          2
        );
      }
      if (orderItem?.comboChild) {
        orderItem.amount = (orderItem.amount / orderItem.prevQuantity) * orderItem.quantity;
      } else {
        orderItem.amount =
          orderItem.totalAmount -
          orderItem.discountDetail.discount.value -
          orderItem.discountDetail.promotionalOffer;
      }
      orderItem.amount = UtilityService.getToDecimalValue(orderItem.amount, 2);
      const orderItemOriginalTax = CartAction.getTotalTax(
        orderItem.totalAmount,
        orderItem.totalAmount,
        productTaxData
      );
      const orderItemTax = CartAction.getTotalTax(
        orderItem.totalAmount,
        orderItem.amount,
        productTaxData
      );
      orderItem.originalTax = UtilityService.getToDecimalValue(orderItemOriginalTax.value, 2);
      orderItem.tax = UtilityService.getToDecimalValue(orderItemTax.value, 2);
      orderItem.taxes = orderItemTax.data;

      if (orderItem.code === Constants.TAX_CODE.COMBO) {
        orderItem?.composition?.menuProducts?.forEach((comboSelectedItem, index) => {
          orderItem.composition.menuProducts[index] = CartAction.updatePriceInfo({
            ...comboSelectedItem,
            prevQuantity: comboSelectedItem.quantity,
            quantity: comboSelectedItem.comboItemQty != null ? comboSelectedItem.comboItemQty * orderItem.quantity : orderItem.quantity,
            comboChild: true
          });
        });
      }
    }
    return orderItem;
  };

  static getTransactionDetail = (cartItem, taxData) => {
    let totalAmount = 0;
    // eslint-disable-next-line
    cartItem.map((item) => {
      totalAmount += item.amount;
    });
    const taxDetail = CartAction.getTotalTax(totalAmount, totalAmount, taxData);
    const paidAmountWithoutRound = totalAmount + taxDetail.value;
    const transactionDetail = {
      discountDetail: {},
      taxableAmount: totalAmount,
      tax: taxDetail.value,
      taxes: taxDetail.data,
      paidAmount: Math.round(paidAmountWithoutRound),
      roundOffValue: Math.round(paidAmountWithoutRound) - paidAmountWithoutRound,
      savings: 0,
    };
    return transactionDetail;
  };

  static getSettlementData = (payments) => {
    const settlements = [];
    const { edcExternalTransactionId, dqrExternalTransactionId } = store.getState().paymentSlice;
    const paymentType = store.getState().paymentSlice.paymentType;
    payments.forEach((item) => {
      if (
        paymentType === Constants.PaymentType.WITHWALLET &&
        item.paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET
      ) {
        return;
      }
      let obj = {
        mode: item.id,
        amount: parseInt(item.amount, 10),
        externalSettlements: [],
      };
      if (
        [35,36,37,39].includes(item.id)
      ) {
        const externalSettlementArray = [];
        if (!UtilityService.checkEmpty(edcExternalTransactionId[item.id])) {
          externalSettlementArray.push(edcExternalTransactionId[item.id]);
        }
        if (!UtilityService.checkEmpty(dqrExternalTransactionId[item.id])) {
          externalSettlementArray.push(dqrExternalTransactionId[item.id]);
        }
        obj = {
          mode: item.id,
          amount: parseInt(item.amount, 10),
          externalSettlements: this.makeExternalSettlement(externalSettlementArray),
        };
      }
      settlements.push(obj);
    });
    return settlements;
  };

  static updatePaidAddonInCart = (indexOfParentProduct, quantity) => {
    const currentCartItems = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
    // eslint-disable-next-line
    currentCartItems[indexOfParentProduct].composition.options.map((option) => {
      const key = `{${option.id}_}`;
      // eslint-disable-next-line
      currentCartItems.map((item, index) => {
        if (item.itemKey === key) {
          currentCartItems[index].quantity -= quantity;
          currentCartItems[index] = this.updatePriceInfo(currentCartItems[index]);
        }
        if (currentCartItems[index].quantity < 1) {
          currentCartItems.splice(index, 1);
        }
      });
    });
    return currentCartItems;
  };

  static generateItemKey(orderItem, loyalTea) {
    const customizationKeys = [];

    if (!UtilityService.checkEmpty(orderItem?.composition?.addons)) {
      Object.keys(orderItem?.composition?.addons).forEach((key) => {
        customizationKeys.push(orderItem?.composition?.addons[key].name);
      });
    }

    if (!UtilityService.checkEmpty(orderItem?.composition?.options)) {
      Object.keys(orderItem?.composition?.options).forEach((key) => {
        customizationKeys.push(orderItem?.composition?.options[key].name);
      });
    }

    if (!UtilityService.checkEmpty(orderItem?.composition?.variants)) {
      Object.keys(orderItem?.composition?.variants).forEach((key) => {
        customizationKeys.push(orderItem?.composition?.variants[key].name);
      });
    }

    customizationKeys.sort();

    if (loyalTea) {
      customizationKeys.push('LoyalTea');
    }

    const key = `${orderItem.productName}_${customizationKeys.join('_')}`;

    return key;
  }

  // static clearLoyalTea() {
  //   dispatch(setAppliedCoupon(''))
  //   dispatch(updateLoyalTeaInCart(0));
  //   const newCartItems = [];
  //   const currentCartItems = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
  //   const discountDetail = {
  //     discount: { percentage: 0, value: 0 },
  //     discountCode: null,
  //     discountReason: null,
  //     promotionalOffer: 0,
  //     totalDiscount: 0,
  //   };
  //   Object.values(currentCartItems).map((cartItem) =>
  //     !cartItem.hasBeenRedeemed ? newCartItems.push(cartItem) : newCartItems.push({...cartItem,discountDetail,amount:cartItem.totalAmount,hasBeenRedeemed:false,itemKey:CartAction.generateItemKey(cartItem)})
  //   );
  //   dispatch(updateCartItems(newCartItems));
  //   const tdetail = CartAction.getFinalTransactionDetail(newCartItems);
  //   return tdetail;
  // }

  static makeExternalSettlement(externalSettlements) {
    const externalSettlementArray = [];
    if (!UtilityService.checkEmpty(externalSettlements)) {
      externalSettlements.forEach((item) => {
        externalSettlementArray.push({
          amount: item.amount,
          externalTransactionId: item.id,
        });
      });
    }

    return externalSettlementArray;
  }

  static createCartForWalletRecharge = (walletRechargeData, callBack) => {
    const activeWalletRechargeTab = store.getState().paymentSlice.activeWalletRechargeTab;
    const cartItem = [
      {
        billType: 'NET_PRICE',
        amount: walletRechargeData.rechargeAmount,
        discountDetail: {
          discount: { percentage: 0, value: 0 },
          discountCode: null,
          discountReason: null,
          promotionalOffer: 0,
          totalDiscount: 0,
        },
        originalPrice: walletRechargeData.rechargeAmount,
        originalTax: 0,
        price: walletRechargeData.rechargeAmount,
        productName:
          activeWalletRechargeTab === Constants.walletRechargeTab.WalletRecharge
            ? 'Chaayos Wallet'
            : Constants.walletRechargeTab.AdvancePayment,
        quantity: 1,
        totalAmount: walletRechargeData.rechargeAmount,
        tax: 0,
        taxes: [],
      },
    ];
    const transactionDetail = ProductService.getTransactionObject();
    transactionDetail.totalAmount = walletRechargeData.rechargeAmount;
    transactionDetail.paidAmount = walletRechargeData.rechargeAmount;
    dispatch(updateCartItems(cartItem));
    dispatch(updateTransactionDetail(transactionDetail));
    const transactionData = {
      ...CartAction.getOrderInfo(),
      walletRechargeBonus: walletRechargeData.bonus,
    };
    dispatch(setTotalAmount(transactionDetail.paidAmount));
    dispatch(setRemainingAmount(transactionDetail.paidAmount));
    emitMessage({ TRANSACTION_SUMMARY: transactionData });
    if (callBack) {
      callBack();
    }
  };

  static async addPaidAddons(id, quantity) {
    const { productBasicDetail } = store.getState().orderSlice;
    const { productCustomization } = store.getState().orderSlice;
    const processedItem = await this.getProcessedItem(
      {
        productData: {
          productDetail: productBasicDetail.products[id],
          customization: productCustomization[id],
        },
        recProd: false,
        selectedDimension: ProductService.getDefaultDimension(productCustomization[id]),
      },
      quantity
    );
    await this.addItemToCart(
      { ...processedItem, itemKey: processedItem.itemKey.replace('FROM_OTHERS#', '') },
      false,
      false,
      () => {},
      false
    );
  }

  static removePaidAddon = (id, quantity) => {
    const currentCart = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
    const isServiceChargeApplicable=store.getState().orderSlice.isServiceChargeApplied;
    let index = 0;
    if (UtilityService.checkEmpty(currentCart)) {
      return currentCart;
    }
    // eslint-disable-next-line
    currentCart.map((cartItem, itemIndex) => {
      if (cartItem.productId === id && cartItem.quantity >= quantity) {
        index = itemIndex;
      }
    });
    if (currentCart[index].productId === id && currentCart[index].quantity >= quantity) {
      currentCart[index].quantity -= quantity;
      currentCart[index] = this.updatePriceInfo(currentCart[index]);
    }
    if (currentCart[index].quantity === 0) {
      currentCart.splice(index, 1);
    }
    CartAction.updateProductQty(id, (-1*quantity));
    dispatch(updateCartItems(currentCart));
    dispatch(updateTransactionDetail(this.getFinalTransactionDetail(currentCart,isServiceChargeApplicable)));
    return JSON.parse(JSON.stringify(currentCart));
  };

  static eligibleLoyalTeas(currentCart) {
    let eligibleTeas = store.getState().cartSlice.eligibleLoyalTeas;
    const { productCustomization } = store.getState().orderSlice;
    let newEligibleTeas = {};
    currentCart.forEach((orderItem) => {
      const dim = ProductService.getDefaultDimension(productCustomization[orderItem.productId]);
      if (
        orderItem.hasBeenRedeemed ||
        !Constants.LOYALTEA_CHAIS.includes(orderItem.productId) ||
        orderItem.dimension !== dim
      ) {
        return;
      }

      if (UtilityService.checkEmpty(eligibleTeas)) {
        eligibleTeas = { [orderItem.itemKey]: { ...orderItem } };
      } else {
        eligibleTeas = JSON.parse(JSON.stringify(eligibleTeas));
        eligibleTeas[orderItem.itemKey] = { ...orderItem };
      }
    });

    const sortedEntries = Object.entries(eligibleTeas).sort(
      (item1, item2) => item1[1].amount - item2[1].amount
    );
    const sortedKeys = sortedEntries.map((entry) => entry[0]);
    newEligibleTeas = sortedKeys.reduce((obj, key) => {
      obj[key] = eligibleTeas[key];
      return obj;
    }, {});
    return newEligibleTeas;
  }

  static getSpecialOrderItem(orderItem) {
    const { taxData } = store.getState().metadataSlice;
    const productTaxData = taxData[orderItem.code];
    if (
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER ||
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER ||
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER
    ) {
      orderItem.totalAmount = 0;
      orderItem.discountDetail.promotionalOffer = null;
      orderItem.amount = 0;
      const orderItemOriginalTax = CartAction.getTotalTax(
        orderItem.totalAmount,
        orderItem.totalAmount,
        productTaxData
      );
      const orderItemTax = CartAction.getTotalTax(
        orderItem.totalAmount,
        orderItem.amount,
        productTaxData
      );
      orderItem.originalTax = UtilityService.getToDecimalValue(orderItemOriginalTax.value, 2);
      orderItem.tax = UtilityService.getToDecimalValue(orderItemTax.value, 2);
      orderItem.taxes = orderItemTax.data;
    }
    return orderItem;
  }

  static priceExits(productId, dim) {
    const { productPrice, productCustomization } = store.getState().orderSlice;
    let dimension = dim;
    if(UtilityService.checkEmpty(productCustomization[productId]) || UtilityService.checkEmpty(productPrice?.prices[productId]))
    {
      return null;
    }
    if (UtilityService.checkEmpty(dimension)) {
      dimension = ProductService.getDefaultDimension(productCustomization[productId]);
    }
    const price = productPrice?.prices[productId]?.prices[dimension];
    return price?.price;
  }

  static sendMessageToCafeApp = (data) => {
    emitMessage(data);
  };

  static isEligibleForRecommendationOffer = (productId) => {
    let pId = productId;
    const parentId = UtilityService.getParentIdForMilkSelection(productId);
    const { recomOfferData, recommendedProductData, claimedRecomProductData } =
      store.getState().orderSlice;
    const { loyalTeaInCart, transactionDetail } = store.getState().cartSlice.cart;
    const { subscriptionInCart, comboInCart } = store.getState().cartSlice;
    const customer = store.getState().customerSlice.customerBasicInfo;
    const productsQty = store.getState().cartSlice.cart.productsQty;
    const { appliedCoupon } = store.getState().offersSlice;
    const exploreMoreOptionsProducts = store.getState().orderSlice.exploreMoreOptionsProducts;

    if (parentId > -1) {
      pId = parentId;
    }

    const c1 =
      recomOfferData?.productIds?.includes(pId) &&
      UtilityService.checkEmpty(claimedRecomProductData);
    const c2 = !UtilityService.checkEmpty(recommendedProductData) && recommendedProductData[pId];
    const c3 = recomOfferData?.resultAPC <= transactionDetail.totalAmount && loyalTeaInCart === 0;
    const c4 =
      !customer?.hasSubscription && !comboInCart && UtilityService.checkEmpty(appliedCoupon);
    const c5 =
      !subscriptionInCart && (UtilityService.checkEmpty(productsQty[pId]) || productsQty[pId] <= 0);
    const c7 =
      !UtilityService.checkEmpty(exploreMoreOptionsProducts) &&
      exploreMoreOptionsProducts.filter((prod) => prod.productId === pId).length > 0;
    const c6 = c1 && (c2 || c7) && c3 && c4 && c5;
    return c6;
  };

  static isEligibleToShowInCart(cartItem) {
    let val = true;
    if (cartItem.productType === 12 && cartItem.itemKey.indexOf('FROM_OTHERS#') === -1) {
      val = false;
    }
    return val;
  }

  static getCustomizationObject = (product, customObj) => {
    const {
      productPrice,
      productInventory,
      productBasicDetail,
      productCustomization,
      recomOfferData,
    } = store.getState().orderSlice;
    const customization = productCustomization[product.productId];
    const productDetail = productBasicDetail.products[product.productId];
    const defaultDimension = ProductService.getDefaultDimension(
      productCustomization[product.productId]
    );

    let item = null;
    if (UtilityService.checkEmpty(customObj.id)) {
      customObj = { ...customObj, id: customObj.productId };
    }
    if (customObj.type === 'VARIANT') {
      customization.prices[defaultDimension]?.variant.forEach((variantType) => {
        variantType.options.forEach((variant) => {
          if (variant.name === customObj.name) {
            item = { ...variant, variantName: variantType.name };
          }
        });
      });
      if (UtilityService.checkEmpty(item)) {
        customization.prices[defaultDimension]?.variant.forEach((variantType) => {
          variantType.options.forEach((variant) => {
            if (variant.id === customObj.id) {
              item = { ...variant, variantName: variantType.name };
            }
          });
        });
      }
    } else if (customObj.type === 'FREE_ADDON') {
      customization.prices[defaultDimension]?.addon[0].options.forEach((addon) => {
        if (addon.name === customObj.name || addon.id === customObj.id) {
          item = addon;
        }
      });
    } else if (customObj.type === 'PAID_ADDON' || customObj.type === 'FREE_OPTION') {
      customization.prices[defaultDimension]?.paidAddon[0]?.options.forEach((paidaddon) => {
        if (
          (paidaddon.name === customObj.name || paidaddon.id === customObj.id) &&
          (CartAction.priceExits(paidaddon.id)>0 || CartAction.priceExits(paidaddon.id) === 0 )&&
          (UtilityService.checkEmpty(productInventory) ||
            UtilityService.checkEmpty(productInventory[paidaddon.id]) ||
            productInventory[paidaddon.id].quantity > 0)
        ) {
          item = paidaddon;
        }
      });
    }
    return item;
  };

  static getProductPrice = (productId, dim) => {
    const { productPrice } = store.getState().orderSlice;
    let dimension = dim;
    if (UtilityService.checkEmpty(dimension)) {
      dimension = ProductService.getDefaultDimensionById(productId);
    }
    return productPrice?.prices[productId]?.prices[dimension]?.price;
  };

  static addSubscriptionProductInCart = async (subscriptionProduct, dimension, callBack) => {
    const { productBasicDetail, productCustomization } = store.getState().orderSlice;
    const productId = subscriptionProduct.productId;
    const productDetail = productBasicDetail.products[productId];
    const customization = productCustomization[productId];
    let selectedDimension = dimension;
    if (UtilityService.checkEmpty(dimension)) {
      selectedDimension = ProductService.getDefaultDimension(customization);
    }
    await CartAction.addItemToCart(
      CartAction.getProcessedItem({
        productData: { productDetail, customization },
        isCustomisable: false,
        isRecommProduct: false,
        selectedDimension,
      }),
      false,
      false,
      callBack
    );
  };

  static handleBuyMembership = (SubscriptionProduct, dimension) => {
    const { subscriptionInCart } = store.getState().cartSlice;
    const { customerBasicInfo } = store.getState().customerSlice;
    const { cartItems } = store.getState().cartSlice.cart;
    if (!subscriptionInCart) {
      const couponObj = {
        newCustomer: customerBasicInfo?.newCustomer,
        couponCode: SubscriptionProduct.skuCode,
        skipSubscriptionValidation: true,
      };
      if (UtilityService.checkEmpty(cartItems)) {
        CartAction.addSubscriptionProductInCart(SubscriptionProduct, dimension, () => {
          dispatch(isSubscriptionInCart(true));
          dispatch(
            setShowSubscriptionCoupon({
              status: true,
              productId: SubscriptionProduct.productId,
              dimension,
            })
          );
        });
      } else {
        dispatch(
          OfferAction.applyCoupon(couponObj, async (info, offerDescription) => {
            await CartAction.addSubscriptionProductInCart(SubscriptionProduct, dimension, () => {
              dispatch(isSubscriptionInCart(true));
              dispatch(
                setShowSubscriptionCoupon({
                  status: true,
                  productId: SubscriptionProduct.productId,
                  dimension,
                })
              );
            });
            const { transactionDetail } = store.getState().cartSlice.cart;
            emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription } });
          })
        );
      }
    } else {
      dispatch(
        UtilityService.showSnackBar({
          open: true,
          message: ' Subscription already added in cart',
          autoHideDuration: 2000,
        })
      );
    }
    dispatch(setMembershipOptionDialogue({ open: false }));
  };

  static deleteExploreMoreOption = (currentCartItems, productId) => {
    const exploreMoreOptionsProduct = store.getState().orderSlice.exploreMoreOptionsProducts;

    const updatedExploreMoreOptions = [];

    exploreMoreOptionsProduct.forEach((item) => {
      let updatedItem = item;
      if (item.productId === productId) {
        updatedItem = { ...updatedItem, quantity: 0 };
      }
      updatedExploreMoreOptions.push(updatedItem);
    });

    dispatch(setExploreMoreOptionsProducts(updatedExploreMoreOptions));

    let emoIndexMap = {};
    currentCartItems.forEach((item, index) => {
      if (item.isexploreMoreOptionsProduct) {
        emoIndexMap = { ...emoIndexMap, [item.productId]: index };
      }
    });
    dispatch(setExploreMoreOptionCartIndexMap(emoIndexMap));
  };

  static isClaimedRecommOfferProductInCart(){
    const { transactionDetail } = store.getState().cartSlice.cart;
    const { recomOfferData } = store.getState().orderSlice;
    const { subscriptionInCart } = store.getState().cartSlice;
    const currentAppliedCoupon = store.getState().offersSlice.appliedCoupon;
    const { claimedRecomProductData } = store.getState().orderSlice;
    const loyalTeaInCart = JSON.parse(
      JSON.stringify(store.getState().cartSlice.cart.loyalTeaInCart)
    );

    const currentCartItems = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
    const comboInCart = this.isComboInCart(currentCartItems);
    const productsQty = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.productsQty));
    const c1 = !UtilityService.checkEmpty(claimedRecomProductData) && loyalTeaInCart === 0;
    const c2 =true ;
    const c3 =
      productsQty[claimedRecomProductData?.productId] > 0 &&
      !subscriptionInCart &&
      !comboInCart
    const c4 = (
        !UtilityService.checkEmpty(claimedRecomProductData) &&
        transactionDetail.totalAmount - claimedRecomProductData.productPrice >=
        recomOfferData?.resultAPC
      ) ||
      (
        UtilityService.checkEmpty(claimedRecomProductData) &&
          transactionDetail.totalAmount >=
          recomOfferData?.resultAPC
      );
    const c5 = c1 && c2 && c3 && c4;
    return c5;
  }

  static getUpdatedOrderItem = (orderItem) => {

    const currentCartItems = store.getState().cartSlice.cart.cartItems;
    let updatedItem = null;

    currentCartItems.forEach((item, index) => {
      if (item.itemKey === orderItem.itemKey) {
        updatedItem = item;
      }
    });
    return updatedItem;
  };

  static isServiceChargeApplicable = () => {
    const { serviceChargeProductIds }=store.getState().metadataSlice.allCafeAppProperties;
    const { productBasicDetail } = store.getState().orderSlice;
    const currentCart = store.getState().cartSlice.cart.cartItems;
    const scProdId = serviceChargeProductIds?.find(id => !UtilityService.checkEmpty(productBasicDetail.products[id]));
    const { unitDetail } = store.getState().metadataSlice;
    const { initiateTablepaymentSettlement,tableSummary } = store.getState().tableSlice;
    const { orderSource } = store.getState().cartSlice;
    const {isRechargingWallet} = store.getState().paymentSlice;

    let scaTotalAmount = 0;
    let scaTaxableAmount = 0;
    if(!UtilityService.checkEmpty(scProdId) && unitDetail.serviceChargePosEnabled &&
    unitDetail?.serviceCharge && this.isValid(currentCart,productBasicDetail) && !this.allTakeAway(currentCart) &&
    orderSource === Constants.ORDER_SOURCE.CAFE &&
    !(CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER ||
    CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER ||
    CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER) && !isRechargingWallet){
    currentCart.forEach((orderItem) => {
      if(productBasicDetail.products[orderItem.productId].serviceChargeApplicable){
        scaTotalAmount += orderItem.totalAmount;
        scaTaxableAmount += orderItem.amount;
      }
    });
    if(scaTotalAmount > 0){
      currentCart.forEach((orderItem, i) => {
        if(productBasicDetail.products[orderItem.productId].type !== 12){
            orderItem?.composition?.options?.forEach(paI => {
              console.log("orderItm - ",orderItem.productId,orderItem.isServiceChargeApplied);
              const fpaI = currentCart.find(it => it.productName === paI?.name && it.quantity >= orderItem.quantity);
              if(!UtilityService.checkEmpty(fpaI) && productBasicDetail.products[orderItem.productId].serviceChargeApplicable && !productBasicDetail.products[fpaI.productId].serviceChargeApplicable){
                const preDisctotalAmount = fpaI.totalAmount/fpaI.quantity;
                const postDisctotalAmount = fpaI.amount/fpaI.quantity;
                scaTotalAmount += preDisctotalAmount * orderItem.quantity;
                scaTaxableAmount += postDisctotalAmount * orderItem.quantity;
              }else if(!UtilityService.checkEmpty(fpaI) && !productBasicDetail.products[orderItem.productId].serviceChargeApplicable && productBasicDetail.products[fpaI.productId].serviceChargeApplicable){
                const preDisctotalAmount = fpaI.totalAmount/fpaI.quantity;
                const postDisctotalAmount = fpaI.amount/fpaI.quantity;
                scaTotalAmount -= preDisctotalAmount * orderItem.quantity;
                scaTaxableAmount -= postDisctotalAmount * orderItem.quantity;
              }
            });

            orderItem?.composition?.menuProducts?.forEach(mP => {
              mP?.composition?.options?.forEach(paI => {
                const fpaI = currentCart.find(it => it.productName === paI?.name && it.quantity >= orderItem.quantity);
                if(!UtilityService.checkEmpty(fpaI) && productBasicDetail.products[orderItem.productId].serviceChargeApplicable && !productBasicDetail.products[fpaI.productId].serviceChargeApplicable){
                  const preDisctotalAmount = fpaI.totalAmount/fpaI.quantity;
                  const postDisctotalAmount = fpaI.amount/fpaI.quantity;
                  scaTotalAmount += preDisctotalAmount * mP.quantity;
                  scaTaxableAmount += postDisctotalAmount * mP.quantity;
                }else if(!UtilityService.checkEmpty(fpaI) && !productBasicDetail.products[orderItem.productId].serviceChargeApplicable && productBasicDetail.products[fpaI.productId].serviceChargeApplicable){
                  const preDisctotalAmount = fpaI.totalAmount/fpaI.quantity;
                  const postDisctotalAmount = fpaI.amount/fpaI.quantity;
                  scaTotalAmount -= preDisctotalAmount * mP.quantity;
                  scaTaxableAmount -= postDisctotalAmount * mP.quantity;
                }
              });
            });
          }
      });
    }
  }

   console.log("Service Charge Total Applicable ", scaTotalAmount);
    return scaTotalAmount > 0;
  }
 
  static removeServiceCharge = async (transactionDetail) => {
    const serviceChargeItem = store.getState().cartSlice.cart.serviceChargeItem;
    const cartItems = store.getState().cartSlice.cart.cartItems;
    const isServiceChargeApplied = store.getState().orderSlice.isServiceChargeApplied;
    if(!UtilityService.checkEmpty(serviceChargeItem) || transactionDetail.serviceCharge > 0){
      if (!isServiceChargeApplied) {
        let serviceChargeRemovedKey = null;
        if (serviceChargeItem.totalAmount !== 0) {
          serviceChargeRemovedKey = {
            serviceChargeRemoved: Constants.Y,
            serviceChargeValue: serviceChargeItem.totalAmount
          };
        }
        store.dispatch(setServiceChargeRemovedKey(serviceChargeRemovedKey));
      }
      store.dispatch(setServiceChargeItem(null));
      store.dispatch(updateTransactionDetail(this.getFinalTransactionDetail(cartItems, false)));
    } else {
      store.dispatch(setServiceChargeRemovedKey(null));
    }
  }

  static addServiceCharge = async (totalAmount,taxableAmount,totalTaxData,transactionDetail, isserviceChargeApplied) => {
    const {
      productBasicDetail,
      productCustomization
    } = store.getState().orderSlice;
    const { taxData,allCafeAppProperties } = store.getState().metadataSlice;
    const {unitDetail} = store.getState().metadataSlice;
    transactionDetail.serviceChargePercent = unitDetail.serviceCharge;

    const scproductId = this.getServiceChargeProductIds().find((pId) => !UtilityService.checkEmpty(productBasicDetail.products[pId]));
    const productDetail = productBasicDetail.products[scproductId];

    if(UtilityService.checkEmpty(scproductId) || UtilityService.checkEmpty(productDetail)){
      return;
    }
    if(totalAmount === 0.00){
      this.removeServiceCharge(transactionDetail);
      return;
    }

    const scApplicableAmount = allCafeAppProperties.calServiceChargePreDiscount ? totalAmount : taxableAmount;
    const serviceCharge = scApplicableAmount * unitDetail.serviceCharge / 100;

    const customization = productCustomization[scproductId];
    let milkType = null;
    const dimension = ProductService.getDefaultDimension(customization);
    const parentIndex = UtilityService.getParentIdForMilkSelection(scproductId);
    if (parentIndex > -1) {
      milkType = parentIndex.toString();
    }

    const defVar = {};
    customization.prices[dimension]?.variant?.forEach((variant) => {
      defVar[variant.name] = { ...variant.options[0], variantName: variant.name };
    });

    // const orderItem = await this.getProcessedItem(
    //   {
    //     productData: { productDetail, customization },
    //     recProd: false,
    //     selectedVariants: defVar,
    //     selectedMilkType: milkType,
    //     isCustomisable: true,
    //     selectedDimension:dimension,
    //   },1,null,null,false,serviceCharge
    // )
    const productTaxData = taxData[productDetail?.taxCode];
    const orderItemTax = CartAction.getTotalTax(serviceCharge, serviceCharge, productTaxData);
    const orderItem = {
      isCustomisable:true,
      code: productDetail?.taxCode,
      complimentaryDetail:{
        reasonCode: null,
        isComplimentary: false,
        reason: null,
      },
      dimension,
      composition:{
        addons:[],
        options:[],
        variants:[],
        milkOptions:[],
        menuProducts:[],
        others: [],
        products: [],
      },
      previousComposition:{
        addons:[],
        options:[],
        variants:[],
        milkOptions:[],
        menuProducts:[],
        others: [],
        products: [],
      },
      billType: 'NET_PRICE',
      amount:serviceCharge,
      brandId: 1,
      discountDetail:{
        discount: { percentage: 0, value: 0 },
        discountCode: null,
        discountReason: null,
        promotionalOffer: 0,
        totalDiscount: 0,
      },
      hasBeenRedeemed: false,
      loyaltyBurnPoints: false,
      loyaltyBurned: false,
      isCombo: false,
      itemId: 1,
      isRecomOfferApplied:false,
      orderItemRemark:null,
      originalPrice:serviceCharge,
      originalTax: orderItemTax.value,
      price:serviceCharge,
      productAliasName:productDetail?.aliasName,
      productId: productDetail?.id?.id,
      productName: productDetail?.name,
      productType: productDetail?.type,
      quantity:1,
      qtyAddedByCustomer: 0,
      recProd: false,
      recipeId: customization.prices[dimension].recipeId,
      totalAmount:serviceCharge,
      takeAway: false,
      tax: orderItemTax.value,
      taxes: orderItemTax.data,
      itemKey: `${productDetail?.id?.id}_${dimension}`,
      catId: productDetail?.catId,
      previousOrder:null,
      isexploreMoreOptionsProduct:false,
      sourceCategory: null,
      sourceSubCategory: null,
      comboItemQty: null,
      isHoldOn:Constants.N
    };

    const itemTax = {data:null,value:0};
    itemTax.data = orderItem.taxes;
    itemTax.value = orderItem.tax;

    // Modified this to keep track of Service Charge Removal value
    if (isserviceChargeApplied) {
      itemTax.data.forEach((tax, j) => {
        if (tax.value <= 0) {
          return;
        }
        const key = `${tax.type}_${tax.code}_${tax.percentage}`;
        if (UtilityService.checkEmpty(totalTaxData[key])) {
          totalTaxData[key] = { ...tax };
          transactionDetail.tax += tax.value;
        } else {
          totalTaxData[key].total += tax.total;
          totalTaxData[key].taxable += tax.taxable;
          totalTaxData[key].value += tax.value;
          transactionDetail.tax += tax.value;
        }
      });
      store.dispatch(setServiceChargeItem(orderItem));
      transactionDetail.taxableAmount += serviceCharge;
      transactionDetail.serviceCharge = UtilityService.getToDecimalValue(serviceCharge, 2);
      store.dispatch(setServiceChargeRemovedKey(null));
    } else {
      let serviceChargeRemovedKey = null;
      if (serviceCharge !== 0) {
        serviceChargeRemovedKey = {
          serviceChargeRemoved: Constants.Y,
          serviceChargeValue: serviceCharge
        };
      }
      store.dispatch(setServiceChargeRemovedKey(serviceChargeRemovedKey));
      store.dispatch(setServiceChargeItem(null));
      transactionDetail.serviceCharge = 0;
    }

  }

  static getServiceChargeProductIds = () => {
    const { serviceChargeProductIds } = store.getState().metadataSlice.allCafeAppProperties;
    return serviceChargeProductIds || [];
  }

  static allTakeAway = (cartItems) => cartItems.every((cart) => cart.takeAway === true);

  static isValid = (cartItems, productBasicDetail) => {
    if (!Array.isArray(cartItems) || cartItems.length === 0) return false;

    return cartItems.some(cart =>
      (cart.takeAway === false || UtilityService.checkEmpty(cart.takeAway)) &&
      productBasicDetail?.products?.[cart.productId]?.serviceChargeApplicable === true && (UtilityService.checkEmpty(cart.comboChild) || cart.comboChild === false)
    );
  };

  static getFinalTransactionDetail = (cartItems,isserviceChargeApplied = false) => {
    const { taxData } = store.getState().metadataSlice;
    const totalTaxData = {};
    const prevTransactionDetail = store.getState().cartSlice.cart.transactionDetail;
    const { recomOfferData, claimedRecomProductData, productPrice, unitSubscriptionProducts,productBasicDetail } =
      store.getState().orderSlice;
    const { appliedCoupon } = store.getState().offersSlice;
    const { initiateTablepaymentSettlement,tableSummary } = store.getState().tableSlice;

    const transactionDetail = JSON.parse(JSON.stringify(ProductService.getTransactionObject()));
    let loyalTeaInCart = 0;
    const newCartItems = JSON.parse(JSON.stringify(cartItems));
    const { offerUnlocked } = store.getState().progressTabButtonsSlice;
    const { unitDetail } = store.getState().metadataSlice;
    const { orderSource } = store.getState().cartSlice;
    const {isRechargingWallet} = store.getState().paymentSlice;

    let check = false;
    cartItems.forEach((item) => {
      if (item.code === Constants.TAX_CODE.COMBO) {
        check = true;
        item.composition.menuProducts.forEach((menuProduct) => {
          newCartItems.push({ ...menuProduct, isMenuProduct: true });
        });
      }
    });
    console.log("CartItems::",cartItems);
    dispatch(setComboInCart(check));
    let scaTotalAmount = 0; // service charge applicable total amount
    let scaTaxableAmount = 0;// service charge applicable taxable amount
    const scaCartItems = [];// service charge applicable cartItems
    // let paTotalAmount = 0;//paid addon totalAmount for those whose parentItem serviceCharge Not Applicable
    // let paTaxableAmount =0;
    // eslint-disable-next-line
    let serviceChargeFlag = this.isValid(newCartItems, productBasicDetail) && !this.allTakeAway(newCartItems);
    newCartItems.forEach((orderItem, i) => {
      if (orderItem.hasBeenRedeemed) {
        loyalTeaInCart += (orderItem?.loyaltyCount || orderItem.quantity);
      }
      if (orderItem.isMenuProduct) {
        transactionDetail.savings +=
          orderItem.originalPrice + orderItem.originalTax - (orderItem.amount + orderItem.tax);
      } else {
        let discount = 0;
        if(!this.getServiceChargeProductIds().includes(orderItem.productId)){
          transactionDetail.totalAmount += orderItem.totalAmount;
          transactionDetail.taxableAmount += orderItem.amount;
        }
        console.log("Service charge productIds::::",this.getServiceChargeProductIds(),productBasicDetail.products);
        const scproductId = this.getServiceChargeProductIds().find((pId) => !UtilityService.checkEmpty(productBasicDetail.products[pId]));
        orderItem.isServiceChargeApplied = false;
        if(!isRechargingWallet && unitDetail?.serviceChargePosEnabled && !UtilityService.checkEmpty(unitDetail.serviceCharge) && !UtilityService.checkEmpty(scproductId) && !UtilityService.checkEmpty(productBasicDetail.products[scproductId]) &&
          !(CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER ||
        CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER ||
        CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER) &&
         orderSource === Constants.ORDER_SOURCE.CAFE && !this.getServiceChargeProductIds().includes(orderItem.productId) 
          && productBasicDetail.products[orderItem.productId].serviceChargeApplicable && serviceChargeFlag
        ){
          scaTotalAmount += orderItem.totalAmount;
          scaTaxableAmount += orderItem.amount;
          orderItem.isServiceChargeApplied = true;
        }

        let discExist = true;
        if (
          UtilityService.checkEmpty(orderItem.discountDetail.discount.percentage) &&
          UtilityService.checkEmpty(orderItem.discountDetail.promotionalOffer) &&
          UtilityService.checkEmpty(orderItem.discountDetail.discount.value) &&
          UtilityService.checkEmpty(orderItem.discountDetail.discountReason) &&
          UtilityService.checkEmpty(orderItem.discountDetail.totalDiscount)
        ) {
          discExist = false;
        }
        if (discExist) {
          transactionDetail.discountDetail.discount.percentage =
            orderItem.discountDetail.discount.percentage;
          // transactionDetail.taxableAmount -=
          //   orderItem.discountDetail.promotionalOffer + orderItem.discountDetail.discount.value;
          transactionDetail.discountDetail.promotionalOffer +=
            orderItem.discountDetail.promotionalOffer;
          transactionDetail.discountDetail.discount.value +=
            orderItem.discountDetail.discount.value;
          if (UtilityService.checkEmpty(transactionDetail.discountDetail.discountReason)) {
            transactionDetail.discountDetail.discountReason =
              orderItem.discountDetail.discountReason;
          }
          if (
            !UtilityService.checkEmpty(
              unitSubscriptionProducts[orderItem.discountDetail.discountReason]
            )
          ) {
            transactionDetail.savings +=
              orderItem.discountDetail.totalDiscount +
              CartAction.getTotalTax(
                orderItem.discountDetail.totalDiscount,
                orderItem.discountDetail.totalDiscount,
                taxData[orderItem.code]
              ).value;
          }
          discount =
            orderItem.discountDetail.promotionalOffer + orderItem.discountDetail.discount.value;
        }
      }

      const productTaxData = taxData[orderItem.code];
      const itemTax = CartAction.getTotalTax(
        orderItem.totalAmount,
        orderItem.amount,
        productTaxData
      );

      // if (orderItem.isMenuProduct) {
      //   itemTax.data = orderItem.taxes;
      //   const sgst = orderItem?.taxes?.find(ti => ti.code === 'SGST/UTGST');
      //   const cgst = orderItem?.taxes?.find(ti => ti.code === 'CGST');
      //   itemTax.value = sgst.value + cgst.value
      // }


      // eslint-disable-next-line
      itemTax.data.forEach((tax, j) => {
        if (tax.value <= 0) {
          return;
        }
        const key = `${tax.type}_${tax.code}_${tax.percentage}`;
        if (UtilityService.checkEmpty(totalTaxData[key])) {
          totalTaxData[key] = { ...tax };
          transactionDetail.tax += tax.value;
        } else {
          totalTaxData[key].total += tax.total;
          totalTaxData[key].taxable += tax.taxable;
          totalTaxData[key].value += tax.value;
          transactionDetail.tax += tax.value;
        }
      });
    });

    console.log("orderItm - ",newCartItems);
    newCartItems.forEach((orderItem, i) => {
      console.log("orderItm - ",orderItem.productId,orderItem.isServiceChargeApplied);
      if (!orderItem.isMenuProduct) {
      if(productBasicDetail.products[orderItem.productId].type !== 12){
          orderItem?.composition?.options?.forEach(paI => {
            console.log("orderItm - ",orderItem.productId,orderItem.isServiceChargeApplied);
            const fpaI = newCartItems.find(it => it.productName === paI?.name && it.quantity >= orderItem.quantity);
            if(!UtilityService.checkEmpty(fpaI) && orderItem.isServiceChargeApplied && !fpaI.isServiceChargeApplied){
              const preDisctotalAmount = fpaI.totalAmount/fpaI.quantity;
              const postDisctotalAmount = fpaI.amount/fpaI.quantity;
              scaTotalAmount += preDisctotalAmount * orderItem.quantity;
              scaTaxableAmount += postDisctotalAmount * orderItem.quantity;
            }else if(!UtilityService.checkEmpty(fpaI) && !orderItem.isServiceChargeApplied && fpaI.isServiceChargeApplied){
              const preDisctotalAmount = fpaI.totalAmount/fpaI.quantity;
              const postDisctotalAmount = fpaI.amount/fpaI.quantity;
              scaTotalAmount -= preDisctotalAmount * orderItem.quantity;
              scaTaxableAmount -= postDisctotalAmount * orderItem.quantity;
            }
          });

          orderItem?.composition?.menuProducts?.forEach(mP => {
            mP?.composition?.options?.forEach(paI => {
              let fpaI;
              if(!UtilityService.checkEmpty(paI)) fpaI = newCartItems.find(it => it.productName === paI?.name && it.quantity >= orderItem.quantity);
              if(!UtilityService.checkEmpty(fpaI) && orderItem.isServiceChargeApplied && !fpaI.isServiceChargeApplied){
                const preDisctotalAmount = fpaI.totalAmount/fpaI.quantity;
                const postDisctotalAmount = fpaI.amount/fpaI.quantity;
                scaTotalAmount += preDisctotalAmount * mP.quantity;
                scaTaxableAmount += postDisctotalAmount * mP.quantity;
              }else if(!UtilityService.checkEmpty(fpaI) && !orderItem.isServiceChargeApplied && fpaI.isServiceChargeApplied){
                const preDisctotalAmount = fpaI.totalAmount/fpaI.quantity;
                const postDisctotalAmount = fpaI.amount/fpaI.quantity;
                scaTotalAmount -= preDisctotalAmount * mP.quantity;
                scaTaxableAmount -= postDisctotalAmount * mP.quantity;
              }
            });
          });


        }
      }
    });
    dispatch(updateLoyalTeaInCart(loyalTeaInCart));
    // eslint-disable-next-line
    this.addServiceCharge(scaTotalAmount, scaTaxableAmount,totalTaxData,transactionDetail, isserviceChargeApplied);
    // if((isserviceChargeApplied || initiateTablepaymentSettlement) && UtilityService.checkEmpty(tableSummary?.settledOrderId) ){
    //   this.addServiceCharge(scaTotalAmount, scaTaxableAmount,totalTaxData,transactionDetail);
    // }
    Object.keys(totalTaxData).forEach((key) => transactionDetail.taxes.push(totalTaxData[key]));
    const finalAmountBeforeRound = transactionDetail.taxableAmount + transactionDetail.tax;

    transactionDetail.paidAmount = Math.round(finalAmountBeforeRound);
    transactionDetail.roundOffValue = transactionDetail.paidAmount - finalAmountBeforeRound;
    transactionDetail.discountDetail.totalDiscount +=
      transactionDetail.discountDetail.promotionalOffer +
      transactionDetail.discountDetail.discount.value;



    transactionDetail.roundOffValue = UtilityService.getToDecimalValue(
      transactionDetail.roundOffValue,
      2
    );
    transactionDetail.tax = UtilityService.getToDecimalValue(transactionDetail.tax, 2);
    transactionDetail.taxableAmount = UtilityService.getToDecimalValue(
      transactionDetail.taxableAmount,
      2
    );
    transactionDetail.discountDetail.totalDiscount = UtilityService.getToDecimalValue(
      transactionDetail.discountDetail.totalDiscount,
      2
    );
    transactionDetail.discountDetail.promotionalOffer = UtilityService.getToDecimalValue(
      transactionDetail.discountDetail.promotionalOffer,
      2
    );
    transactionDetail.discountDetail.discount.value = UtilityService.getToDecimalValue(
      transactionDetail.discountDetail.discount.value,
      2
    );
    transactionDetail.totalAmount = UtilityService.getToDecimalValue(
      transactionDetail.totalAmount,
      2
    );

    transactionDetail.serviceCharge = UtilityService.getToDecimalValue(
      transactionDetail.serviceCharge,
      2
    );

    transactionDetail.savings = UtilityService.getToDecimalValue(transactionDetail.savings, 2);
    if (prevTransactionDetail.paidAmount !== transactionDetail.paidAmount) {
      dispatch(setCustomerAvailedWalletOffer(false));
      dispatch(setIsWalletOrder(false));
      dispatch(setCreWalletSuggestionResponse(null));
      dispatch(setCurrentWalletSuggestion(null));
      emitMessage({ WALLET_SUGGESTION_OPENED: { currentWalletSuggestion: null } });
    }
    dispatch(updateTransactionDetail(transactionDetail));

    if (
      (!UtilityService.checkEmpty(claimedRecomProductData) &&
        transactionDetail.totalAmount - claimedRecomProductData.productPrice <
        recomOfferData?.resultAPC &&
        appliedCoupon === Constants.COUPONS.RECOMMENDATION_OFFER)
      || (!UtilityService.checkEmpty(appliedCoupon) &&
        (appliedCoupon === offerUnlocked?.offerCode || appliedCoupon === offerUnlocked?.couponCode) &&
        transactionDetail?.taxableAmount < offerUnlocked.minBillValue
      )
    ) {
      let tranDetail = transactionDetail;
      dispatch(
        OfferAction.clearCouponDetails((tdetail) => {
          tranDetail = tdetail;
          // emitMessage({ COUPON_APPLIED: { transactionDetail:tdetail, offerDescription: null } });
        }),
        true
      );
      return tranDetail;
    }
    return transactionDetail;
  };

  static serviceChargeRemoval =   (isServiceChargeApplicable) => {
    const cartItem = store.getState().cartSlice.cart.cartItems;
    console.log("cartItems:::",cartItem);
    const tdetail = this.getFinalTransactionDetail(cartItem, isServiceChargeApplicable);
    dispatch(updateTransactionDetail(tdetail));
    emitMessage({SERVICE_CHARGE_CHANGED : {transactionDetail: tdetail}})
  }

  static getValidRecommendations = (data = []) =>
    Array.isArray(data)
      ? data.filter(product => {
          const id = product.productId;
          const isPriceAvailable = !UtilityService.checkEmpty(CartAction.priceExits(id));
          const isDisplay = product.display;
          const isInventoryAvailable = CartAction.isInventoryAvalable(id);
          return isPriceAvailable && isDisplay && isInventoryAvailable;
        })
      : [];


}

export default CartAction;
