import { useEffect, useState } from "react";
import { Box, Stack, Typography, useTheme } from "@mui/material";
import moment from 'moment';
import { StyledIcon } from "../../layouts/dashboard/header/styles";
import UtilityService from "../../services/UtilityService";
import CartAction from "./CartAction";
import { iconConfig } from "../../icon-config";
import { dispatch, useSelector } from "../../redux/store";
import { UnSelectedBackground } from "../../layouts/baseStyles";
import CouponSection from "../coupon/CouponSection";
import { setTableFooterMenuModalState } from "../../components/TableService/TableSlice";
import { setCouponError } from "../coupon/OffersSlice";
import { ProductService } from "../../services/ProductService";
import Constants from "../../utils/Constants";
import PreviousOrder from "../../components/RightSideBar/order/PreviousOrder";

export default function CartHeader(){
  const theme = useTheme();
  const [toggleCustomerProfile, setToggleCustomerProfile] = useState(false);
  const [toggleOffersSection, setToggleOffersSection] = useState(false);
  const customer = useSelector((store) => store.customerSlice.customerBasicInfo);
  const selectedBuzzer = useSelector((store) => store.homeSlice.selectedBuzzer);
  const { isTablefooterMenuModalOpen } = useSelector((store) => store.tableSlice);
  const { unitSubscriptionProducts } = useSelector((store) => store.orderSlice);
  const { freeLoyaltyTea, chaayosCashbackData } = useSelector((store) => store.customerSlice);
  const previousOrderDetails = useSelector((store) => store.customerSlice.previousOrders);

  useEffect(() => {
    if (!isTablefooterMenuModalOpen ) {
      closeAllButtonsView();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTablefooterMenuModalOpen]);

   function stringAvatar(name) {
    const nameInitials = !UtilityService.checkEmpty(name) ? `${name.split(' ')[0][0]}` : 'C';
    return {
      sx: {
        bgcolor: theme.palette.dustyPink[300],
        height: '50px',
        width: '50px',
        aspectRatio: '1/1',
        borderRadius: 1000,
        display: 'flex',
        mr: 1,
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
      },

      children: nameInitials,
    };
  }

    const isBirthdayWeek = (date) => {
    const currDate = new Date().getTime();
    const days = UtilityService.getDayDifferenceFromNow(date, currDate);
    if (days <= 7) {
      return true;
    }
    return false;
  };

  const handleOffersSection = (value) => {
    closeAllButtonsView();
    dispatch(setTableFooterMenuModalState(value));
    setToggleOffersSection(value);
    if (!isTablefooterMenuModalOpen) {
      dispatch(
        setCouponError({
          couponCode: '',
          errorMessage: '', 
        })
      );
    }
  };

  const handleCustomerProfile = (value) => {
    closeAllButtonsView();
    dispatch(setTableFooterMenuModalState(value));
    setToggleCustomerProfile(value);
  };

  const closeAllButtonsView = () => {
    setToggleOffersSection(false);
    // setToggleRecommendations(false);
    setToggleCustomerProfile(false);
    dispatch(setTableFooterMenuModalState(false));
  };

    return (
      <Stack>
        <Stack sx={{ display: 'flex', px: 2, pt: 1 }}>
            <Box
              sx={{
                color: theme.palette.leafGreen[500],
                display: 'flex',
                justifyContent: `${!UtilityService.checkEmpty(selectedBuzzer) ? 'space-between' : 'space-between'
                  }`,
                width: '100%',
                textAlign: 'end',
                pb: 0.5,
                ...(CartAction.getCartQuantity() === 0 && { color: theme.palette.leafGreen[200] }),
                borderWidth: '0px 0px 1px 0px',
                borderStyle: 'solid',
                borderColor: theme.palette.grey[400],
              }}
            >
            <Typography 
             variant="caption" 
             color={!UtilityService.checkEmpty(customer?.contact) ? theme.palette.leafGreen[500] : theme.palette.neutral.grey} 
             sx={{ 
               display: 'flex',
               alignItems: 'center',
               cursor: !UtilityService.checkEmpty(customer?.contact) ? 'pointer' : 'not-allowed',
               fontWeight: 700,
               pointerEvents: !UtilityService.checkEmpty(customer?.contact) ? 'auto' : 'none',
              }}
             onClick={() => handleCustomerProfile(!toggleCustomerProfile)}>
             <StyledIcon
                sx={{
                  height: 20,
                  width: 20,
                  mr: 0.5,
                  mb: 0,
                  color: !UtilityService.checkEmpty(customer?.contact) ? theme.palette.leafGreen[500] : theme.palette.neutral.grey,
                }}
              >
                {iconConfig.profile}
              </StyledIcon>
              {(customer?.name?.split(" ")[0] || customer?.name) || 'Chai Lover'}{false ? ' X ' : ''}
              </Typography>
              {/* <Typography
                variant="caption"
                color={theme.palette.leafGreen[500]}
                sx={{
                  display: 'flex', 
                  alignItems: 'center',
                  cursor: 'pointer',
                  fontWeight: 700,
                }}
                onClick={() => handleOffersSection(!toggleOffersSection)}
              >
                <StyledIcon 
                  sx={{
                    height: 20,
                    width: 20,
                    mr: 0.5,
                    mb: 0,
                    color: theme.palette.leafGreen[500],
                  }}
                >
                  {iconConfig.offersStar}
                </StyledIcon>
                Offers
              </Typography> */}
            </Box>
        </Stack>
        <Stack>
          {toggleOffersSection  ? (
            <>
            <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              zIndex: 5, 
            }}
            onClick={() => handleOffersSection(false)}
          />
            <UnSelectedBackground
              sx={{
                position: 'fixed',
                bottom: 30,
                right: '0%',
                background: '#ffffff',
                height: '93%',
                minWidth: '400px',
                width: '30%',
                zIndex: 7,
                py: 1,
                flexDirection: 'column',
                marginRight: '1%'
              }}
            >
              <Stack direction="row" justifyContent="space-between" mx={3} mb={2} sx={{
                borderWidth: '0px 0px 1px 0px',
                borderStyle: 'solid',
                borderColor: theme.palette.grey[300],
                py: 1,
              }}>
                <Typography variant="body4" sx={{ color: theme.palette.common.black }}>
                  OFFERS
                </Typography>
                <Typography onClick={() => handleOffersSection(false)} variant="body2" sx={{ color: theme.palette.common.black }}>
                  X
                </Typography>
              </Stack>
              
              <CouponSection />
            </UnSelectedBackground>
            </>
          ) : null}
        </Stack>
        <Stack>
          {toggleCustomerProfile ? (
            <>
            <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              zIndex: 5, 
            }}
            onClick={() => handleCustomerProfile(false)}
          />
            <UnSelectedBackground
              sx={{
                position: 'fixed',
                bottom: 30,
                right: '0%',
                background: theme.palette.neutral.white,
                height: '93%',
                width: '30%',
                minWidth: '400px',
                zIndex: 7,
                py: 1,
                flexDirection: 'column',
                marginRight: '1%',
              }}
            >
              <Stack
                sx={{
                  px: 3,
                }}
              >
                <Stack
                  sx={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    my: 2,
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      ml: 1,
                    }}
                  >
                    <Box sx={{ ...stringAvatar(customer.name).sx }}>
                      <Typography
                        variant="h5"
                        sx={{
                          width: '100%',
                          textAlign: 'center',
                          fontWeight: 700,
                          textTransform: 'capitalize',
                        }}
                      >
                        {stringAvatar(customer.name).children}
                      </Typography>
                    </Box>
                  </Box>
                  <Stack sx={{ display: 'flex', justifyContent: 'end', mb: 0.5, flex: 3 }}>
                    <Typography
                      variant="subtitle6"
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        mb: -0.8,
                        flex: '1',
                        textTransform: 'capitalize',
                      }}
                    >
                      {customer.name || 'Chai Lover'}
                      {!UtilityService.checkEmpty(customer.subscriptionInfoDetail) &&
                        customer?.hasSubscription &&
                        !UtilityService.checkEmpty(
                          unitSubscriptionProducts[customer.subscriptionInfoDetail?.subscriptionCode]
                            ?.productId
                        ) ? (
                        <Box
                          component="img"
                          src={ProductService.getProductImage(
                            unitSubscriptionProducts[
                              customer.subscriptionInfoDetail?.subscriptionCode
                            ].productId,
                            Constants.IMAGE_TYPES.GRID_MENU_100X100
                          )}
                          sx={{
                            height: 28,
                            ml: 0.5,
                            mb: 0.5,
                            aspectRatio: '1/1',
                          }}
                        />
                      ) : (
                        <Box />
                      )}
                      {customer.dateOfBirth && isBirthdayWeek(customer.dateOfBirth) ? (
                        <Typography variant="body3">&nbsp;(Birthday Week 🎂)</Typography>
                      ) : null}
                    </Typography>
                    {!UtilityService.checkEmpty(customer.subscriptionInfoDetail) &&
                      customer?.hasSubscription ? (
                      <Stack sx={{ flex: 1 }}>
                        <Typography
                          variant="caption"
                          color={theme.palette.neutral.grey}
                          sx={{ flex: 1 }}
                        >
                          Valid till{' '}
                          {moment(customer?.subscriptionInfoDetail?.endDate).format("DD MMM 'YY")}
                        </Typography>
                        {customer?.subscriptionInfoDetail?.remainingChai > 0 ? (
                          <Typography
                            variant="caption"
                            color={theme.palette.neutral.grey}
                            sx={{ flex: 1 }}
                          >
                            {customer?.subscriptionInfoDetail?.remainingChai} Chai Available
                          </Typography>
                        ) : (
                          <Box />
                        )}
                      </Stack>
                    ) : (
                      <Typography variant="caption" sx={{ flex: 1 }}>
                        {customer.newCustomer ? (
                          <Typography color={theme.palette.neutral.grey}>New member</Typography>
                        ) : (
                          <Box />
                        )}
                      </Typography>
                    )}
                  </Stack>
                  <Typography onClick={() => handleOffersSection(false)} variant="body2" sx={{ color: theme.palette.common.black }}>
                  X
                </Typography>
                </Stack>
                <Stack
                  sx={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}
                >
                  <Stack>
                    <Typography variant="body3">Redeem</Typography>
                    <Stack sx={{ flex: 2, ml: 1, mt: 1, flexDirection: 'column' }}>
                      <Typography display="flex" variant="body4" sx={{ mr: 1 }}>
                        <StyledIcon sx={{ mr: 0.5, color: theme.palette.neutral.grey }}>
                          {iconConfig.loyaltyPointsIcon}
                        </StyledIcon>
                        {customer.loyalityPoints} Points
                      </Typography>
                      <Typography display="flex" variant="body4" sx={{ mt: 0 }}>
                        <StyledIcon sx={{ mr: 1, color: theme.palette.leafGreen[400] }}>
                          {iconConfig.loyalteaCount}
                        </StyledIcon>
                        {freeLoyaltyTea} Free Chai
                      </Typography>
                    </Stack>
                  </Stack>
                  <Stack>
                    <Typography variant="body3">
                      <StyledIcon sx={{ mr: 0.5, color: theme.palette.leafGreen[500] }}>
                        {iconConfig.walletAmount}
                      </StyledIcon>
                      Wallet Balance
                    </Typography>

                    <Stack sx={{ width: "100%", flex: 2, flexDirection: 'row' }}>
                      {/* <Button
                      sx={{
                        height: '35px',
                        maxHeight: '35px',
                        backgroundColor: theme.palette.neutral.white,
                        width: '100%',
                        ...(cartItems.length > 0 && {
                          bgcolor: theme.palette.specialColors.UNSELECTED_BACKGROUND,
                          color: theme.palette.neutral.grey,
                          borderColor: theme.palette.neutral.grey,
                        }),
                      }}
                      variant={(isRechargingWallet) ? "disabled" : "outlined"}
                      onClick={handleRecharge}
                    >
                      Recharge
                    </Button> */}
                      <Typography justifyContent="flex-end" width="100%" display="flex" variant="body4">
                        &#8377;{' '}
                        {UtilityService.checkEmpty(customer?.walletBalance)
                          ? 0
                          : customer?.walletBalance}
                      </Typography>
                    </Stack>
                    {!UtilityService.checkEmpty(customer.id) &&
                      Constants.EXCLUDED_CUSTOMER_IDS.indexOf(customer.id) === -1 &&
                      !UtilityService.checkEmpty(chaayosCashbackData?.totalCashback) ? (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between',
                          flex: 1.5,
                          mt: 2,
                          height: '100%',
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            flex: 1.5,
                            height: '100%',
                          }}
                        >
                          <StyledIcon sx={{ mr: 0.5, color: theme.palette.leafGreen[500], width: 28, height: 25 }}>
                            {iconConfig.chaayosCash}
                          </StyledIcon>
                          <Typography sx={{ flex: 1.5, mb: 0.5 }} variant="body3" component="div">
                            Chaayos Cash
                          </Typography>
                        </Box>
                        <Stack
                          spacing={1}
                          // border="1px solid"
                          sx={{ width: "100%", flex: 4, '& > *': { flexGrow: 1 } }}
                        >
                          <Typography justifyContent="flex-end" width="100%" display="flex" variant="body4">
                            &#8377; {chaayosCashbackData.totalCashback}
                          </Typography>
                        </Stack>
                      </Box>
                    ) : (
                      <Box />
                    )}
                  </Stack>
                </Stack>
              </Stack>
              {!UtilityService.checkEmpty(previousOrderDetails?.previousOrderList) && (
                <Stack
                  sx={{
                    my: 1,
                    overflow: 'hidden',
                  }}
                >
                  <Typography variant="subtitle6" sx={{ px: 2 }}>
                    Previous Order
                  </Typography>
                  <Stack
                    sx={{
                      overflow: 'scroll',
                    }}
                  >
                    <PreviousOrder previousOrders={previousOrderDetails} sx={{ display: 'flex' }} />
                  </Stack>
                </Stack>
              )}
            </UnSelectedBackground>
            </>
          ) : null}
        </Stack>
       </Stack>
    );
}