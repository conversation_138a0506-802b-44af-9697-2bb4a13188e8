import {
  Box,
  Button,
  Divider,
  FormControlLabel,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Typography,
  styled,
  useTheme,
} from '@mui/material';
import { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router';
import moment from 'moment/moment';
import { emitMessage } from '../../cafeAppCommunication/cafeAppCommunication';
import {
  clearCustomerSlice,
  resetOtpVerificationDetails,
  setCustomerBasicInfo,
  setDroolsData,
  setOtpStartTime,
  setOtpVerificationDetails,
} from '../../components/customerInfo/CustomerSlice';
import {
  setClaimedRecomProductData,
  setCurrentWalletSuggestion,
  setIsRecommendationInCart,
  setIsWalletOrder,
  setRecommendedProductData,
  setRecommendedProductDataIndex,
  setShowRecomAnimation,
} from '../../components/order/OrderSlice';
import SelectedOrderItem from '../../components/order/order-category-section/SelectedOrderItem';
import PaymentActions from '../../components/payment/PaymentActions';
import {
  setCreWalletSuggestionResponse,
  setCustomerAvailedWalletOffer,
  setIsRechargingWallet,
  setPaymentStartTime,
  setPaymentType,
} from '../../components/payment/PaymentSlice';
import { goBack, NavigateViaCode } from '../../components/progressTabButtons/ProgressTabNavigation';
import { iconConfig } from '../../icon-config';
import { StyledIcon } from '../../layouts/dashboard/header/styles';
import HomeAction from '../../pages/home/<USER>';
import {
  BookWastageprogressTabBarConstant,
  UnstatisfiedCustomerprogressTabBarConstant,
  progressTabBarConstant,
  progressTabBarData,
} from '../../pages/home/<USER>';
import {
  clearHomeSlice,
  setCurrentProcessTabs,
  setIsCustomerInteracting,
  setRightSideBar,
  setShowBuzzerScreen,
} from '../../pages/home/<USER>';
import { setOrderStartTimer, setOrderTimemm, setOrderTimess } from '../../redux/MetadataSlice';
import { dispatch, store as store1, useSelector } from '../../redux/store';
import UtilityService from '../../services/UtilityService';
import typography from '../../theme/typography';
import Constants from '../../utils/Constants';
import OfferAction from '../coupon/OffersAction';
import { clearOfferSlice, setAppliedCoupon, setCouponError, setShowUpsellingRecommProduct } from '../coupon/OffersSlice';
import {
  clearCartSlice,
  isSubscriptionInCart,
  setComingFromRecharge,
  setOrderRemark,
  updateAutoClickOnProceedBtn,
  updateCartItems,
} from './CartSlice';
import FinalPage from './OrderSummary';
import { WalletSuggestionAction } from './WalletSuggestionAction';
import OrderAction from '../../components/order/OrderAction';
import SelectedComboOrderItem from '../../components/order/order-category-section/SelectedComboOrderItem';
import CartAction from './CartAction';
import CommunicationService from '../../cafeAppCommunication/communicationService';
import { ComplOrderProgressTabBarConstant } from '../../pages/complimentry-orders/ComplimentryOrderHeaderData';
import { setCheckoutOrder } from '../../pages/complimentry-orders/ComplimentryOrderSlice';
import ProgressTabButtonsAction from '../../components/progressTabButtons/ProgressTabButtonsAction';
import CustomerAction from '../../components/customerInfo/customerAction';
import { SelectedBackground, UnSelectedBackground } from '../../layouts/baseStyles';
import { setFreebieCouponDialogue } from '../../components/popups/PopupsSlice';
import CartHeader from './CartHeader';

const CutOut = styled(Box)(({ theme }) => ({
  position: 'absolute',
  width: '30px',
  height: '30px',
  borderRadius: '50%',
  border: '2px dashed',
  right: '-20px',
  top: 0,
  bottom: 0,
  margin: 'auto 0',
  bgcolor: '#faf8f5',
  borderColor: theme.palette.leafGreen[500],
}));

export default function Cart() {
  const navigate = useNavigate();
  const { cartItems, transactionDetail } = useSelector((store) => store.cartSlice.cart);
  const { showUpsellingRecommProduct } = useSelector((store) => store.offersSlice);
  let customer = useSelector((store) => store.customerSlice.customerBasicInfo);
  if (!UtilityService.checkEmpty(customer)) {
    customer = {
      ...customer,
      name: customer?.name?.split(" ")[0] || customer?.name
    }
  }
  const [iShowSuggestion, setShowSuggestion] = useState(true);
  const [cartAmount, setCartAmount] = useState(0);
  const isCustomerInteracting = useSelector((store) => store.homeSlice.isCustomerInteracting);
  const otpLoader = useSelector((store) => store.customerSlice.otpVerificationDetails.otpLoader);
  const loyalTeaInCart = useSelector((store) => store.cartSlice.cart.loyalTeaInCart);
  const droolsData = useSelector((store) => store.customerSlice.droolsData);
  const productsQty = useSelector((store) => store.cartSlice.cart.productsQty);
  const { subscriptionInCart, comboInCart, autoClickOnProceedBtn } = useSelector(
    (store) => store.cartSlice
  );
  const { isWalletOrder, claimedRecomProductData, unitSubscriptionProducts, productBasicDetail } =
    useSelector((store) => store.orderSlice);
  const { appliedCoupon, showSubscriptionCoupon, offerApplyResponsePending } = useSelector((store) => store.offersSlice);
  const theme = useTheme();
  const [finalPage, setFinalPage] = useState(false);
  const checkOutOrder = useSelector((store) => store.complimentaryOrdersSlice.checkOutOrder);
  const selectedBuzzer = useSelector((store) => store.homeSlice.selectedBuzzer);
  const { isUnlockOfferTaken } = useSelector((store) => store.offersSlice);
  const { offerUnlocked } = useSelector((store) => store.progressTabButtonsSlice);
  const { eligibleForFreeSecondChai, loyaltyBurnOfferDetails } = useSelector(
    (store) => store.customerSlice
  );
  const selectSuggestedInSession = useSelector(
    (store) => store.progressTabButtonsSlice.selectSuggestedInSession
  );

  const isRechargingWallet = useSelector((store) => store.paymentSlice.isRechargingWallet);
  const comingFromRecharge = useSelector((store) => store.cartSlice.comingFromRecharge);
  const { showOrderSummary } = useSelector((state) => state.cartSlice);

  useEffect(() => {
    if (
      !UtilityService.checkEmpty(claimedRecomProductData) &&
      productsQty[claimedRecomProductData?.productId] <= 0
    ) {
      dispatch(setClaimedRecomProductData({}));
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productsQty]);

  useEffect(() => {
    let amount = 0;
    cartItems.forEach((item) => {
      amount += item.price * item.quantity;
    });
    setCartAmount(amount);
    // eslint-disable-next-line
  }, [transactionDetail]);

  useEffect(() => {
   const hasRecommendation = cartItems.some(item => item.recProd === true);
   dispatch(setIsRecommendationInCart(hasRecommendation));
  },[cartItems])

  function getFreebieProductName() {
    const name = [];
    offerUnlocked?.productList?.forEach((id) => {
      if (!UtilityService.checkEmpty(productBasicDetail?.products?.[id])) {
        name.push(productBasicDetail?.products?.[id].name);
      }
    });
    return name.join(', ');
  }

  async function handleProceed() {
    if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER) {
      store1.dispatch(setIsRechargingWallet(false));
      store1.NavigateViaCode(UnstatisfiedCustomerprogressTabBarConstant.PAYMENT);
      store1.dispatch(setCreWalletSuggestionResponse(null));
      store1.dispatch(setCurrentWalletSuggestion(null));
      emitMessage({ WALLET_SUGGESTION_OPENED: { currentWalletSuggestion: null } });
      return;
    }
    if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER) {
      store1.dispatch(setIsRechargingWallet(false));
      NavigateViaCode(BookWastageprogressTabBarConstant.PAYMENT);
      store1.dispatch(setCreWalletSuggestionResponse(null));
      store1.dispatch(setCurrentWalletSuggestion(null));
      emitMessage({ WALLET_SUGGESTION_OPENED: { currentWalletSuggestion: null } });
      return;
    }
    if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER) {
      store1.dispatch(setIsRechargingWallet(false));
      // NavigateViaCode(ComplOrderProgressTabBarConstant.CHECKOUT);
      store1.dispatch(setCreWalletSuggestionResponse(null));
      store1.dispatch(setCurrentWalletSuggestion(null));
      store1.dispatch(setCheckoutOrder(true));
      emitMessage({ WALLET_SUGGESTION_OPENED: { currentWalletSuggestion: null } });
      return;
    }
    if (HomeAction.getCodeForSelectedTab() === progressTabBarConstant.PAYMENT) {
      // CartAction.checkout(null);
    } else if (HomeAction.getCodeForSelectedTab() === progressTabBarConstant.ORDER) {
      // if(showUpsellingRecommProduct && !autoClickOnProceedBtn){
      //   const flag = await OfferAction.getUpsellingRecommProduct();
      //   if(flag){
      //     return;
      //   }
      // }
      dispatch(setShowRecomAnimation(false));
      let selectSuggested = false;
      const customerDetail = {
        contactNumber: customer.contact,
        date: new Date().toISOString().slice(0, 10),
      };
      store1.dispatch(OfferAction.getCustomerOffers(customerDetail));
      const isServiceChargeApplied=store1.getState().orderSlice.isServiceChargeApplied;
      CartAction.getFinalTransactionDetail(cartItems,isServiceChargeApplied);
      emitMessage({ TRANSACTION_SUMMARY: CartAction.getOrderInfo() });
      NavigateViaCode(progressTabBarConstant.OFFER);
      if (
        droolsData?.SELECT_SECTION?.length > 0 &&
        !subscriptionInCart &&
        !comboInCart &&
        !loyalTeaInCart
      ) {
        selectSuggested = true;
        store1.dispatch(
          setRightSideBar({
            open: true,
            code: Constants.RIGTHSIDE_BAR.CHAAYOS_SELECT_SUGGESTION,
            data: {
              callback: () => {
                if (loyalTeaInCart > 0 && !customer.otpVerified) {
                  handleLoyalTeaOtpVerification(progressTabBarConstant.ORDER);
                }
              },
            },
          })
        );
        store1.dispatch(setDroolsData({ ...droolsData, SELECT_SECTION: [] }));
        ProgressTabButtonsAction.addGoalToMap(
          Constants.GOAL_NAME.selectMembership,
          progressTabBarConstant.OFFER
        );
      } else if (!selectSuggestedInSession) {
        ProgressTabButtonsAction.removeGoalFromMap(Constants.GOAL_NAME.selectMembership);
      }
      setFinalPage(true);

      if (eligibleForFreeSecondChai) {
        ProgressTabButtonsAction.setGoalStatus(
          Constants.GOAL_NAME.secondFcRedeemed,
          loyalTeaInCart > 0
        );
        ProgressTabButtonsAction.setLoyalteaRedemptionGoal(
          loyalTeaInCart,
          eligibleForFreeSecondChai
        );
      }

      if (
        UtilityService.checkEmpty(appliedCoupon) &&
        ((!UtilityService.checkEmpty(customer.subscriptionInfoDetail) &&
          customer?.hasSubscription) ||
          subscriptionInCart) &&
        (UtilityService.checkEmpty(droolsData?.SELECT_SECTION) || subscriptionInCart) &&
        (!subscriptionInCart || cartItems.length > 1) &&
        !comboInCart &&
        !loyalTeaInCart
        // && (customer.otpVerified || !UtilityService.checkEmpty(customer.faceId))
      ) {
        if (
          customer?.hasSubscription &&
          !UtilityService.checkEmpty(customer.subscriptionInfoDetail.subscriptionCode) &&
          customer.subscriptionInfoDetail.subscriptionCode === Constants.COUPONS.CHAAYOS_SELECT
        ) {
          store1.dispatch(setAppliedCoupon(customer.subscriptionInfoDetail.subscriptionCode));
        } else if (
          UtilityService.checkEmpty(unitSubscriptionProducts) &&
          unitSubscriptionProducts[showSubscriptionCoupon?.productId]?.skuCode ===
          Constants.COUPONS.CHAAYOS_SELECT
        ) {
          store1.dispatch(
            setAppliedCoupon(unitSubscriptionProducts[showSubscriptionCoupon?.productId]?.skuCode)
          );
        }
      } else if (
        !UtilityService.checkEmpty(unitSubscriptionProducts[appliedCoupon]) &&
        comboInCart &&
        loyalTeaInCart === 0 &&
        !subscriptionInCart
      ) {
        store1.dispatch(setAppliedCoupon(''));
        store1.dispatch(
          OfferAction.clearCouponDetails((td) => {
            // if (!subscriptionInCart) {
            // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail: td, offerDescription: null } });
            // }
          }, !subscriptionInCart)
        );
      } else if (
        !UtilityService.checkEmpty(unitSubscriptionProducts[appliedCoupon]) &&
        loyalTeaInCart > 0
      ) {
        store1.dispatch(setAppliedCoupon(Constants.COUPONS.LOYALTEA));
      }
      if (!selectSuggested && loyalTeaInCart > 0 && !customer.otpVerified) {
        handleLoyalTeaOtpVerification(progressTabBarConstant.ORDER);
      }
    } else if (HomeAction.getCodeForSelectedTab() === progressTabBarConstant.OFFER) {
      if ((loyalTeaInCart > 0 || loyaltyBurnOfferDetails?.code === appliedCoupon) && !customer.otpVerified) {
        handleLoyalTeaOtpVerification(progressTabBarConstant.OFFER);
      } else {
        OfferAction.handleProceedOfferSection();
      }
    }
  }

  function handleOrderCancel() {
    dispatch(
      setRightSideBar({
        open: true,
        code: Constants.RIGTHSIDE_BAR.ORDER_CANCEL_CONFIRMATION,
        data: {
          message: 'Are you sure you want to cancel order?',
          callback: (val) => {
            if (val) {
              dispatch(clearCustomerSlice());
              NavigateViaCode(1, false);
              dispatch(setCurrentProcessTabs(progressTabBarData));
              dispatch(clearCartSlice());
              dispatch(OrderAction.setProductQuantityMap(0));
              dispatch(clearOfferSlice());
              dispatch(clearHomeSlice());
              dispatch(setIsWalletOrder(false));
              dispatch(setCustomerAvailedWalletOffer(false));
              dispatch(setOrderStartTimer(false));
              dispatch(setOrderTimemm(0));
              dispatch(setOrderTimess(0));
              dispatch(setRecommendedProductData(null));
              dispatch(setRecommendedProductDataIndex({}));
              navigate('/home');
              emitMessage({ WALLET_SUGGESTION_OPENED: { currentWalletSuggestion: null } });
              emitMessage({ ORDER_CANCELLED: {} });
            }
          },
        },
      })
    );
  }

  function handleOrderHold() {
    // TO do save the cart and then empty it.

    dispatch(updateCartItems([]));
    dispatch(setCustomerBasicInfo({}));
  }

  function handleClearCart() {
    if (CartAction.getCartQuantity() > 0) {
      emitMessage({ WALLET_SUGGESTION_OPENED: { currentWalletSuggestion: null } });
      dispatch(clearOfferSlice());
      dispatch(clearCartSlice());
      dispatch(OrderAction.setProductQuantityMap(0));
      dispatch(setIsCustomerInteracting(false));
      dispatch(setClaimedRecomProductData({}));
      dispatch(setIsWalletOrder(false));
      dispatch(setCustomerAvailedWalletOffer(false));
      NavigateViaCode(progressTabBarConstant.ORDER);
    }
  }

  useEffect(() => {
    if (autoClickOnProceedBtn) {
      handleProceed();
    }
    dispatch(updateAutoClickOnProceedBtn(false));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoClickOnProceedBtn]);

  function handleLoyalTeaOtpVerification(src) {
    emitMessage({ LOYALTY_REDEEMED: true });
    dispatch(setOtpStartTime(UtilityService.getCurrentTime()));

    dispatch(
      setOtpVerificationDetails({
        otpLoader: true,
        preOtpVerifiedData: null,
        reason: Constants.COUPONS.LOYALTY_FROM_COUPON,
        heading: 'Loyalty Redemption',
        callback: () => {
          if (src === progressTabBarConstant.OFFER) OfferAction.handleProceedOfferSection();
        },
      })
    );
  }

  const orderSummaryPage = checkOutOrder ||
                    (HomeAction.getCodeForSelectedTab() !== ComplOrderProgressTabBarConstant.SELECTION &&
                      HomeAction.getCodeForSelectedTab() !== progressTabBarConstant.ORDER &&
                      HomeAction.getCodeForSelectedTab() !== UnstatisfiedCustomerprogressTabBarConstant.ORDER &&
                      HomeAction.getCodeForSelectedTab() !== ComplOrderProgressTabBarConstant.ORDER &&
                      HomeAction.getCodeForSelectedTab() !== BookWastageprogressTabBarConstant.ORDER);

  return (
    <Stack
      display="flex"
      flexDirection="column"
      alignItems="space-between"
      sx={{
        flex: '4',
        height: '99%',
        maxWidth: '27%',
        p: 0.25,
        m: 1,
      }}
    >
      <Stack
        sx={{
          flex: 9,
          position: 'relative',
          height: '100%',
          width: 1,
          mb: 1,
          border: '1px solid',
          borderColor: theme.palette.grey[400],
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'space-between',
          pb: 1.5,
          background: theme.palette.neutral.white,
          borderRadius: '10px'
        }}
      >
        {isCustomerInteracting || otpLoader ? (
          <Box
            sx={{
              // backdropFilter: `blur(1.5px)`,
              // background: theme.palette.specialColors.BLUR_BLACK_SCREEN,
              position: 'absolute',
              height: '100%',
              width: '100%',
              zIndex: '1',
              borderRadius: '12px',
            }}
          />
        ) : (
          <Box />
        )}
        {HomeAction.getCodeForSelectedTab() === progressTabBarConstant.ORDER ||
          HomeAction.getCodeForSelectedTab() === UnstatisfiedCustomerprogressTabBarConstant.ORDER ||
          HomeAction.getCodeForSelectedTab() === BookWastageprogressTabBarConstant.ORDER ||
          HomeAction.getCodeForSelectedTab() === ComplOrderProgressTabBarConstant.ORDER ||
          HomeAction.getCodeForSelectedTab() === progressTabBarConstant.OFFER
          ? (
            <>
            { orderSummaryPage ? null : <CartHeader/>}
          <Stack sx={{ display: 'flex', px: 2, pt: 1 }}>
              <Box
                sx={{
                  color: theme.palette.leafGreen[500],
                  display: 'flex',
                  justifyContent: `${!UtilityService.checkEmpty(selectedBuzzer) ? 'space-between' : 'end'
                    }`,
                  width: '100%',
                  textAlign: 'end',
                  pb: 0.5,
                  ...(CartAction.getCartQuantity() === 0 && { color: theme.palette.leafGreen[200] }),
                  borderWidth: '0px 0px 1px 0px',
                  borderStyle: 'solid',
                  borderColor: theme.palette.grey[400],
                }}
              >
                {!UtilityService.checkEmpty(selectedBuzzer) ? (
                  <Typography
                    onClick={() => dispatch(setShowBuzzerScreen(true))}
                    variant="caption"
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      color: theme.palette.leafGreen[500],
                      width: 'fit-content',
                      cursor: 'pointer',
                      fontWeight: 700,
                      ...(CartAction.getCartQuantity() > 0 && { cursor: 'pointer' }),
                    }}
                  >
                    Token Number: &nbsp;{selectedBuzzer}&nbsp;
                    <StyledIcon
                      // onClick={() => dispatch(setShowBuzzerScreen(true))}
                      sx={{
                        cursor: 'pointer',
                        color: theme.palette.leafGreen[500],
                        '&:active': {
                          color: theme.palette.grey[400],
                        },
                      }}
                    >
                      {iconConfig.editIcon}
                    </StyledIcon>
                  </Typography>
                ) : (
                  <Box />
                )}
                <Typography
                  onClick={() => handleClearCart()}
                  variant="caption"
                  sx={{
                    width: 'fit-content',
                    cursor: 'no-drop',
                    fontWeight: 700,
                    ...(CartAction.getCartQuantity() > 0 && { cursor: 'pointer' }),
                  color: 'rgba(227, 77, 77, 1)'}}
                >
                  Clear Cart
                </Typography>
              </Box>
            </Stack></>
          ) : (
            <Box />
          )}
        <Stack
          sx={{ flexDirection: 'row', width: '100%', justifyContent: 'space-between', px: 2, py: 1 }}
        >
          <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center' }}>
            <StyledIcon sx={{ color: theme.palette.neutral.grey }}>{iconConfig.cart}</StyledIcon>
            &nbsp;CART
          </Typography>
          {/* <Typography variant='caption'>{loyalTeaInCart} </Typography> */}
          <Typography variant="caption">
            {CartAction.getCartQuantity()} ITEM{CartAction.getCartQuantity() !== 1 ? 'S' : ''}
          </Typography>
        </Stack>
        {checkOutOrder ||
          (HomeAction.getCodeForSelectedTab() !== ComplOrderProgressTabBarConstant.SELECTION &&
            HomeAction.getCodeForSelectedTab() !== progressTabBarConstant.ORDER &&
            HomeAction.getCodeForSelectedTab() !== UnstatisfiedCustomerprogressTabBarConstant.ORDER &&
            HomeAction.getCodeForSelectedTab() !== ComplOrderProgressTabBarConstant.ORDER &&
            HomeAction.getCodeForSelectedTab() !== BookWastageprogressTabBarConstant.ORDER) ? (
          <Box sx={{ position: 'relative', display: 'flex', flex: '1', width: '100%' }}>
            <FinalPage orderInfo={CartAction.getOrderInfo()} />
          </Box>
        ) : (
          <Box
            sx={{
              display: 'flex',
              position: 'relative',
              flex: 3,
              justifyContent: 'center',
              width: '100%',
              px: 2,
            }}
          >
            <Stack
              divider={<Divider sx={{ borderRadius: 1, height: '5px' }} />}
              sx={{
                display: 'flex',
                width: '97%',
                flex: 7,
                height: '97%',
                position: 'absolute',
                top: '0',
                bottom: '0',
                overflowY: 'auto',
              }}
            >
              {!UtilityService.checkEmpty(cartItems) ? (
                cartItems.map((cartItem, index) =>
                  // eslint-disable-next-line
                  cartItem.code !== Constants.TAX_CODE.COMBO ? (
                    CartAction.isEligibleToShowInCart(cartItem) ? (
                      <SelectedOrderItem productDetails={cartItem} key={index} itemIndex={index} />
                    ) : null
                  ) : (
                    <SelectedComboOrderItem productDetails={cartItem} key={index} itemIndex={index} />
                  )
                )
              ) : (
                <Box />
              )}
            </Stack>
          </Box>
        )}
        {!UtilityService.checkEmpty(offerUnlocked) && loyalTeaInCart === 0 && !comboInCart && UtilityService.checkEmpty(appliedCoupon) && !isRechargingWallet ? (
          <Stack
            sx={{
              overflow: 'hidden',
              width: '94%',
              display: 'flex',
              justifyContent: 'center',
              borderRadius: '12px',
              mx: '3%',
              my: 2,
            }}
          >
            <SelectedBackground
              sx={{
                position: 'relative',
                borderRadius: 1.5,
                width: '100%',
                minHeight: '11vh',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                color: theme.palette.leafGreen[500],
                px: '20px',
                py: 0.7,
                flexWrap: 'wrap',
                border: '2px dashed',
                borderColor: theme.palette.leafGreen[500],
              }}
            >
              <CutOut sx={{ backgroundColor: '#f6ecdd' }} />
              <CutOut sx={{ backgroundColor: '#eef3ee', left: '-20px' }} />
              {Math.round(offerUnlocked.minBillValue - cartAmount) <= 0 ? (
                <Stack
                  sx={{
                    flexDirection: 'row',
                    height: 'fit-content',
                    alignItems: 'center',
                  }}
                >
                  <Typography variant="caption">{`Congratulations ${customer?.name ? `${customer?.name}!` : ''} Freebie Unlocked add freebie - eligible product ${getFreebieProductName()}`}</Typography>
                  <Button
                    variant="contained"
                    onClick={() => {
                      dispatch(setFreebieCouponDialogue({ open: true, data: { readOnly: false } }));
                    }}
                    sx={{
                      minWidth: '75px',
                      ...(HomeAction.getCodeForSelectedTab() !== progressTabBarConstant.ORDER && {
                        visibility: 'hidden'
                      })
                    }}
                  >
                    + Add
                  </Button>
                </Stack>
              ) : (
                <Stack
                  sx={{
                    flexDirection: 'row',
                    height: 'fit-content',
                    alignItems: 'center',
                  }}
                >
                  <Typography>{`Add Items worth ₹${Math.round(
                    offerUnlocked.minBillValue - cartAmount
                  )} more to get ${offerUnlocked.text}`}</Typography>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      dispatch(setFreebieCouponDialogue({ open: true, data: { readOnly: true } }));
                    }}
                    sx={{
                      minWidth: '75px',
                    }}
                  >
                    View
                  </Button>
                </Stack>
              )}
            </SelectedBackground>
          </Stack>
        ) : null}
        {(HomeAction.getCodeForSelectedTab() === progressTabBarConstant.ORDER ||
          HomeAction.getCodeForSelectedTab() === UnstatisfiedCustomerprogressTabBarConstant.ORDER ||
          HomeAction.getCodeForSelectedTab() === BookWastageprogressTabBarConstant.ORDER) &&
          cartItems.length > 0 ? (
          <>
            <Stack
              sx={{
                flexDirection: 'row',
                width: '100%',
                justifyContent: 'space-between',
                alignItems: 'center',
                // color: theme.palette.neutral.grey,
                px: 2,
              }}
            >
              <Typography sx={{ display: 'flex', alignItems: 'center', ...typography.subtitle7 }}>
                Total{' '}
                <Typography variant="caption" sx={{ ml: 1 }}>
                  (inclusive of all taxes)
                </Typography>
              </Typography>
              <Typography sx={{ ...typography.subtitle6 }}>
                &#8377;{transactionDetail.paidAmount?.toFixed(2)}
              </Typography>
            </Stack>
            <Box sx={{ height: '1px', width: '94%', mx: 2, my: 1, bgcolor: '#D7D7D7' }} />
          </>
        ) : (
          <Box />
        )}

        {!checkOutOrder &&
          (HomeAction.getCodeForSelectedTab() === progressTabBarConstant.ORDER ||
            HomeAction.getCodeForSelectedTab() === UnstatisfiedCustomerprogressTabBarConstant.ORDER ||
            HomeAction.getCodeForSelectedTab() === BookWastageprogressTabBarConstant.ORDER ||
            HomeAction.getCodeForSelectedTab() === ComplOrderProgressTabBarConstant.ORDER ||
            HomeAction.getCodeForSelectedTab() === progressTabBarConstant.OFFER) ? (
          <Box sx={{ mx: 2 }}>
            <TextField
              autoComplete="off"
              onChange={(event) => {
                const val = event.target.value.trim();
                if (!UtilityService.checkEmpty(val)) {
                  dispatch(setOrderRemark(val));
                }
              }}
              placeholder="Remark"
              InputProps={{ style: { height: 40, textAlign: 'center' } }}
              sx={{
                mt: 0.5,
                background: theme.palette.neutral.white,
                width: '100%',
                borderRadius: 1,
              }}
            />
          </Box>
        ) : (
          <Box />
        )}
      </Stack>
      <Box sx={{ display: 'flex', width: '100%', px: !(HomeAction.getCodeForSelectedTab() === progressTabBarConstant.OFFER ||
            HomeAction.getCodeForSelectedTab() === progressTabBarConstant.PAYMENT) ? 0 :0, py: 1 ,}}>
        {(HomeAction.getCodeForSelectedTab() === progressTabBarConstant.OFFER ||
            HomeAction.getCodeForSelectedTab() === progressTabBarConstant.PAYMENT) && <Stack sx={{ flexDirection: 'row', flex: 1, width: '100%' }}>
          {/* <StyledIcon
            sx={{ cursor: 'pointer', height: 40, width: 40, mr: 1 }}
            onClick={() => handleOrderHold()}
          >
            {iconConfig.orderHold}
          </StyledIcon> */}
          {false ? (
            <StyledIcon
              sx={{ cursor: 'pointer', height: 40, width: 40, position: 'static', zIndex: '1' }}
              onClick={() => handleOrderCancel()}
            >
              {iconConfig.orderCancel}
            </StyledIcon>
          ) : (
            <Box />
          )}
          {!isRechargingWallet && (HomeAction.getCodeForSelectedTab() === progressTabBarConstant.OFFER ||
            HomeAction.getCodeForSelectedTab() === progressTabBarConstant.PAYMENT) ?
            <Button
              onClick={() => {
                dispatch(setPaymentType(null))
                if (comingFromRecharge === true) {
                  dispatch(setIsWalletOrder(false));
                  dispatch(setCustomerAvailedWalletOffer(false));
                  dispatch(setCreWalletSuggestionResponse(false));
                  dispatch(setComingFromRecharge(false));
                }
                dispatch(resetOtpVerificationDetails({}));
                goBack(HomeAction.getCodeForSelectedTab())
              }}
              sx={{
                flexDirection: 'row',
                flex: HomeAction.getCodeForSelectedTab() === progressTabBarConstant.PAYMENT ? 0.3 : 1,
                height: 50,
                fontSize: '20px',
                width: '100%',
                minWidth: 110,
                mr: 10,
                backgroundColor: "#E5F1D4",
                '&:hover': {
                  backgroundColor: "#E5F1D4"
                }

              }}
              variant="outlined"
            >
              <Stack direction="row" sx={{ alignItems: 'center', width: '100%' }}>
                <Typography variant="subtitle6">&#8592; Back</Typography>
              </Stack>
            </Button> : <Box />}

        </Stack>}
        {!checkOutOrder &&
          (HomeAction.getCodeForSelectedTab() === progressTabBarConstant.ORDER ||
            HomeAction.getCodeForSelectedTab() === UnstatisfiedCustomerprogressTabBarConstant.ORDER ||
            HomeAction.getCodeForSelectedTab() === BookWastageprogressTabBarConstant.ORDER ||
            HomeAction.getCodeForSelectedTab() === ComplOrderProgressTabBarConstant.ORDER ||
            HomeAction.getCodeForSelectedTab() === progressTabBarConstant.OFFER) ? (
          <Stack sx={{ flex: 1.5 }}>
            <Box sx={{ flexDirection: 'row', flex: 2, width: '100%' }}>
                {
                  orderSummaryPage ? null :
                    <Button
                      variant="remove"
                      sx={{
                        flex: 1,
                        height: 50,
                        width: '30%',
                        fontSize: '15px',
                        background: theme.palette.neutral.white,
                      }}
                      onClick={() => handleOrderCancel()}
                    >
                      X Cancel
                    </Button>
                }
              <Button
                onClick={() => handleProceed()}
                sx={{
                  height: 50,
                  border:1,
                  fontSize: '20px',
                  width: orderSummaryPage ? '100%' :'65%',
                  marginLeft: orderSummaryPage ? '1%' : '5%',
                  ...(CartAction.getCartQuantity() <= 0 && {
                    background: theme.palette.leafGreen[200],
                    color: `${theme.palette.neutral.white} !important`,
                  }),
                  // ...(loyalTeaInCart > 0 && {
                  //   background: theme.palette.clayRed[500],
                  //   '&:hover': {
                  //     backgroundColor: theme.palette.clayRed[500],
                  //   },
                  // })
                }}
                variant={CartAction.getCartQuantity() > 0 && !offerApplyResponsePending && !isCustomerInteracting ? 'contained' : 'disabled'}
              >
                <Stack direction="row" sx={{ alignItems: 'center' }}>
                  <Typography>Proceed</Typography>
                  <Box sx={{ width: 20 }} />
                  <StyledIcon>{iconConfig.doneTick}</StyledIcon>
                </Stack>
              </Button>
            </Box>
          </Stack>
        ) : (
          <Box />
        )}
      </Box>
    </Stack>
  );
}
