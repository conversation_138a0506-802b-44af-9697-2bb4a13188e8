import React from 'react';
import { <PERSON>, But<PERSON>, Stack, Typography, useTheme, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';
import PropTypes from 'prop-types';
import moment from 'moment';
import { StyledBackgroundGradient } from '../../layouts/baseStyles';
import { useSelector } from '../../redux/store';
import UtilityService from '../../services/UtilityService';
import Constants from '../../utils/Constants';
import OfferAction from './OffersAction';
import { setUnitSubscriptionProducts } from '../../components/order/OrderSlice';
import CartAction from '../cart/CartAction';
import { ProductService } from '../../services/ProductService';
import { isSubscriptionInCart } from '../cart/CartSlice';

CouponLayout.propTypes = {
  name: PropTypes.string,
  endDate: PropTypes.number,
  discount: PropTypes.number,
  description: PropTypes.string,
  setCouponCode: PropTypes.func,
  index: PropTypes.number,
  apply: PropTypes.func,
  remove: PropTypes.func,
};

export const CouponCutOut = styled(Box)(({ theme }) => ({
  position: 'absolute',
  width: '35px',
  height: '35px',
  borderRadius: '50%',
  border: '2px dashed',
  right: '-20px',
  top: 0,
  bottom: 0,
  margin: 'auto 0',
  bgcolor: '#faf8f5',
  borderColor: theme.palette.leafGreen[500],
}));

export default function CouponLayout({
  name,
  endDate,
  discount,
  description,
  setCouponCode,
  index,
  apply,
  remove,
}) {
  const theme = useTheme();
  const { appliedCoupon, couponError } = useSelector(
    (store) => store.offersSlice
  );
  const { transactionDetail } = useSelector((store) => store.cartSlice.cart);
  const { subscriptionInCart } = useSelector((store) => store.cartSlice);
  const customer = useSelector((store) => store.customerSlice.customerBasicInfo);
  const { chaayosCashbackData,loyaltyBurnOfferDetails } = useSelector((store) => store.customerSlice);
  const isEligibleLoyaltyProductPresent = OfferAction.isEligibleLoyaltyProductPresent();
  const chaayosSelectSavings = OfferAction.calculateSelectSavings();
  const {
    recomOfferData,
    claimedRecomProductData,
    unitSubscriptionProducts,
    productCustomization,
  } = useSelector((store) => store.orderSlice);

  function validTill(date) {
    const DDMMYY = new Date(date).toISOString().slice(0, 10);
    return DDMMYY;
  }
  return (
    <Stack
      sx={{
        display: 'flex',
        width: '98%',
        position: 'relative',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        mr: '2%',
      }}
    >
      <Box
        id="firstRowOuterBox"
        sx={{
          overflow: 'hidden',
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          borderRadius: '12px',
        }}
      >
        <Box
          id="firstRowInnerBox"
          sx={{
            position: 'relative',
            borderRadius: 1.5,
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            pl: 3.5,
            flexWrap: 'wrap',
            border: '2px dashed',
            borderColor: theme.palette.leafGreen[500],
            ...(appliedCoupon === name && {
              borderColor: theme.palette.clayRed[500],
              background: theme.palette.specialColors.ERROR_BACKGROUND,
            }),
            // boxShadow: `2px 3px 6px 0px ${theme.palette.leafGreen[500]}`,
            py: 3,
            mb: 2,
            ...(couponError.couponCode === name && { mb: 1 }),
            ...(!UtilityService.checkEmpty(unitSubscriptionProducts[name]) && {
              borderColor: theme.palette.leafGreen[500],
              backdropFilter: 'blur(11px)',
              background:
                'linear-gradient(197deg, rgb(229 241 212 / 50%) -5.44%, rgb(1 140 53 / 60%) 154.59%)',

              ...((unitSubscriptionProducts[name]?.productId ===
                Constants.PRODUCTID.CHAAYOS_PREPAID ||
                unitSubscriptionProducts[name]?.productId === Constants.PRODUCTID.IITBOMB) && {
                background: `linear-gradient(70deg, ${theme.palette.clayRed[500]} -246%,${theme.palette.mintGreen[500]} 77%)`,
                borderColor: alpha(theme.palette.clayRed[400], 0.5),
                backdropFilter: 'blur(11px)',
              }),
            }),

            ...(name === Constants.COUPONS.CHAAYOS_CASH && {
              borderColor: '#F7B2C1',
              bgcolor: '#F4E2E4',
              backdropFilter: 'blur(11px)',
            }),
          }}
        >
          <Stack sx={{ width: '100%', flexDirection: 'row' }}>
            <Stack id="couponFirstCol" justifyContent="center" sx={{ flex: 2, height: '100%' }}>
              <Typography variant="body4" sx={{ display: 'flex', width: '80%' }}>
                {description}
              </Typography>
              {name !== Constants.COUPONS.CHAAYOS_CASH ? (
                <>
                  {!UtilityService.checkEmpty(unitSubscriptionProducts[name]) &&
                  !customer.hasSubscription ? (
                    <>
                      {subscriptionInCart ? (
                        <Stack
                          sx={{
                            display: 'inline',
                          }}
                        >
                          <Typography variant="captionBold">
                            &#8377;&nbsp;
                            {CartAction.getProductPrice(unitSubscriptionProducts[name].productId)}
                          </Typography>
                          <Typography variant="body3">
                            &nbsp;for&nbsp;
                            {
                              productCustomization[unitSubscriptionProducts[name].productId].prices[
                                ProductService.getDefaultDimensionById(
                                  unitSubscriptionProducts[name].productId
                                )
                              ].name
                            }
                          </Typography>
                        </Stack>
                      ) : (
                        <Box />
                      )}

                      <Typography
                        variant="body4"
                        sx={{
                          color: theme.palette.neutral.black,
                        }}
                      >
                        {unitSubscriptionProducts[name].name.toUpperCase()}
                      </Typography>
                    </>
                  ) : (
                    <Typography variant="caption">{name}</Typography>
                  )}
                </>
              ) : (
                <>
                  <Typography
                    sx={{ display: 'flex', justifyContent: 'start', alignItems: 'center' }}
                  >
                    <Typography variant="h7" sx={{ color: theme.palette.leafGreen[500] }}>
                      {' '}
                      &#8377;&nbsp;{chaayosCashbackData?.totalCashback}&nbsp;&nbsp;{' '}
                    </Typography>{' '}
                    Available
                  </Typography>
                  {chaayosCashbackData?.latestExpiringCashback > 0 ? (
                    <Typography variant="h8" sx={{ display: 'flex' }}>
                      &#8377;&nbsp;{chaayosCashbackData?.latestExpiringCashback} Expires on{' '}
                      {moment(chaayosCashbackData?.latestExpiringDate).format('ll')}
                    </Typography>
                  ) : (
                    <Box />
                  )}
                </>
              )}
            </Stack>
            <Stack id="couponSecondCol" justifyContent="center" sx={{ flex: 2 }}>
              {!UtilityService.checkEmpty(endDate) ? (
                <Stack justifyContent="center" sx={{ flex: 2 }}>
                  <Typography variant="captionBold">Valid Till</Typography>
                  <Typography variant="body2">{validTill(endDate)}</Typography>
                </Stack>
              ) : (
                <Box />
              )}
              {name === Constants.COUPONS.CHAAYOS_SELECT ? (
                <Stack justifyContent="center" sx={{ flex: 2 }}>
                  <Typography variant="captionBold">Potential Savings</Typography>
                  <Typography variant="body2">
                    &#8377;&nbsp;
                    {chaayosSelectSavings}
                    {/* {appliedCoupon === Constants.COUPONS.CHAAYOS_SELECT
                  ? transactionDetail.discountDetail.totalDiscount
                  : Math.round(0.15 * transactionDetail.totalAmount)} */}
                  </Typography>
                </Stack>
              ) : (
                <Box />
              )}
              {name === Constants.COUPONS.CHAAYOS_CASH ? (
                <Stack justifyContent="center" sx={{ flex: 2 }}>
                  &nbsp;
                </Stack>
              ) : (
                <Box />
              )}
              {name === Constants.COUPONS.RECOMMENDATION_OFFER ? (
                <Stack justifyContent="center" sx={{ flex: 2 }}>
                  <Typography variant="captionBold">Potential Savings</Typography>
                  <Typography variant="body2">
                    &#8377;&nbsp;
                    {recomOfferData?.discountType === Constants.COMBO_DISCOUNT_TYPE.PERCENTAGE ? (
                      Math.min(
                        claimedRecomProductData.productPrice,
                        UtilityService.getToDecimalValue(
                          (recomOfferData.discountValue * claimedRecomProductData.productPrice) /
                            100,
                          2
                        )
                      )
                    ) : (
                      <>
                        {appliedCoupon === Constants.COUPONS.RECOMMENDATION_OFFER
                          ? transactionDetail.discountDetail.totalDiscount
                          : recomOfferData?.discountValue}
                      </>
                    )}
                  </Typography>
                </Stack>
              ) : (
                <Box />
              )}
              {name === Constants.COUPONS.LOYALTEA ? (
                <Stack justifyContent="center" sx={{ flex: 2 }}>
                  <Typography variant="captionBold">Potential Savings</Typography>
                  <Typography variant="body2">
                  &#8377;&nbsp;
                  {appliedCoupon === Constants.COUPONS.LOYALTEA
                      ? transactionDetail.discountDetail.totalDiscount
                      : (isEligibleLoyaltyProductPresent || 0)}
                  </Typography>
                </Stack>
              ) : (
                <Box />
              )}
              {name === loyaltyBurnOfferDetails?.code ? (
                <Stack justifyContent="center" sx={{ flex: 2 }}>
                  <Typography variant="captionBold">Potential Savings</Typography>
                  <Typography variant="body2">
                    &#8377;&nbsp;
                    {appliedCoupon === loyaltyBurnOfferDetails?.code
                      ? transactionDetail.discountDetail.totalDiscount
                      : loyaltyBurnOfferDetails?.offer?.offerValue}
                  </Typography>
                </Stack>
              ) : (
                <Box />
              )}
            </Stack>
            <Stack
              id="couponThirdCol"
              sx={{ flex: 1.5, width: '50%', justifyContent: 'center', alignItems: 'center' }}
            >
              {appliedCoupon !== name ? (
                <Button
                  onClick={() => apply(name, index)}
                  variant="outlined"
                  sx={{ background: theme.palette.mintGreen[500] }}
                >
                  {!UtilityService.checkEmpty(unitSubscriptionProducts[name]) &&
                  !customer?.hasSubscription
                    ? 'Buy & Apply'
                    : 'Apply'}
                </Button>
              ) : (
                <Button onClick={() => remove()} variant="remove">
                  Remove
                </Button>
              )}
            </Stack>
          </Stack>
          {/* bonus loyalty points */}
          <Stack id="bottomLineDiscription">
            {name === loyaltyBurnOfferDetails?.code || name === Constants.COUPONS.LOYALTEA? (
              <Stack flexDirection="row" sx={{ alignItems: 'baseline' }}>
                <Typography
                  variant="body4"
                  sx={{ display: 'inline', color: theme.palette.clayRed[500] }}
                >
                  {(customer?.loyalityPoints ?? 0) + (customer?.bonusLoyaltyPoints ?? 0)} LoyalTea points&nbsp;
                </Typography>
                <Typography variant="body3"> Available</Typography>
              </Stack>
            ) : (
              <Box />
            )}
          </Stack>
          <CouponCutOut
            sx={{
              left: '-20px',
              bgcolor: '#eaf2ed',
              ...(appliedCoupon === name && {
                borderColor: theme.palette.clayRed[500],
              }),
              ...(!UtilityService.checkEmpty(unitSubscriptionProducts[name]) &&
                appliedCoupon === name && {
                  borderColor: theme.palette.leafGreen[500],
                }),
              ...((unitSubscriptionProducts[name]?.productId ===
                Constants.PRODUCTID.CHAAYOS_PREPAID ||
                unitSubscriptionProducts[name]?.productId === Constants.PRODUCTID.IITBOMB) && {
                borderColor: alpha(theme.palette.clayRed[400], 0.5),
              }),
              ...(name === Constants.COUPONS.CHAAYOS_CASH && {
                borderColor: '#F7B2C1',
              }),
            }}
          />
          <CouponCutOut
            sx={{
              bgcolor: '#faf8f5',
              ...(appliedCoupon === name && {
                borderColor: theme.palette.clayRed[500],
              }),
              ...(!UtilityService.checkEmpty(unitSubscriptionProducts[name]) &&
                appliedCoupon === name && {
                  borderColor: theme.palette.leafGreen[500],
                }),
              ...(name === Constants.COUPONS.CHAAYOS_CASH && {
                borderColor: '#F7B2C1',
              }),
              ...((unitSubscriptionProducts[name]?.productId ===
                Constants.PRODUCTID.CHAAYOS_PREPAID ||
                unitSubscriptionProducts[name]?.productId === Constants.PRODUCTID.IITBOMB) && {
                borderColor: alpha(theme.palette.clayRed[400], 0.5),
              }),
            }}
          />
        </Box>
      </Box>
      <Box id="secondRow" sx={{ mb: 2 }}>
        {couponError.couponCode === name ? (
          <Typography variant="caption" sx={{ color: theme.palette.clayRed[500] }}>
            {couponError.errorMessage}
          </Typography>
        ) : (
          <Box />
        )}
      </Box>
    </Stack>
  );
}
