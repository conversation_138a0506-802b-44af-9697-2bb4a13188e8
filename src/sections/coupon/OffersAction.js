// eslint-disable-next-line import/no-cycle
import { emitMessage } from '../../cafeAppCommunication/cafeAppCommunication';
import { setOtpStartTime, setOtpVerificationDetails } from '../../components/customerInfo/CustomerSlice';
import { setCartRulesData, setCcFlatCoupon, setClaimedRecomProductData, setCurrentWalletSuggestion, setRecomOfferData } from '../../components/order/OrderSlice';
import { store } from '../../redux/store';
import RestService from '../../services/RestService';
import UtilityService from '../../services/UtilityService';
import APIConstants from '../../utils/APIConstants';
import Constants from '../../utils/Constants';
import OrderDataModel from '../../utils/OrderDataModel';
// eslint-disable-next-line import/no-cycle
import CartAction from '../cart/CartAction';
import { isSubscriptionInCart, setCashRedeemed, updateCartItems } from '../cart/CartSlice';
import {
  setAllLoyaltyBurnCouponDetails,
  setAppliedCoupon,
  setAvailableCoupons,
  setAwardLoyalty,
  setByPassLoyalteaAward,
  setCouponError,
  setMax3MonthsSavings,
  setOfferApplyResponsePending,
  setShowSubscriptionCoupon,
  setShowUpsellingRecommProduct,
  setSubscriptionOffersDetails,
  setUnitAvailableCoupons,
  setUnlockOfferAppliedBy,
} from './OffersSlice';

import CustomerAction from '../../components/customerInfo/customerAction';
// eslint-disable-next-line import/no-cycle
import PaymentActions from '../../components/payment/PaymentActions';
import {
  setCreWalletSuggestionResponse,
  setIsRechargingWallet,
  setPaymentStartTime,
} from '../../components/payment/PaymentSlice';
import ProgressTabButtonsAction from '../../components/progressTabButtons/ProgressTabButtonsAction';
import { NavigateViaCode } from '../../components/progressTabButtons/ProgressTabNavigation';
import {
  progressTabBarConstant
} from '../../pages/home/<USER>';
import { WalletSuggestionAction } from '../cart/WalletSuggestionAction';
// eslint-disable-next-line import/no-cycle
import HomeAction from '../../pages/home/<USER>';
import { pushMessageToClevertap } from '../../services/ClevertapService';
import CommunicationService from '../../cafeAppCommunication/communicationService';
import { recommendationService } from '../../services/RecommendationService';

export default class OfferAction {
  static getCustomerOffers = (data) => (dispatch) => {
    RestService.getJSON(APIConstants.getEndpoints().offers.getCustomerOffers, data)
      .then((response) => {
        if (!UtilityService.checkEmpty(response)) {
          dispatch(OfferAction.searchForAutoApplicableCoupon(response.data.list));
          dispatch(setAvailableCoupons(response.data.list));
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static searchForAutoApplicableCoupon = (data) => (dispatch) => {
      data?.forEach((offer)=>{
        if(Constants.CCFLAT === offer.code.substring(0,6))
        {
          dispatch(setCcFlatCoupon({
            value : true,
            couponCode : offer.code
          }));
        }      
      })
  };

  static getUnitOffers = (data) => (dispatch) => {
    RestService.postJSON(APIConstants.getEndpoints().offers.getUnitOffers, data)
      .then((response) => {
        if (!UtilityService.checkEmpty(response) && Array.isArray(response)) {
          dispatch(setUnitAvailableCoupons(response));
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static getRecommendationOffersData = (customerId) => (dispatch) =>{
    const {recommendationOfferAvailable}=store.getState().metadataSlice.allCafeAppProperties;
    if(UtilityService.checkEmpty(recommendationOfferAvailable) || !recommendationOfferAvailable){
      return;
    }
    const url = RestService.constructURL(
      APIConstants.getEndpoints().offers.recommendationOfferData,
      {},
      { customerId }
    );

    RestService.getJSON(url)
    .then((response) => {
      if (!UtilityService.checkEmpty(response?.data)) {
      //   const data = {
      //     "resultAPC": 500,
      //     "discountType": "PERCENTAGE",
      //     "discountValue": 40,
      //     "offerTag": "FLAT 40% OFF",
      //   "productIds": [10,80,130,151,992,20,1201,660,1109, 460, 1028]
      // }
        dispatch(setRecomOfferData(response.data));
      }
    })
    .catch((error) => {
      console.log(error);
    });
  }

  static getSubscriptionOffers = () => (dispatch) => {
    RestService.getJSON(APIConstants.getEndpoints().offers.subscriptionOfferData)
    .then((response) => {
      if (!UtilityService.checkEmpty(response?.data?.body)) {
        const subscriptionProducts = {};
        Object.values(response.data.body).forEach((product) => {
          if(product.subType === Constants.SUBSCRIPTION_INFO.subType)
          {
            subscriptionProducts[product?.id] = { ...product, productId:product?.id };   
            subscriptionProducts[product?.skuCode] = { ...product, productId:product?.id };     
          }
        });
        dispatch(setSubscriptionOffersDetails(subscriptionProducts))
      }
    })
    .catch((error) => {
      console.log(error)
    })
  }

  // static savings = () => dispatch =>{
  //   const { transactionDetail } = store.getState().cartSlice.cart;

  //   const offerManagementObj = {
  //     unitId:UtilityService.getUnitId(),
  //     totalAmount:transactionDetail.totalAmount,
  //     paidAmount:transactionDetail.paidAmount,
  //     productId:[Constants.PRODUCTID.CHAAYOS_SELECT],

  //   }
  //   RestService.postJSON(APIConstants.getEndpoints().offers.checkMembership, offerManagementObj)
  //   .then((response) =>{
  //     if (!UtilityService.checkEmpty(response) && !UtilityService.checkEmpty(response.applicableDiscounts[Constants.PRODUCTID.CHAAYOS_SELECT])) {
  //       console.log("d")
  //     }
  //   })
  //   .catch((error) => {
  //     console.log(error);
  //   });
  // }

  static loyaltyApplyCallBack = (newEligibleTeas, freeLoyaltyTea, loyalTeaInCart, addDelay) => (dispatch) => {
    let count = 1;
    dispatch(
      OfferAction.clearCouponDetails((transactionDetail) => {
        // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
      })
    );
    Object.keys(newEligibleTeas).forEach((itemKey) => {
      if (
        newEligibleTeas[itemKey].quantity <= count &&
        newEligibleTeas[itemKey].quantity <= freeLoyaltyTea
      ) {
        const cartItem = JSON.parse(
          JSON.stringify({ ...newEligibleTeas[itemKey], hasBeenRedeemed: true,isRecomOfferApplied:false ,loyaltyBurnPoints: 0,loyaltyBurned: false,})
        );
        cartItem.discountDetail = {
          discount: {
            percentage: 0,
            value: 0,
          },
          discountCode: null,
          discountReason: Constants.COUPONS.LOYALTEA,
          promotionalOffer: cartItem.price,
          totalDiscount: 0,
        };
        CartAction.updateCartItems(
          cartItem,
          newEligibleTeas[itemKey].index,
          JSON.parse(JSON.stringify(itemKey))
        );
        count -= newEligibleTeas[itemKey].quantity;
      }
      // loyalTeaInCart = store.getState().cartSlice.cart.loyalTeaInCart;
      if (count !== 0) {
        dispatch(
          OfferAction.clearCouponDetails((transactionDetail) => {
            // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
          })
        );
        dispatch(
          setCouponError({
            couponCode: Constants.COUPONS.LOYALTEA,
            errorMessage: "Eligble Tea's qty are more in Cart OR No Eligible Item Present in Cart",
          })
        );
        emitMessage({COUPON_ERROR:{ couponCode: Constants.COUPONS.LOYALTEA,
          errorMessage: "Eligble Tea's qty are more in Cart OR No Eligible Item Present in Cart" }})
      } else {
        const delayVal = addDelay ? 3000 : 0;
        const gifPlay = setTimeout(() => {
          emitMessage({ TRANSACTION_SUMMARY: CartAction.getOrderInfo() });
          const transactionDetail = store.getState().cartSlice.cart.transactionDetail;
          emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription:Constants.COUPONS.LOYALTEA }})
        }, delayVal);
      
        dispatch(
          setCouponError({
            couponCode: '',
            errorMessage: '',
          })
        );
        emitMessage({COUPON_ERROR:{ couponCode: '', errorMessage:  '' }})
      }
    });
  };

  static isEligibleLoyaltyProductPresent = () => {
    const currentCartItems = store.getState().cartSlice.cart.cartItems;
    const newEligibleTeas = CartAction.eligibleLoyalTeas(currentCartItems);
    const freeLoyaltyTea = store.getState().customerSlice.freeLoyaltyTea;
    let priceCheck = -1;
    Object.values(newEligibleTeas).forEach((item)=>
    {
      if(item.quantity === 1 && priceCheck < 0)
      {
        priceCheck = item.price;
        
      }
    })
    
    if (
      !UtilityService.checkEmpty(newEligibleTeas) &&
      priceCheck &&
      freeLoyaltyTea >0
    ) {
      return priceCheck;
    } 
    return -1;
  };

  // static addSelectInCart = (callBack) => {
  //   const { productBasicDetail, productCustomization } = store.getState().orderSlice;
  //   const productId = Constants.PRODUCTID.CHAAYOS_SELECT;
  //   const productDetail = productBasicDetail.products[productId];
  //   const customization = productCustomization[productId];
  //   const selectedDimension = ProductService.getDefaultDimension(customization);
  //   CartAction.addItemToCart(
  //     CartAction.getProcessedItem({
  //       productData: { productDetail, customization },
  //       isCustomisable: false,
  //       isRecommProduct: false,
  //       selectedDimension,
  //     }),
  //     false,
  //     false,
  //     callBack
  //   );
  // };

  static handleApply = (val) => (dispatch) => {
    if (UtilityService.checkEmpty(val)) return;
    const tranDetail = store.getState().cartSlice.cart.transactionDetail;

    let data = {};
    const customer = store.getState().customerSlice.customerBasicInfo;
  const showSelect = store.getState().offersSlice.showChaayosSelect;
    const { subscriptionInCart, comboInCart } = store.getState().cartSlice;
    const freeLoyaltyTea = store.getState().customerSlice.freeLoyaltyTea;
    const loyalTeaInCart = store.getState().cartSlice.cart.loyalTeaInCart;
    const { unitSubscriptionProducts } = store.getState().orderSlice
    const { loyaltyBurnOfferDetails,eligibleForFreeSecondChai } = store.getState().customerSlice;

    // eslint-disable-next-line prefer-const
    let currentCartItems = store.getState().cartSlice.cart.cartItems;
    // if(tranDetail?.discountDetail?.discountReason === Constants.COUPONS.CHAAYOS_SELECT){
    //   dispatch(OfferAction.clearCouponDetails((td) => {
    //     emitMessage({ COUPON_APPLIED: { transactionDetail : td, offerDescription: null } });
    //   }))
    // }

    // const isChaayosSelectMember =  store.getState().customerSlice.chaayosSelectMember;
    const { productBasicDetail, productCustomization } = store.getState().orderSlice;
    if (comboInCart) {
      if (!UtilityService.checkEmpty(unitSubscriptionProducts[val]) && subscriptionInCart) {
        CartAction.addSubscriptionProductInCart(unitSubscriptionProducts[val],null,() => {
          const tDetail = store.getState().cartSlice.cart.transactionDetail;
          emitMessage({ COUPON_APPLIED: { transactionDetail: tDetail } });
          dispatch(setAppliedCoupon(val));
        });
      }
      dispatch(
        UtilityService.showSnackBar({
          open: true,
          snackType: Constants.SNACK_TYPE.ERROR,
          message: "Can't apply offer with combos in cart!",
        })
      );
      return;
    }
    if (Constants.COUPONS.LOYALTEA === val && loyalTeaInCart === 0) {
      const newEligibleTeas = CartAction.eligibleLoyalTeas(currentCartItems);

      if (UtilityService.checkEmpty(newEligibleTeas)) {
        dispatch(
          OfferAction.clearCouponDetails((transactionDetail) => {
            // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
          })
        );
        dispatch(
          setCouponError({
            couponCode: Constants.COUPONS.LOYALTEA,
            errorMessage: "Eligble Tea's qty are more in Cart OR No Eligible Item Present in Cart",
          })
        );
        emitMessage({COUPON_ERROR:{ couponCode: Constants.COUPONS.LOYALTEA,
          errorMessage: "Eligble Tea's qty are more in Cart OR No Eligible Item Present in Cart", }})
        return;
      }

      if (!customer.otpVerified) {
        emitMessage({ LOYALTY_REDEEMED: true });
        dispatch(setOtpStartTime(UtilityService.getCurrentTime()))

        dispatch(
          setOtpVerificationDetails({
            otpLoader: true,
            preOtpVerifiedData: null,
            reason: Constants.COUPONS.LOYALTY_FROM_COUPON,
            heading: 'Loyalty Redemption',
            callback: () => {
              dispatch(this.loyaltyApplyCallBack(newEligibleTeas, freeLoyaltyTea, loyalTeaInCart,true));
            },
          })
        );
      } else {
        dispatch(this.loyaltyApplyCallBack(newEligibleTeas, freeLoyaltyTea, loyalTeaInCart, false));
      }
    } 
    // else if (Constants.COUPONS.CHAAYOS_SELECT === val && selectIncart) {
    //   dispatch(
    //     OfferAction.clearCouponDetails((transactionDetail) => {
          
    //     }),
    //     false
    //   );
    //   dispatch(addSelectIncart(true));
    //   dispatch(setShowChaayosSelect(true));
    //   currentCartItems = store.getState().cartSlice.cart.cartItems;
    //   const productId = Constants.PRODUCTID.CHAAYOS_SELECT;
    //   const productDetail = productBasicDetail.products[productId];
    //   const customization = productCustomization[productId];
    //   const selectedDimension = ProductService.getDefaultDimension(customization);
    //   const updatedCart = [];
    //   const { taxData } = store.getState().metadataSlice;

    //   Object.keys(currentCartItems).forEach((key) => {
    //     const discountDetail = {
    //       discount: { percentage: 15, value: UtilityService.getToDecimalValue(0.15 * currentCartItems[key].totalAmount,2) },
    //       discountCode: Constants.COUPON_DISCOUNT_CODE.CHAAYOS_SELECT,
    //       discountReason: Constants.COUPONS.CHAAYOS_SELECT,
    //       promotionalOffer: 0,
    //       totalDiscount: UtilityService.getToDecimalValue(0.15 * currentCartItems[key].totalAmount,2),
    //     };
    //     const amount =
    //       currentCartItems[key].totalAmount -
    //       discountDetail.discount.value -
    //       discountDetail.promotionalOffer;
    //     const taxes = CartAction.getTotalTax(
    //       currentCartItems[key].totalAmount,
    //       amount,
    //       taxData[currentCartItems[key].code]
    //     );
     

    //     if(currentCartItems[key].productId === Constants.getGoonjId())
    //     {
    //       updatedCart.push(currentCartItems[key]);
    //     }
    //     else if (currentCartItems[key].productId !== Constants.PRODUCTID.CHAAYOS_SELECT ) {
    //       updatedCart.push({
    //         ...currentCartItems[key],
    //         discountDetail,
    //         amount,
    //         taxes: taxes.data,
    //         tax: taxes.value,
    //       });
    //     }
    //   });
    //   CartAction.getFinalTransactionDetail(updatedCart);
    //   dispatch(updateCartItems(updatedCart));
    //   if (selectIncart) {
    //     this.addSelectInCart(() => {
    //       const tDetail = store.getState().cartSlice.cart.transactionDetail;
    //       emitMessage({ COUPON_APPLIED: { transactionDetail: tDetail } });
    //       dispatch(setAppliedCoupon(Constants.COUPONS.CHAAYOS_SELECT));
    //     });
    //   }
    // }
     else if(Constants.COUPONS.RECOMMENDATION_OFFER === val) {
        dispatch(
          OfferAction.clearCouponDetails((transactionDetail) => {
            // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
          },false)
        )
        dispatch(OfferAction.recommCouponApply());
    }
    else {
      let skipSubscriptionValidation = false;
      if(subscriptionInCart && !UtilityService.checkEmpty(unitSubscriptionProducts[val]))
      {
        skipSubscriptionValidation = true;
      }
      let signupOffer;
      if(val === loyaltyBurnOfferDetails?.code)
      {
        signupOffer = eligibleForFreeSecondChai;
      }
      data = {
        newCustomer: customer.newCustomer,
        couponCode: val,
        signupOffer,
        skipSubscriptionValidation
      };
      dispatch(
        setCouponError({
          couponCode: '',
          errorMessage: '',
        })
      );
      emitMessage({COUPON_ERROR:{ couponCode: '', errorMessage:  '' }})
      if (Constants.COUPONS.LOYALTEA !== val) {
        dispatch(
          OfferAction.applyCoupon(data, (info, offerDescription) => {
            const {otpVerified} = store.getState().customerSlice.customerBasicInfo;
            if(val !== loyaltyBurnOfferDetails?.code || otpVerified ){
              emitMessage({ COUPON_APPLIED: { transactionDetail: info, offerDescription } });
            }
          })
        );
      }
    }
  };

  static applyUnlockOffer=(data)=>(dispatch)=>{
    const {offerUnlocked} = store.getState().progressTabButtonsSlice;
    if(HomeAction.getCodeForSelectedTab() === progressTabBarConstant.PAYMENT){
      NavigateViaCode(progressTabBarConstant.OFFER)
    }
    if(data?.data?.remove){
      dispatch(setUnlockOfferAppliedBy("CUSTOMER"))
      dispatch(OfferAction.clearCouponDetails((transactionDetail) => {
        dispatch(setUnlockOfferAppliedBy('CRE'))
        // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
      }))
    }else{
      dispatch(setUnlockOfferAppliedBy("CUSTOMER"))
      dispatch(
        OfferAction.applyCoupon(data?.couponObj, (info, offerDescription) => {
          emitMessage({ COUPON_APPLIED: { transactionDetail: info, offerDescription } });
        })
      )
    }
  }

  static applyCoupon = (data, callback) => (dispatch) => {
  
  const {subscriptionInCart} = store.getState().cartSlice
  const { unitSubscriptionProducts } = store.getState().orderSlice;
  const { showSubscriptionCoupon, appliedCoupon, subscriptionOffersDetails, unlockOfferAppliedBy } = store.getState().offersSlice;
  const { offerUnlocked } = store.getState().progressTabButtonsSlice;
    dispatch(
      OfferAction.clearCouponDetails((transactionDetail) => {
        // Ankit emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } });
      },false)
    );
    const cartItems = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
    const { transactionDetail } = store.getState().cartSlice.cart;
    const customerId = store.getState().customerSlice.customerBasicInfo.id;
    const customerOtpRequired = store.getState().customerSlice.customerBasicInfo.otpVerified;
    const { loyaltyBurnOfferDetails } = store.getState().customerSlice;
    let cartItemsWithoutGoonj = JSON.parse(JSON.stringify(cartItems));
    const isServiceChargeApplicable=store.getState().orderSlice.isServiceChargeApplied;
    let goonjProduct = null;
    let subscriptionProduct = null
    if(cartItems.length > 0 && (!subscriptionInCart || !UtilityService.checkEmpty(unitSubscriptionProducts[data.couponCode]) || (!UtilityService.checkEmpty(appliedCoupon) || !UtilityService.checkEmpty(unitSubscriptionProducts[appliedCoupon])) ))
    {
      cartItemsWithoutGoonj = cartItemsWithoutGoonj.filter((cartItem)=>{
        const goonjId = Constants.getGoonjId();
        if(cartItem.productId === goonjId)
        {
          goonjProduct  = cartItem ;
          return false;
        }
        if(!UtilityService.checkEmpty(unitSubscriptionProducts[cartItem.productId]))
        {
          subscriptionProduct = cartItem;
          return false;
        }
        return true;
      })
      const ordersObj = JSON.parse(JSON.stringify(cartItemsWithoutGoonj));
      ordersObj.forEach((cartItem, idx) => {
        const options = [];

        cartItem?.composition?.options.forEach((paidAddon) => {
          if (!UtilityService.checkEmpty(paidAddon?.name)) {
            options.push(paidAddon?.name);
          }
        });
        cartItem.composition.options = options;
      });

      const order = {
        ...OrderDataModel.orderInfo,
        customerId,
        orders: ordersObj,
        transactionDetail,
        unitId: UtilityService.getUnitId(),
      };
      let url = APIConstants.getEndpoints().offers.applyCoupon;
      if (!UtilityService.checkEmpty(subscriptionOffersDetails[data.couponCode])
      || data.couponCode === Constants.COUPONS.CHAAYOS_CASH) {  url = APIConstants.getEndpoints().offers.applySubscription;
      }
      dispatch(setOfferApplyResponsePending(true))
      RestService.postJSON(url, { order, ...data })
        .then((response) => {
          dispatch(setOfferApplyResponsePending(false));
          if (!UtilityService.checkEmpty(response)) {
            if (!response.error) {
              if(offerUnlocked.offerCode === data.couponCode){
                pushMessageToClevertap(Constants.clevertapEventName.unlockOfferAvailed,
                  {offerCampaignId:offerUnlocked.offerCampaignId || offerUnlocked.campaignId, offerCode: offerUnlocked.offerCode , offerText:offerUnlocked.text, addedBy: unlockOfferAppliedBy})
                  if(unlockOfferAppliedBy === "CUSTOMER"){
                    dispatch(setUnlockOfferAppliedBy('CRE'))
                    dispatch(UtilityService.showSnackBar({open:true, snackType:Constants.SNACK_TYPE.SUCCESS, message:"Customer has availed unlock offer"}))
                  }
              }
              if (response.otpRequired && !customerOtpRequired && !data?.skipSubscriptionValidation && HomeAction.getCodeForSelectedTab() === progressTabBarConstant.OFFER) {
                dispatch(
                  setOtpVerificationDetails({
                    otpLoader: true,
                    preOtpVerifiedData: { data, callback },
                    reason: Constants.COUPONS.APPLY_COUPON,
                    heading: !UtilityService.checkEmpty(unitSubscriptionProducts[data.couponCode]) ? `${unitSubscriptionProducts[data.couponCode].name}` :'Coupon Redemption',
                  ...( data.couponCode === Constants.COUPONS.CHAAYOS_CASH && {heading: 'Chaayos Cash', } )
                  })
                );
                dispatch(setOfferApplyResponsePending(false));
                emitMessage({ COUPON_OTP_VERIFICATION: true });
                dispatch(setOtpStartTime(UtilityService.getCurrentTime()))
                if(loyaltyBurnOfferDetails?.code !== data.couponCode){
                  return;
                }
              }
              dispatch(setByPassLoyalteaAward(response?.order?.bypassLoyateaAward))
              // dispatch(setShowSubscriptionCoupon({status: false,productId: null, dimension:null}));
              const updatedCartItems = [];
              // eslint-disable-next-line array-callback-return
              ordersObj.map((cartItem, index) => {
                if (UtilityService.checkEmpty(unitSubscriptionProducts[cartItem.productId])) {
                  const amount =
                    cartItem.amount -
                    response.order.orders[index].discountDetail.discount.value -
                    response.order.orders[index].discountDetail.promotionalOffer;
                  const { taxData } = store.getState().metadataSlice;
                  const taxes = CartAction.getTotalTax(
                    cartItem.totalAmount,
                    amount,
                    taxData[cartItem.code]
                  );
                  let itemKey = cartItemsWithoutGoonj[index]?.itemKey;
                  if(response?.order?.orders[index]?.loyaltyBurned ){
                    if(!cartItemsWithoutGoonj[index]?.itemKey?.includes("LoyaltyBurn")){
                      itemKey = `${cartItemsWithoutGoonj[index]?.itemKey  }LoyaltyBurn`;
                    }
                  }
                  else{
                      itemKey = cartItemsWithoutGoonj[index]?.itemKey?.replace("LoyaltyBurn","")
                      if(itemKey?.split("_").length > 1)
                      {
                        itemKey = cartItem.itemKey.replace("_LoyaltyBurn","")
                      }
                      else{
                        itemKey = cartItem.itemKey.replace("LoyaltyBurn","")
                      }
                  }

                  updatedCartItems.push({
                    ...cartItemsWithoutGoonj[index],
                    loyaltyBurnPoints: response?.order?.orders[index]?.loyaltyBurnPoints,
                    loyaltyBurned: response?.order?.orders[index]?.loyaltyBurned,
                    amount,
                    taxes: taxes.data,
                    tax: taxes.value,
                    discountDetail: response.order.orders[index].discountDetail,
                    itemKey
                  });
                }
              });
              if(!UtilityService.checkEmpty(goonjProduct)){
                updatedCartItems.push(goonjProduct);
              }
              // if(!UtilityService.checkEmpty(subscriptionProduct)){
              //   updatedCartItems.push(subscriptionProduct);
              //   dispatch(isSubscriptionInCart(true))
              //   dispatch(setShowSubscriptionCoupon({status:true,productId:subscriptionProduct?.productId,dimension:subscriptionProduct?.dimension}))

              // }
              dispatch(updateCartItems(updatedCartItems));
              const recomOfferClaimedData =store.getState().orderSlice.claimedRecomProductData;
              const { loyalTeaInCart,productsQty } = store.getState().cartSlice.cart;
              emitMessage({
                POS_ADDED_ITEM: {
                  cartItems: updatedCartItems,
                  loyalTeaInCart,
                  productsQtyMap: productsQty,
                  lastUpdatedCartItem:null,
                  recomOfferClaimedData
                },
              });
              // const tDetail = CartAction.clearLoyalTea(); remove below transaction line when uncommenting this
              dispatch(setAwardLoyalty(response.awardLoyalty))
              if(Constants.COUPONS.CHAAYOS_CASH === response.couponCode || 
                Constants.COUPONS.CASHBACK === response.couponCode){
              dispatch(setAppliedCoupon(Constants.COUPONS.CHAAYOS_CASH));
              dispatch(setCashRedeemed(response.order.cashRedeemed))
            }else{
              dispatch(setAppliedCoupon(response.couponCode));
            }
              const tDetail=CartAction.getFinalTransactionDetail(updatedCartItems,isServiceChargeApplicable);

              if(subscriptionInCart && !UtilityService.checkEmpty(unitSubscriptionProducts[response.couponCode]))
              {
                CartAction.addSubscriptionProductInCart(unitSubscriptionProducts[response.couponCode],showSubscriptionCoupon?.dimension,()=>{
                  dispatch(isSubscriptionInCart(true))
                  dispatch(setShowSubscriptionCoupon({status:true,productId:showSubscriptionCoupon?.productId,dimension:showSubscriptionCoupon?.dimension}))
                  const currentCart = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
                  const tranDetail = CartAction.getFinalTransactionDetail(currentCart,isServiceChargeApplicable);
                  callback(tranDetail, response.offerDescription);
                });
              }
              else if (callback !== undefined) {
                callback(tDetail, response.offerDescription);
              }
            } else {
              // CartAction.clearLoyalTea();
              dispatch(
                setCouponError({ couponCode: data.couponCode, errorMessage:  response.errorMessage })
              );
              if(HomeAction.getCodeForSelectedTab() !== progressTabBarConstant.OFFER){
                dispatch(UtilityService.showSnackBar({ open: true, message:  response.errorMessage}));
              }
              console.log(response.errorMessage);      
              const cartWithoutCoupon = store.getState().cartSlice.cart.cartItems;
              emitMessage({ COUPON_APPLIED: { transactionDetail:CartAction.getFinalTransactionDetail(cartWithoutCoupon,isServiceChargeApplicable), offerDescription: null } });      
              emitMessage({COUPON_ERROR:{ couponCode: data.couponCode, errorMessage:  response.errorMessage }})
              if(subscriptionInCart && !UtilityService.checkEmpty(unitSubscriptionProducts[data.couponCode]))
              {
                CartAction.addSubscriptionProductInCart(unitSubscriptionProducts[data.couponCode],showSubscriptionCoupon?.dimension,()=>{
                  dispatch(isSubscriptionInCart(true))
                  dispatch(setShowSubscriptionCoupon({status:true,productId:showSubscriptionCoupon?.productId,dimension:showSubscriptionCoupon?.dimension}))
                  const currentCart = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
                  const tranDetail = CartAction.getFinalTransactionDetail(currentCart,isServiceChargeApplicable);
                  callback(tranDetail);
                });
              }
              else{
                const currentCartItems = store.getState().cartSlice.cart.cartItems;
                callback(CartAction.getFinalTransactionDetail(currentCartItems,isServiceChargeApplicable));
              }
            }
          }
        })
        .catch((error) => {
          
          if(subscriptionInCart && !UtilityService.checkEmpty(unitSubscriptionProducts[data.couponCode]))
          {
            CartAction.addSubscriptionProductInCart(unitSubscriptionProducts[data.couponCode],showSubscriptionCoupon?.dimension,()=>{
              dispatch(isSubscriptionInCart(true))
              dispatch(setShowSubscriptionCoupon({status:true,productId:showSubscriptionCoupon?.productId,dimension:showSubscriptionCoupon?.dimension}))
              const currentCart = JSON.parse(JSON.stringify(store.getState().cartSlice.cart.cartItems));
              const tranDetail = CartAction.getFinalTransactionDetail(currentCart,isServiceChargeApplicable);
              callback(tranDetail);
            });
          }
          dispatch(setOfferApplyResponsePending(false));
          console.log(error);
        });
    }
    else{
      dispatch(
        setCouponError({ couponCode: data.couponCode, errorMessage: "Subscription In Cart, Please remove it to apply any another coupon" })
      );
      emitMessage({COUPON_ERROR:{ couponCode: data.couponCode, errorMessage: "Subscription In Cart, Please remove it to apply any another coupon" }})
      callback(transactionDetail);
    }
  };


  static clearCouponDetails = (callback, isEmitMessage=true) => (dispatch) => {
    const { appliedCoupon,unlockOfferAppliedBy } = store.getState().offersSlice;
    const { cartItems } = store.getState().cartSlice.cart;
    const updatedCartItems = [];
    const { unitSubscriptionProducts } = store.getState().orderSlice
    const customer = store.getState().customerSlice.customerBasicInfo;
    const { taxData } = store.getState().metadataSlice;    
    const {offerUnlocked} = store.getState().progressTabButtonsSlice;
    let subscriptionProductId = Constants.PRODUCTID.CHAAYOS_SELECT;
    const isServiceChargeApplicable=store.getState().orderSlice.isServiceChargeApplied;
    const prevAppliedCoupon=appliedCoupon;
    dispatch(setCashRedeemed(0))
    cartItems.forEach((cartItem) => {
      const taxes = CartAction.getTotalTax(
        cartItem.totalAmount,
        cartItem.totalAmount,
        taxData[cartItem.code]
      );

      if (UtilityService.checkEmpty(unitSubscriptionProducts[cartItem.productId]) || (appliedCoupon !== unitSubscriptionProducts[cartItem.productId]?.skuCode)) {
        subscriptionProductId = cartItem.productId
        let itemKey = cartItem.itemKey;
        if(cartItem?.hasBeenRedeemed)
        {
          itemKey = cartItem.itemKey.replace("LoyalTea","")
          if(itemKey?.split("_").length > 1)
          {
            itemKey = cartItem.itemKey.replace("_LoyalTea","")
          }
          else{
            itemKey = cartItem.itemKey.replace("LoyalTea","")
          }
        }
        
        updatedCartItems.push({
          ...cartItem,
          isRecomOfferApplied: false,
          amount: cartItem.totalAmount,
          hasBeenRedeemed: false,
          loyaltyBurnPoints: 0,
          loyaltyBurned: false,
          complimentaryDetail : {
            reasonCode: null,
            isComplimentary: false,
            reason: null,
          },
          discountDetail: {
            discount: { percentage: 0, value: 0 },
            discountCode: null,
            discountReason: null,
            promotionalOffer: 0,
            totalDiscount: 0,
          },
          taxes: taxes.data,
          tax: taxes.value,
          itemKey,
        });
      }
      else{
        dispatch(isSubscriptionInCart(false))
      }
    });
    dispatch(setAppliedCoupon(''));
    dispatch(
      setCouponError({
        couponCode: '',
        errorMessage: '',
      })
    );
    emitMessage({COUPON_ERROR:{ couponCode: '', errorMessage:  '' }})
    if(!UtilityService.checkEmpty(unitSubscriptionProducts[subscriptionProductId])){
      CartAction.updateProductQty(subscriptionProductId,-1);
    }
    if (!UtilityService.checkEmpty(updatedCartItems) && updatedCartItems.length > 0) {
      if(isEmitMessage){
      const recomOfferClaimedData =store.getState().orderSlice.claimedRecomProductData;
      const { productsQty } = store.getState().cartSlice.cart;

        emitMessage({
          POS_ADDED_ITEM: {
            cartItems: updatedCartItems,
            loyalTeaInCart: 0,
            productsQtyMap: productsQty,
            lastUpdatedCartItem:null,
            recomOfferClaimedData
          },
        });
      }

    }
    if (
      UtilityService.checkEmpty(customer) ||
      !customer?.hasSubscription ||
      customer?.subscriptionInfoDetail?.daysLeft <= 0
    ) {
      dispatch(setShowSubscriptionCoupon({status: false,productId: null, dimension:null}));

    }

    dispatch(updateCartItems(updatedCartItems));
    dispatch(setByPassLoyalteaAward(false));
    if(prevAppliedCoupon === offerUnlocked.offerCode){
      if(unlockOfferAppliedBy === 'CUSTOMER'){
        dispatch(UtilityService.showSnackBar({open:true, snackType:Constants.SNACK_TYPE.ERROR, message:"Customer has removed unlock offer"}))
      }
      pushMessageToClevertap(Constants.clevertapEventName.unlockOfferRemoved,{offerCampaignId:offerUnlocked.offerCampaignId || offerUnlocked.campaignId, offerCode: offerUnlocked.offerCode , offerText:offerUnlocked.text, removedBy: unlockOfferAppliedBy})
    }
    // Ankit if (callback !== undefined) {
      if(isEmitMessage){
        emitMessage({ COUPON_APPLIED: { transactionDetail:CartAction.getFinalTransactionDetail(updatedCartItems,isServiceChargeApplicable), offerDescription: null } });
      }
    // callback(CartAction.getFinalTransactionDetail(updatedCartItems));
    // }
  };

  static calculateSelectSavings(){
    const currentCartItems = store.getState().cartSlice.cart.cartItems;
    let savings  = 0 ;
    currentCartItems.forEach((cartItem=>{
      if(cartItem.productId !== Constants.getGoonjId() && cartItem.productId !== Constants.PRODUCTID.CHAAYOS_SELECT)
      {
            savings += (0.15*cartItem.totalAmount)
      }
    }))
    return UtilityService.getToDecimalValue(savings,2);
  }

  static calculateSavings3Months = () => (dispatch) => {
    const { transactionDetail } = store.getState().cartSlice.cart;
    const { appliedCoupon } = store.getState().offersSlice;
    const { customerFrequency, last90DaysSale } =
      store.getState().customerSlice.customerVisitDetail;
    let savings = Math.round(0.15 * transactionDetail.totalAmount);
    let savings3months;
    const maxRange = 500;
    const minRange = 400;
    const randomNumber = Math.ceil(Math.random() * (maxRange - minRange) + minRange);
    const { totalAmount } = transactionDetail;
    if (appliedCoupon === Constants.COUPONS.CHAAYOS_SELECT) {
      savings = transactionDetail.discountDetail.totalDiscount;
    }
    if (UtilityService.checkEmpty(customerFrequency)) {
      savings3months = randomNumber;
      dispatch(setMax3MonthsSavings(savings3months.toFixed(2)));
      return savings3months.toFixed(2);
    }
    const currentOrderSavings3month = savings * customerFrequency;
    const currentOrderSales = totalAmount * customerFrequency;
    const last90DaysSalesSavings = (currentOrderSavings3month * last90DaysSale) / currentOrderSales;

    if (currentOrderSavings3month < 400 && last90DaysSalesSavings < 400) {
      savings3months = randomNumber;
    } else {
      savings3months = Math.max(currentOrderSavings3month, last90DaysSalesSavings + savings);
    }

    dispatch(setMax3MonthsSavings(savings3months.toFixed(2)));
    return savings3months.toFixed(2);
  };

  static recommCouponApply = () => dispatch =>{
        let updatedCartItems = [];
        let offerApplied = false;
        const currentCartItems = store.getState().cartSlice.cart.cartItems;
        const { recomOfferData, claimedRecomProductData } = store.getState().orderSlice;
        const { taxData } = store.getState().metadataSlice;
        const isServiceChargeApplicable=store.getState().orderSlice.isServiceChargeApplied;

        currentCartItems.forEach((cartItem,key) => {
          if(claimedRecomProductData?.itemKey === cartItem?.itemKey){
            let discountDetail = {
              discount: { percentage: 0, value: 0 },
              discountCode: null,
              discountReason: null,
              promotionalOffer: 0,
              totalDiscount: 0,
            }
            let promotionalOffer = 0;
            if(recomOfferData?.discountType === Constants.COMBO_DISCOUNT_TYPE.FIXED)
            {
              promotionalOffer = Math.min(recomOfferData?.discountValue,cartItem.price);
            }
            else if(recomOfferData?.discountType === Constants.COMBO_DISCOUNT_TYPE.PERCENTAGE){
              promotionalOffer = Math.min(cartItem?.price,UtilityService.getToDecimalValue((recomOfferData.discountValue*cartItem.price)/100,2));
            }
            discountDetail =  {
            discount:{ percentage: 0, value: 0 },
            discountCode: null,
            discountReason: Constants.COUPONS.RECOMMENDATION_OFFER,
            promotionalOffer,
            totalDiscount: 0,
          }
          const amount =
            currentCartItems[key].totalAmount -
            discountDetail.discount.value -
            discountDetail.promotionalOffer;
          const taxes = CartAction.getTotalTax(
                        currentCartItems[key].totalAmount,
                        amount,
                        taxData[currentCartItems[key].code]
        );
          offerApplied = true;
          updatedCartItems.push({
                                ...cartItem,
                                discountDetail,
                                amount,
                                taxes: taxes.data,
                                tax: taxes.value,
                                isRecomOfferApplied:true
                              })
        }else{
          updatedCartItems.push(cartItem);
        }
      })

      if(!offerApplied)
      {
        updatedCartItems = [];
        currentCartItems.forEach((cartItem,key) => {
            if(claimedRecomProductData?.productId === cartItem?.productId){
              let discountDetail = {
                discount: { percentage: 0, value: 0 },
                discountCode: null,
                discountReason: null,
                promotionalOffer: 0,
                totalDiscount: 0,
              }
              let promotionalOffer = 0;
              if(recomOfferData?.discountType === Constants.COMBO_DISCOUNT_TYPE.FIXED)
              {
                promotionalOffer = Math.min(recomOfferData?.discountValue,cartItem.price);;
              }
              else if(recomOfferData?.discountType === Constants.COMBO_DISCOUNT_TYPE.PERCENTAGE){
                promotionalOffer = Math.min(cartItem.price,UtilityService.getToDecimalValue((recomOfferData.discountValue*cartItem.price)/100,2));
              }
              discountDetail =  {
              discount:{ percentage: 0, value: 0 },
              discountCode: null,
              discountReason: Constants.COUPONS.RECOMMENDATION_OFFER,
              promotionalOffer,
              totalDiscount: 0,
            }
            const amount =
            currentCartItems[key].totalAmount -
            discountDetail.discount.value -
            discountDetail.promotionalOffer;
            const taxes = CartAction.getTotalTax(
                          currentCartItems[key].totalAmount,
                          amount,
                          taxData[currentCartItems[key].code])
            offerApplied = true;
            updatedCartItems.push({
                                  ...cartItem,
                                  discountDetail,
                                  amount,
                                  taxes: taxes.data,
                                  tax: taxes.value,
                                  isRecomOfferApplied:true
                                })
            dispatch(setClaimedRecomProductData({
              productId:cartItem.productId,
              itemKey:cartItem.itemKey,
              productPrice:  cartItem.price
            }))

          }else{
            updatedCartItems.push(cartItem);
          }
        })

      }

    if(offerApplied)
    {
        const { productsQty } = store.getState().cartSlice.cart;
        const recomOfferClaimedData =store.getState().orderSlice.claimedRecomProductData;

        dispatch(updateCartItems(updatedCartItems));
        emitMessage({
          POS_ADDED_ITEM: {
            cartItems: updatedCartItems,
            loyalTeaInCart: 0,
            productsQtyMap: productsQty,
            lastUpdatedCartItem:null,
            recomOfferClaimedData
          }})
        dispatch(setAppliedCoupon(Constants.COUPONS.RECOMMENDATION_OFFER));
        dispatch(
          setCouponError({
            couponCode: '',
            errorMessage: '',
          })
        );
        emitMessage({COUPON_ERROR:{ couponCode: '', errorMessage:  '' }})
        const tDetail = CartAction.getFinalTransactionDetail(updatedCartItems,isServiceChargeApplicable);
        emitMessage({ COUPON_APPLIED: { transactionDetail: tDetail ,offerDescription:Constants.COUPONS.RECOMMENDATION_OFFER}})
    }
    else{
      dispatch(
        setCouponError({
              couponCode: Constants.COUPONS.RECOMMENDATION_OFFER,
              errorMessage: "Something Went Wrong!."
            })
      );
      emitMessage({COUPON_ERROR:{ couponCode: Constants.COUPONS.RECOMMENDATION_OFFER,
        errorMessage: "Something Went Wrong!." }})
      
      const { transactionDetail } = store.getState().cartSlice.cart;
      emitMessage({ COUPON_APPLIED: { transactionDetail, offerDescription: null } }); 
    }
    console.log("recommendationOffer applied");
  }

  static handleProceedOfferSection = () => {
    const dispatch = store.dispatch;
    const transactionDetail = store.getState().cartSlice.cart.transactionDetail;
    const isWalletOrder = store.getState().orderSlice.isWalletOrder;
    const customerAvailedWalletOffer = store.getState().paymentSlice.customerAvailedWalletOffer;
    emitMessage({ POS_PROCEED_FOR_PAYMENT: '' });
    dispatch(setIsRechargingWallet(false));
    if (CustomerAction.isCustomerEligibleForSuggestion(Constants.SUGGESTION_TYPE.WALLET)) {
      dispatch(
        WalletSuggestionAction.loadWalletSuggestion(transactionDetail.paidAmount, (data) => {
          NavigateViaCode(progressTabBarConstant.PAYMENT, false);
          if (data.isShowWalletSuggestion) {
            // dispatch(PaymentActions.openDrawerForWalletSuggestion());
            ProgressTabButtonsAction.addGoalToMap(
              Constants.GOAL_NAME.walletSuggestion,
              progressTabBarConstant.PAYMENT
            );
          } else {
            dispatch(setCreWalletSuggestionResponse(null));
            dispatch(setCurrentWalletSuggestion(null));
            emitMessage({ WALLET_SUGGESTION_OPENED: { currentWalletSuggestion: null } });
          }
        })
      );
    } else {
      NavigateViaCode(progressTabBarConstant.PAYMENT, false);
      if (!isWalletOrder && !customerAvailedWalletOffer) {
        dispatch(setCreWalletSuggestionResponse(null));
        dispatch(setCurrentWalletSuggestion(null));
        emitMessage({ WALLET_SUGGESTION_OPENED: { currentWalletSuggestion: null } });
        ProgressTabButtonsAction.removeGoalFromMap(Constants.GOAL_NAME.walletSuggestion);
      }
    }
    dispatch(setPaymentStartTime(UtilityService.getCurrentTime()));
    // Goal tracking
    ProgressTabButtonsAction.keyResetter(['walletsSuggested']);
    ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.walletSuggestion, false, true);
  }

    static getLoyaltyBurnOffers = () => dispatch => {
    const {customerBasicInfo} = store.getState().customerSlice;
    RestService.postJSON(APIConstants.getEndpoints().offers.loyaltyBurnOffers, [Constants.RIGTHSIDE_BAR.LOYALTY_OFFER],{unitId:UtilityService.getUnitId()})
    .then((response) => {
      if (!UtilityService.checkEmpty(response) && !UtilityService.checkEmpty(response[Constants.RIGTHSIDE_BAR.LOYALTY_OFFER])) {
        const loyaltyBurnOfferDetails = response[Constants.RIGTHSIDE_BAR.LOYALTY_OFFER];
        const allLoyaltyBurnOfferDetails = {}
        loyaltyBurnOfferDetails?.forEach((data)=>{
          // const data = JSON.parse(JSON.stringify(loyaltyBurnOfferDetails));
          data.eligibleProducts = {};
          data?.offer?.couponMappingList?.forEach((eligibleItem)=>{
            if(eligibleItem?.type === Constants.ITEM_TYPE.PRODUCT && eligibleItem?.status === Constants.ACTIVE){
              data.eligibleProducts[eligibleItem?.value] = eligibleItem;
            }
          })
          data.eligibleProductCategories = {};
          data?.offer?.metaDataMappings?.forEach((eligibleItem)=>{
            if(eligibleItem?.name === Constants.ITEM_TYPE.PRODUCT_CATEGORY && eligibleItem?.status === Constants.ACTIVE){
              data.eligibleProductCategories[eligibleItem?.code] = eligibleItem;
            }
          })
          allLoyaltyBurnOfferDetails[data?.code] = data;
        });
        dispatch(setAllLoyaltyBurnCouponDetails(allLoyaltyBurnOfferDetails));
        // CustomerAction.sendLoyaltyBurnOfferDetailsToCrm();
        // CustomerAction.getCustomerLoyaltyBurnOfferDetails(customerBasicInfo);

      }
    })
    .catch((error) => {
      console.log(error);
    });
  }

  static getUpsellingRecommProduct = async (dispatch) =>{
    const {customerBasicInfo} = store.getState().customerSlice;
    const { unitDetail } = store.getState().metadataSlice.unitDetail;
    const { transactionDetail, cartItems } = store.getState().cartSlice.cart;
    const { unitMenuDetails  } = store.getState().orderSlice;
    let orderType = null;
    const daySlot = unitMenuDetails?.id?.token?.split("_")[2];
    let bev_count = 0;
    let food_count = 0;
    let Bakery_count = 0;
    cartItems?.forEach((item)=>{
      if (item.productType === 7) {
        food_count += 1;
      } else if (item.productType === 6 || item.productType === 5) {
          bev_count += 1;
      } else if (item.productType === 10) {
          Bakery_count += 1;
      }
    })

    if (food_count > 0 && bev_count > 0) {
        orderType = "FOOD_BEV"
    } else if (Bakery_count > 0 && bev_count > 0) {
        orderType = "BAKERY_BEV"
    } else if (food_count > 0) {
        orderType = "FOOD_ONLY"
    } else if (bev_count > 0) {
        orderType = "BEV_ONLY"
    }
    let customerId = customerBasicInfo?.id;
    let custType;
    if (!UtilityService.checkEmpty(customerId)) {
      custType = !customerBasicInfo.newCustomer ? "EXISTING" : "NEW";
      if (customerBasicInfo.newCustomer && (Constants.getEnv() === 'DEV' || Constants.getEnv() === 'STAGE')) {
          customerId = 999999999;
      }
  } else {
      customerId = 5;
      custType = 'UNREGISTERED';
  }
    const reqObj = {
        customerId,
        dayPart: daySlot,
        unitId: unitDetail?.unitId,
        unitCategory: unitDetail?.subCategory,
        unitRegion: unitDetail?.region,
        custType,
        cart_value: transactionDetail?.paidAmount,
        applicationVersion: "V1",
        order_type:orderType,
    }
    let flag = false;
    await RestService.postJSON(APIConstants.getEndpoints().offers.upsellingRecommProducts,reqObj)
    .then(async (response)=>{
      if(!UtilityService.checkEmpty(response)){
        flag = await CommunicationService.showUpsellingRecommendation(response?.recommendedProducts)
        if(flag){
          emitMessage({POS_UPSELLING_RECOMENDATION:{allRecommendationsList:response?.recommendedProducts}});
        }
        }
    }).catch((error)=>{
      store.dispatch(setShowUpsellingRecommProduct(false));
      console.log(error);
    })

    return flag;
  } 

    static fetchCartRulesRecommendations = (params) => (dispatch) => {
      const { customerId, dayPart, unitId, unitCategory, unitRegion, custType } = params;
      
      const url = RestService.constructURL(
        APIConstants.getEndpoints().offers.getCartRulesRecommendations,
        {},
        {}
      );
      console.log("cartRecomm:::",customerId);
      RestService.postJSON(url, {
        customerId,
        dayPart,
        unitId,
        unitCategory,
        unitRegion,
        custType
      })
        .then((response) => {
          if (
            !UtilityService.checkEmpty(response)
          ) {
            dispatch(setCartRulesData(response));
            // Reinitialize recommendation service with new data
            recommendationService.reinitialize(response);
          }
        })
        .catch((error) => {
          console.error('Error fetching cart rules recommendations:', error);
          dispatch(
            UtilityService.showSnackBar({
              open: true,
              snackType: Constants.SNACK_TYPE.ERROR,
              message: 'Error fetching recommendations',
              autoHideDuration: 2000,
            })
          );
        });
    };
}
