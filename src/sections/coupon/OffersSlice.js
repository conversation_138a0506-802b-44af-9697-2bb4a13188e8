import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  availableCoupons: [],
  unitAvailableCoupons: [],
  appliedCoupon: '',
  couponError: {
    couponCode: '',
    errorMessage: '',
  },
  showChaayosSelect: false,
  showSubscriptionCoupon: {
    status: false,
    productId: null,
  },
  max3MonthsSavings: null,
  subscriptionOffersDetails: {},
  awardLoyalty: true,
  bypassLoyateaAward: false,
  isUnlockOfferTaken: false,
  unlockOfferAppliedBy: 'CRE',
  allLoyaltyBurnCouponDetails:{},
  offerApplyResponsePending:false,
  showUpsellingRecommProduct:true,
  newRecomData: null,
};

const slice = createSlice({
  name: 'offers',
  initialState,
  reducers: {
    clearOfferSlice(state) {
      state.availableCoupons = [];
      state.appliedCoupon = '';
      state.couponError = {
        couponCode: '',
        errorMessage: '',
      };
      state.showChaayosSelect = false;
      state.max3MonthsSavings = null;
      state.showSubscriptionCoupon = {
        status: false,
        productId: null,
      };
      state.isUnlockOfferTaken = false;
      state.offerApplyResponsePending = false;
      state.showUpsellingRecommProduct = true;
    },
    setAvailableCoupons(state, action) {
      state.availableCoupons = action.payload;
    },
    setUnitAvailableCoupons(state, action) {
      state.unitAvailableCoupons = action.payload;
    },
    setAppliedCoupon(state, action) {
      state.appliedCoupon = action.payload;
    },
    setCouponError(state, action) {
      state.couponError = action.payload;
    },
    setShowSubscriptionCoupon(state, action) {
      state.showSubscriptionCoupon = action.payload;
    },
    setMax3MonthsSavings(state, action) {
      state.max3MonthsSavings = action.payload;
    },
    setSubscriptionOffersDetails(state, action) {
      state.subscriptionOffersDetails = action.payload;
    },
    setAwardLoyalty(state, action) {
      state.awardLoyalty = action.payload;
    },
    setByPassLoyalteaAward(state, action) {
      state.bypassLoyateaAward = action.payload;
    },
    setIsUnlockOfferTaken(state, action) {
      state.isUnlockOfferTaken = action.payload;
    },
    setUnlockOfferAppliedBy(state, action) {
      state.unlockOfferAppliedBy = action.payload;
    },
    setAllLoyaltyBurnCouponDetails(state,action){
      state.allLoyaltyBurnCouponDetails = action.payload;
    },
    setOfferApplyResponsePending(state, action){
      state.offerApplyResponsePending = action.payload
    },
    setShowUpsellingRecommProduct(state, action){
      state.showUpsellingRecommProduct = action.payload;
    },
    setNewRecmData(state, action) {
      state.newRecomData = action.payload
    }
  },
});
export const {
  setAvailableCoupons,
  setUnitAvailableCoupons,
  setAppliedCoupon,
  clearOfferSlice,
  setCouponError,
  setShowSubscriptionCoupon,
  setMax3MonthsSavings,
  setSubscriptionOffersDetails,
  setAwardLoyalty,
  setByPassLoyalteaAward,
  setIsUnlockOfferTaken,
  setUnlockOfferAppliedBy,
  setAllLoyaltyBurnCouponDetails,
  setOfferApplyResponsePending,
  setShowUpsellingRecommProduct,
  setNewRecmData
} = slice.actions;

export default slice.reducer;
