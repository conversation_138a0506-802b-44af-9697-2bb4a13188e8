package com.stpl.tech.kettle.converter;

import com.stpl.tech.kettle.data.kettle.OrderMetricDetailData;
import com.stpl.tech.kettle.data.kettle.WebOrderMetricDetailData;
import com.stpl.tech.kettle.domain.model.OrderMetricDomain;
import com.stpl.tech.kettle.domain.model.WebOrderMetricDomain;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class OrderMetricConverter {
    public static OrderMetricDetailData convertToOrderMetricDetailData(OrderMetricDomain orderMetricDomain) {
        OrderMetricDetailData data = new OrderMetricDetailData();
        if(Objects.nonNull(orderMetricDomain)) {
            if(Objects.nonNull(orderMetricDomain.getOrderMetricData())) {
                getOrderBasicData(data, orderMetricDomain);
            }
            if(Objects.nonNull(orderMetricDomain.getPaymentMetricData())){
                getPaymentData(data,orderMetricDomain);
            }
            if(Objects.nonNull(orderMetricDomain.getCustomizationData())){
                getCustomizationData(data,orderMetricDomain);
            }
            if(Objects.nonNull(orderMetricDomain.getWalletData())){
                getWalletData(data,orderMetricDomain);
            }
            if(Objects.nonNull(orderMetricDomain.getSecondFreeChaiData())){
                data.setSecondFreeChaiRedeemed(AppUtils.getYOrN(orderMetricDomain.getSecondFreeChaiData().isRedeemed()));
                data.setSecondFreeChaiAvailable(AppUtils.getYOrN(orderMetricDomain.getSecondFreeChaiData().isAvailable()));
            }
            if(Objects.nonNull(orderMetricDomain.getMembershipData())){
                getMembershipData(data,orderMetricDomain);
            }
            if(Objects.nonNull(orderMetricDomain.getOfferData())){
                data.setOfferAvailable(AppUtils.getYOrN(orderMetricDomain.getOfferData().isAvailable()));
                data.setOfferAvailed(AppUtils.getYOrN(orderMetricDomain.getOfferData().isRedeemed()));
            }
            data.setCustomerId(orderMetricDomain.getCustomerId());
            if (Objects.nonNull(orderMetricDomain.getPosVersion())) {
                data.setPosVersion(orderMetricDomain.getPosVersion());
            }
            if (Objects.nonNull(orderMetricDomain.getCafeAppVersion())) {
                data.setCafeAppVersion(orderMetricDomain.getCafeAppVersion());
            }
            if (Objects.nonNull(orderMetricDomain.getOtpDeliveredTime())) {
                data.setOtpDeliveredTime(orderMetricDomain.getOtpDeliveredTime());
            }
            data.setValidCustomer(AppUtils.getYOrN(orderMetricDomain.isValidCustomer()));
            if (Objects.nonNull(orderMetricDomain.getCustomerOrderNumber())) {
                data.setCustomerOrderNumber(orderMetricDomain.getCustomerOrderNumber());
            }
            Boolean loyaltyRedeemed = Objects.nonNull(orderMetricDomain.getRedeemedLoyaltyPoints()) && orderMetricDomain.getRedeemedLoyaltyPoints() > 0;
            if(Objects.isNull(orderMetricDomain.getRedeemedLoyaltyPoints())){
                orderMetricDomain.setRedeemedLoyaltyPoints(Integer.valueOf(0));
            }
            data.setLoyaltyRedeemed(AppUtils.getYOrN(loyaltyRedeemed));
            data.setLoyaltyRedemptionDisplayed(AppUtils.getYOrN(orderMetricDomain.isLoyaltyOfferRedemptionShown()));
            data.setAwardedLoyaltyPoints(orderMetricDomain.getAwardedLoyaltyPoints());
            data.setRedeemedLoyaltyPoints(orderMetricDomain.getRedeemedLoyaltyPoints());
            data.setSavedChaiAdded(AppUtils.getYOrN(orderMetricDomain.isSavedChaiAdded()));
            if (Objects.nonNull(orderMetricDomain.isRecommendedProductAdded())) {
                data.setRecommendedProductAdded(AppUtils.getYOrN(orderMetricDomain.isRecommendedProductAdded()));
            }
            if (Objects.nonNull(orderMetricDomain.isExploreMoreAdded())) {
                data.setExploreMoreAdded(AppUtils.getYOrN(orderMetricDomain.isExploreMoreAdded()));
            }
            if (Objects.nonNull(orderMetricDomain.getRecordingId())) {
                data.setRecordingId(orderMetricDomain.getRecordingId());
            }
            data.setOrderSource(orderMetricDomain.getOrderMetricData().getOrderSource());
            data.setNewChaiSaved(AppUtils.getYOrN(orderMetricDomain.isNewChaiSaved()));
            getPreviousOrderData(data,orderMetricDomain);
        }

        return data;
    }


    public static void getOrderBasicData(OrderMetricDetailData data, OrderMetricDomain orderMetricDomain){
        data.setOrderId(orderMetricDomain.getOrderMetricData().getOrderId());
        data.setUnitId(orderMetricDomain.getOrderMetricData().getUnitId());
        data.setTerminalId(orderMetricDomain.getOrderMetricData().getTerminalId());
        data.setTaxableAmount(orderMetricDomain.getOrderMetricData().getTaxableAmount().setScale(2, RoundingMode.HALF_UP));
        data.setTotalAmount(orderMetricDomain.getOrderMetricData().getTotalAmount().setScale(2,RoundingMode.HALF_UP));
        data.setSettledAmount(orderMetricDomain.getOrderMetricData().getSettledAmount().setScale(2,RoundingMode.HALF_UP));
        data.setBillingServerTime(orderMetricDomain.getOrderMetricData().getBillingServerTime());
        if (Objects.nonNull(orderMetricDomain.getOrderMetricData().getOrderStartTime())) {
            data.setOrderStartTime(orderMetricDomain.getOrderMetricData().getOrderStartTime());
        }
        if (Objects.nonNull(orderMetricDomain.getOrderMetricData().getOrderEndTime())) {
            data.setOrderEndTime(orderMetricDomain.getOrderMetricData().getOrderEndTime());
        }
        if (Objects.nonNull(orderMetricDomain.getOrderMetricData().getLoginStartTime())) {
            data.setLoginStartTime(orderMetricDomain.getOrderMetricData().getLoginStartTime());
        }
        if (Objects.nonNull(orderMetricDomain.getOrderMetricData().getLoginEndTime())) {
            data.setLoginEndTime(orderMetricDomain.getOrderMetricData().getLoginEndTime());
        }
        data.setLoginViaFaceIt(AppUtils.getYOrN(orderMetricDomain.getOrderMetricData().isLoginViaFaceIt()));
        data.setOtpRequested(AppUtils.getYOrN(orderMetricDomain.getOrderMetricData().isOtpRequested()));
    }

    public static void getPaymentData(OrderMetricDetailData data, OrderMetricDomain orderMetricDomain){
        if (Objects.nonNull(orderMetricDomain.getPaymentMetricData().getPaymentStartTime())) {
            data.setPaymentStartTime(orderMetricDomain.getPaymentMetricData().getPaymentStartTime());
        }
        if (Objects.nonNull(orderMetricDomain.getPaymentMetricData().getPaymentEndTime())) {
            data.setPaymentEndTime(orderMetricDomain.getPaymentMetricData().getPaymentEndTime());
        }
        data.setIsEdcPayment(AppUtils.getYOrN(orderMetricDomain.getPaymentMetricData().isEdcPayment()));
    }
    public static void getCustomizationData(OrderMetricDetailData data, OrderMetricDomain orderMetricDomain){
        data.setCustomizationDone(AppUtils.getYOrN(orderMetricDomain.getCustomizationData().isCustomized()));
        data.setCustomizationRequired(AppUtils.getYOrN(orderMetricDomain.getCustomizationData().isRequired()));
        data.setCustomizedProductAdded(AppUtils.getYOrN(orderMetricDomain.getCustomizationData().isPurchased()));
        data.setAddedViaCustomization(AppUtils.getYOrN(false));
        if (Objects.nonNull(orderMetricDomain.getCustomizationData().getType()) && orderMetricDomain.getCustomizationData().isCustomized()) {
            if (AppConstants.CUSTOMER.equals(orderMetricDomain.getCustomizationData().getType())) {
                data.setAddedViaCustomization(AppUtils.getYOrN(true));
            }
        }
        if (Objects.nonNull(orderMetricDomain.getCustomizationData().getStartTime())) {
            data.setCustomizationStartTime(orderMetricDomain.getCustomizationData().getStartTime());
        }
        if (Objects.nonNull(orderMetricDomain.getCustomizationData().getEndTime())) {
            data.setCustomizationEndTime(orderMetricDomain.getCustomizationData().getEndTime());
        }
    }

    public static void getWalletData(OrderMetricDetailData data, OrderMetricDomain orderMetricDomain){
        data.setWalletSuggested(AppUtils.getYOrN(orderMetricDomain.getWalletData().isSuggested()));
        data.setWalletPurchased(AppUtils.getYOrN(orderMetricDomain.getWalletData().isPurchased()));
        data.setWalletRedeemed(AppUtils.getYOrN(orderMetricDomain.getWalletData().isRedeemed()));
        if (Objects.nonNull(orderMetricDomain.getWalletData().getStartTime())) {
            data.setWalletSuggestionStartTime(orderMetricDomain.getWalletData().getStartTime());
        }
        if (Objects.nonNull(orderMetricDomain.getWalletData().getEndTime())) {
            data.setWalletSuggestionEndTime(orderMetricDomain.getWalletData().getEndTime());
        }
    }


    public static void getPreviousOrderData(OrderMetricDetailData data, OrderMetricDomain orderMetricDomain){
        if (Objects.nonNull(orderMetricDomain.getPreviousOrderId())) {
            data.setPreviousOrderId(orderMetricDomain.getPreviousOrderId());
        }
        if (Objects.nonNull(orderMetricDomain.getPreviousOrderBusinessDate())) {
            data.setPreviousOrderBusinessDate(orderMetricDomain.getPreviousOrderBusinessDate());
        }
        if (Objects.nonNull(orderMetricDomain.getPreviousOrderUnitId())) {
            data.setPreviousOrderUnitId(orderMetricDomain.getPreviousOrderUnitId());
        }
        data.setPreviousOrderProductAdded(AppUtils.getYOrN(orderMetricDomain.isPreviousSuggestedProductAdded()));
    }

    public static void getMembershipData(OrderMetricDetailData data, OrderMetricDomain orderMetricDomain){
        data.setMembershipSuggested(AppUtils.getYOrN(orderMetricDomain.getMembershipData().isSuggested()));
        data.setMembershipPurchased(AppUtils.getYOrN(orderMetricDomain.getMembershipData().isPurchased()));
        data.setMembershipUsed(AppUtils.getYOrN(orderMetricDomain.getMembershipData().isRedeemed()));
        if (Objects.nonNull(orderMetricDomain.getMembershipData().getType())) {
            data.setMembershipType(orderMetricDomain.getMembershipData().getType());
        }
    }

    public static WebOrderMetricDetailData convertToWebOrderMetricDetailData(WebOrderMetricDomain orderMetricDomain) {
        WebOrderMetricDetailData data = new WebOrderMetricDetailData();
        if(Objects.nonNull(orderMetricDomain)) {
            data.setCustomerId(orderMetricDomain.getCustomerId());
            data.setCustomerName(orderMetricDomain.getCustomerName());
            data.setOrderId(orderMetricDomain.getOrderId());
            data.setCartId(orderMetricDomain.getCartId());
            data.setUnitId(orderMetricDomain.getUnitId());
            data.setUnitName(orderMetricDomain.getUnitName());
            data.setOrderTime(orderMetricDomain.getOrderTime());
            data.setOfferCode(orderMetricDomain.getOfferCode());
            data.setDiscountAmount(orderMetricDomain.getDiscountAmount());
            data.setTotalAmount(orderMetricDomain.getTotalAmount());
            data.setTableNumber(orderMetricDomain.getTableNumber());
            data.setScanType(orderMetricDomain.getScanType());
        }

        return data;
    }

}
