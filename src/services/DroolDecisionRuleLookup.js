import { store } from '../redux/store';
import UtilityService from './UtilityService';

class DroolDecisionRuleLookup {
  constructor({ cartRulesDecision, inputRulesDroolDecisionProperties, outputRules }) {
    if (!cartRulesDecision || !inputRulesDroolDecisionProperties || !outputRules) {
      throw new Error('Missing required parameters for DroolDecisionRuleLookup initialization');
    }

    this.cartRulesDecision = cartRulesDecision;
    this.inputRulesDroolDecisionProperties = inputRulesDroolDecisionProperties;
    this.outputRules = outputRules;
    this.outputRulesMap = new Map();
    this.ruleMap = new Map();
    this.stateATVMap = new Map(); 

    this._initOutputRules(outputRules);
    this._initRuleMap(inputRulesDroolDecisionProperties);
    this._initStateATVMap(inputRulesDroolDecisionProperties);
  }

  _initOutputRules(outputRules) {
    if (!Array.isArray(outputRules)) {
      throw new Error('outputRules must be an array');
    }

    outputRules.forEach(rule => {
      if (!rule.rulesNumber) {
        throw new Error('Invalid output rule: missing rulesNumber');
      }
      this.outputRulesMap.set(rule.rulesNumber, rule);
    });
  }

  _initRuleMap(inputRules) {
    if (!Array.isArray(inputRules)) {
      throw new Error('inputRules must be an array');
    }

    inputRules.forEach(rule => {
      if (!rule.ruleNum || !rule.currentCartState || UtilityService.checkEmpty(rule.minCurrentCartATV) || UtilityService.checkEmpty(rule.maxCurrentCartATV)) {
        throw new Error('Invalid input rule: missing required properties');
      }

      const key = DroolDecisionRuleLookup._generateKey(rule.currentCartState, rule.minCurrentCartATV, rule.maxCurrentCartATV);
      this.ruleMap.set(key, rule);
    });
  }

  _initStateATVMap(inputRules) {
    inputRules.forEach(rule => {
      const stateKey = rule.currentCartState;
      if (!this.stateATVMap.has(stateKey)) {
        this.stateATVMap.set(stateKey, []);
      }
      this.stateATVMap.get(stateKey).push({
        min: rule.minCurrentCartATV,
        max: rule.maxCurrentCartATV,
        rule
      });
    });
    // Sort rules by min ATV for each state
    this.stateATVMap.forEach(rules => {
      rules.sort((a, b) => a.min - b.min);
    });
  }

  static _generateKey(state, minATV, maxATV) {
    if (!state || typeof minATV !== 'number' || typeof maxATV !== 'number') {
      throw new Error('Invalid parameters for key generation');
    }
    return `state=${state}|min=${minATV}|max=${maxATV}`;
  }

  _findMatchingRules(state, minATV, maxATV) {
    const stateRules = this.stateATVMap.get(state);
    if (!stateRules) return [];

    // Binary search to find matching rules
    const matchingRules = [];
    let left = 0;
    let right = stateRules.length - 1;

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const rule = stateRules[mid];

      if (minATV >= rule.min && minATV <= rule.max) {
        matchingRules.push(rule.rule);
        // Check adjacent rules for overlapping ranges
        let i = mid - 1;
        while (i >= 0 && stateRules[i].max >= minATV) {
          matchingRules.push(stateRules[i].rule);
          i -= 1;
        }
        i = mid + 1;
        while (i < stateRules.length && stateRules[i].min <= maxATV) {
          matchingRules.push(stateRules[i].rule);
          i += 1;
        }
        break;
      }

      if (minATV < rule.min) {
        right = mid - 1;
      } else {
        left = mid + 1;
      }
    }

    return matchingRules;
  }

  _determineATVRange(state, cartAmount) {
    const stateRules = this.stateATVMap.get(state);
    if (!stateRules) return { minATV: 0, maxATV: 0 };

    // Find exact match first
    const exactMatch = stateRules.find(({ min, max, rule }) => 
      cartAmount >= min && cartAmount <= max
    );

    if (exactMatch) {
      return { 
        minATV: exactMatch.min, 
        maxATV: exactMatch.max, 
        matchedRule: exactMatch.rule 
      };
    }

    return null;
  }

  getOutputRule(ruleId) {
    if (typeof ruleId !== 'number') {
      throw new Error('ruleId must be a number');
    }
    return this.outputRulesMap.get(ruleId);
  }

  static filterProductsByRule(rule) {
    if (!rule) return [];

    const state = store.getState().orderSlice;
    const productsMap = state.productBasicDetail.products || {};
    const productPricesMap = state.productPrice?.prices || {};

    const originalProductIds = Object.keys(productsMap); // Preserve original insertion order

    const filteredProducts = originalProductIds
      .map(pid => productsMap[pid])
      .filter(product => {
        // 1. Filter by product type
        if (rule.productType > 0 && product.type !== rule.productType) return false;

        // 2. Filter by veg/non-veg
        if (rule.vegNonVeg === 'VEG') {
          if (product.attr !== 'VEG' && !UtilityService.checkEmpty(product.attr)) return false;
        } else if (rule.vegNonVeg === 'NON_VEG') {
          if (product.attr !== 'NON_VEG') return false;
        }

        // 3. Filter by price
        if (rule.minPriceOfProduct > 0 || rule.maxPriceOfProduct > 0) {
          const prices = productPricesMap[product.id.id]?.prices;
          if (UtilityService.checkEmpty(prices)) return false;

          const price =
            prices?.None?.price ??
            prices?.Regular?.price ??
            Object.values(prices)?.[0]?.price;

          if (price === undefined) return false;

          if (
            price < rule.minPriceOfProduct ||
            (rule.maxPriceOfProduct > 0 && price > rule.maxPriceOfProduct)
          ) {
            return false;
          }
        }

        // if (rule.recommendationType && rule.recommendationType !== 'DEFAULT') {
        //   if (!Array.isArray(product.tag) || !product.tag.includes(rule.recommendationType)) {
        //     return false;
        //   }
        // }

        return true;
      });

    // 4. Apply product count limit if specified
    return rule.productCount > 0
      ? filteredProducts.slice(0, rule.productCount)
      : filteredProducts;
  }


  getRecommendedProductIDs(state, cartAmount) {
    try {
      const { minATV, maxATV, matchedRule } = this._determineATVRange(state, cartAmount);
      if (!matchedRule) return [];

      const outputRuleIds = matchedRule.outputRulesToBeRun?.split('_') || [];
      const recommendedProducts = new Set();

      outputRuleIds.forEach(ruleId => {
        const outputRule = this.getOutputRule(parseInt(ruleId, 10));
        if (!outputRule) return;

        if (outputRule.type === 'ID_BASED') {
          outputRule.productIDsList.forEach(id => recommendedProducts.add(id));
        } else if (outputRule.type === 'LOGIC_BASED') {
          const filteredProducts = DroolDecisionRuleLookup.filterProductsByRule(outputRule);
          filteredProducts.forEach(p => recommendedProducts.add(p.id));
        }
      });

      return Array.from(recommendedProducts);
    } catch (error) {
      console.error('Error in getRecommendedProductIDs:', error);
      return [];
    }
  }

  getDetailedRecommendations(state, cartAmount) {
    try {
      const { minATV, maxATV, matchedRule } = this._determineATVRange(state, cartAmount);
      if (!matchedRule) {
        return {
          matchedInputRules: [],
          ruleStrings: [],
          atvRange: { minATV, maxATV },
          appliedRules: [],
          products: [],
          cartAmount
        };
      } 

      const outputRuleIds = matchedRule.outputRulesToBeRun?.split('_') || [];
      const appliedRules = [];
      const recommendedProducts = new Map();

      outputRuleIds.forEach(ruleId => {
        const outputRule = this.getOutputRule(parseInt(ruleId, 10));
        if (!outputRule) return;

        appliedRules.push(outputRule);

        if (outputRule.type === 'ID_BASED') {
          const products = store.getState().orderSlice.productBasicDetail.products;
          
          outputRule.productIDsList
            .filter(pid => pid !== -1)
            .forEach(pid => {
              const product = products[pid];
              if (product) {
                recommendedProducts.set(pid, { ...product, ruleNumber: outputRule.rulesNumber });
              }
            });
        } else if (outputRule.type === 'LOGIC_BASED') {
          const filteredProducts = DroolDecisionRuleLookup.filterProductsByRule(outputRule);
          filteredProducts.forEach(p => recommendedProducts.set(p.id, { ...p, ruleNumber: outputRule.rulesNumber }));
        }
      });

      return {
        matchedInputRules: [matchedRule],
        ruleStrings: [DroolDecisionRuleLookup._generateKey(state, minATV, maxATV)],
        atvRange: { minATV, maxATV },
        appliedRules,
        products: Array.from(recommendedProducts.values()),
        cartAmount
      };
    } catch (error) {
      console.error('Error in getDetailedRecommendations:', error);
      return {
        matchedInputRules: [],
        ruleStrings: [],
        atvRange: { minATV: 0, maxATV: 0 },
        appliedRules: [],
        products: [],
        cartAmount
      };
    }
  }

  getApplicableRules(state, cartAmount) {
    try {
      const { minATV, maxATV, matchedRule } = this._determineATVRange(state, cartAmount);
      if (!matchedRule) return [];

      const outputRuleIds = matchedRule.outputRulesToBeRun?.split('_') || [];
      return outputRuleIds
        .map(id => this.getOutputRule(parseInt(id, 10)))
        .filter(rule => rule !== undefined);
    } catch (error) {
      console.error('Error in getApplicableRules:', error);
      return [];
    }
  }

  getAvailableCartStates() {
    return Array.from(this.stateATVMap.keys());
  }
}

export { DroolDecisionRuleLookup };