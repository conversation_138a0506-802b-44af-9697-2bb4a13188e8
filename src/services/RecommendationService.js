import { DroolDecisionRuleLookup } from "./DroolDecisionRuleLookup";
import { store } from '../redux/store';

class RecommendationService {
  constructor() {
    this.droolDecisionRuleLookup = null;
    this.isInitialized = false;
  }

  static validateCartRulesData(cartRulesData) {
    if (!cartRulesData) {
      return false;
    }

    const { cartRulesDecision, inputRulesDroolDecisionProperties, outputRules } = cartRulesData;

    if (!cartRulesDecision || !inputRulesDroolDecisionProperties || !outputRules) {
      return false;
    }

    if (!Array.isArray(inputRulesDroolDecisionProperties) || !Array.isArray(outputRules)) {
      return false;
    }

    return true;
  }

  initialize(cartRulesData) {
    try {
      console.log("Initializing recommendation engine!!!");
      const isValid = RecommendationService.validateCartRulesData(cartRulesData);
      if(!isValid) return;
      
      const { cartRulesDecision, inputRulesDroolDecisionProperties, outputRules } = cartRulesData;
      
      this.droolDecisionRuleLookup = new DroolDecisionRuleLookup({
        cartRulesDecision,
        inputRulesDroolDecisionProperties,
        outputRules
      });

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize RecommendationService:', error);
      this.isInitialized = false;
      throw error;
    }
  }

  reinitialize(cartRulesData) {
    try {
      if (!cartRulesData) {
        throw new Error('Cart rules data not found in Redux store');
      }
      
      this.reset();
      return this.initialize(cartRulesData);
    } catch (error) {
      console.error('Failed to reinitialize RecommendationService:', error);
      this.isInitialized = false;
      throw error;
    }
  }

  static validateState(state) {
    if (!state || typeof state !== 'string') {
      throw new Error('Invalid state parameter. Must be a non-empty string');
    }
    return true;
  }

  static validateCartAmount(cartAmount) {
    if (typeof cartAmount !== 'number' || cartAmount < 0) {
      throw new Error('cartAmount must be a non-negative number');
    }
    return true;
  }

  static validateProductList(productList) {
    if (!Array.isArray(productList)) {
      throw new Error('productList must be an array');
    }
    return true;
  }

  getRecommendedProductIDs(state, cartAmount) {
    try {
      if (!this.isInitialized) {
        throw new Error('RecommendationService not initialized. Call initialize() first.');
      }

      RecommendationService.validateState(state);
      RecommendationService.validateCartAmount(cartAmount);

      return this.droolDecisionRuleLookup.getRecommendedProductIDs(state, cartAmount);
    } catch (error) {
      console.error('Error in getRecommendedProductIDs:', error);
      throw error;
    }
  }

  getDetailedRecommendations(state, cartAmount) {
    try {
      if (!this.isInitialized) {
        return null;
      }
      
      RecommendationService.validateState(state);
      RecommendationService.validateCartAmount(cartAmount);

      return this.droolDecisionRuleLookup.getDetailedRecommendations(state, cartAmount);
    } catch (error) {
      console.log('Error in getDetailedRecommendations:', error);
      throw error;
    }
  }

  getApplicableRules(state, cartAmount) {
    try {
      if (!this.isInitialized) {
        throw new Error('RecommendationService not initialized. Call initialize() first.');
      }

      RecommendationService.validateState(state);
      RecommendationService.validateCartAmount(cartAmount);

      return this.droolDecisionRuleLookup.getApplicableRules(state, cartAmount);
    } catch (error) {
      console.error('Error in getApplicableRules:', error);
      throw error;
    }
  }

  getAvailableCartStates() {
    try {
      if (!this.isInitialized) {
        throw new Error('RecommendationService not initialized. Call initialize() first.');
      }

      return this.droolDecisionRuleLookup.getAvailableCartStates();
    } catch (error) {
      console.error('Error in getAvailableCartStates:', error);
      throw error;
    }
  }

  reset() {
    this.droolDecisionRuleLookup = null;
    this.isInitialized = false;
  }
}

// Create and export a singleton instance
const recommendationService = new RecommendationService();

// Export for usage
export { recommendationService, RecommendationService };

// Example usage:
// console.log("=== Recommendation System Examples ===");

// // Test case 1: BEV_ONLY cart with cart amount 150
// console.log("\n1. BEV_ONLY cart with cart amount 150:");
// const result1 = recommendationService.getDetailedRecommendations("BEV_ONLY", 150);
// console.log("Matched Input Rule:", result1.matchedInputRules[0]?.ruleNum);
// console.log("Rule String:", result1.ruleStrings[0]);
// console.log("ATV Range:", result1.atvRange);
// console.log("Applied Rules:", result1.appliedRules.map(r => `Rule ${r.rulesNumber} (${r.type})`));
// console.log("Recommended Products:", result1.products.map(p => `${p.name} - ₹${p.price}`));

// // Test case 2: FOOD_BEV cart with cart amount 350
// console.log("\n2. FOOD_BEV cart with cart amount 350:");
// const result2 = recommendationService.getDetailedRecommendations("FOOD_BEV", 350);
// console.log("Matched Input Rule:", result2.matchedInputRules[0]?.ruleNum);
// console.log("Rule String:", result2.ruleStrings[0]);
// console.log("ATV Range:", result2.atvRange);
// console.log("Applied Rules:", result2.appliedRules.map(r => `Rule ${r.rulesNumber} (${r.type})`));
// console.log("Recommended Products:", result2.products.map(p => `${p.name} - ₹${p.price}`));

// // Test case 3: FOOD_ONLY cart with cart amount 250
// console.log("\n3. FOOD_ONLY cart with cart amount 250:");
// const result3 = recommendationService.getDetailedRecommendations("FOOD_ONLY", 250);
// console.log("Matched Input Rule:", result3.matchedInputRules[0]?.ruleNum);
// console.log("Rule String:", result3.ruleStrings[0]);
// console.log("ATV Range:", result3.atvRange);
// console.log("Applied Rules:", result3.appliedRules.map(r => `Rule ${r.rulesNumber} (${r.type})`));
// console.log("Recommended Products:", result3.products.map(p => `${p.name} - ₹${p.price}`));

// // Test case 4: Get only product IDs
// console.log("\n4. Getting only recommended product IDs for BEV_ONLY, cart amount 150:");
// const productIds = recommendationService.getRecommendedProductIDs("BEV_ONLY", 150);
// console.log("Product IDs:", productIds);

// console.log("\n=== Available Cart States ===");
// console.log(recommendationService.getAvailableCartStates());