/* eslint-disable import/no-cycle */
import moment from "moment";
import RestService from "../../services/RestService";
import APIConstants from "../../utils/APIConstants";
import {
    addPayment,
    removePayment,
    updatePayment,
    setPayments,
    setTotalAmount,
    setRemainingAmount,
    setInputAmount,
    setUnitPaymentMode,
    setUnitPaymentModeTypes,
    setEDCIntialTranscationResponse,
    setEdcExternalTranscationId,
    setPaymentType,
    setSelectedWalletDenomination,
    setCreWalletSuggestionResponse,
    setRefrenceNumber,
    setMakePayment,
    setHangingPayments,
    setIsAutoEDCInititaionFailed,
    setDQRIntialTranscationResponse,
    setDqrExternalTranscationId,
    setStartPollForPaymentStatus,
    setShowDQRPayment,
    setLastOrderIdForEdcPayment,
    setIsAutoDQRInititaionFailed,
    setDQRPaymentStatus
} from "./PaymentSlice";
import { setIsWalletOrder } from "../order/OrderSlice";
// eslint-disable-next-line import/no-cycle
import { setRightSideBar, setShowBuzzerScreen } from "../../pages/home/<USER>";
import Constants from "../../utils/Constants";
import UtilityService from "../../services/UtilityService";
// eslint-disable-next-line import/no-cycle
import { emitMessage } from "../../cafeAppCommunication/cafeAppCommunication";
import CartAction from "../../sections/cart/CartAction";
import ProgressTabButtonsAction from "../progressTabButtons/ProgressTabButtonsAction";
import CommunicationService from "../../cafeAppCommunication/communicationService";
import CustomerAction from "../customerInfo/customerAction";

class PaymentActions {
    static makePayment = () =>
        (dispatch,getState) => {
            const { tableDetail } = getState().metadataSlice;
            const selectedBuzzer = getState().homeSlice.selectedBuzzer;
            const { isRechargingWallet } = getState().paymentSlice;
            if (UtilityService.checkEmpty(selectedBuzzer) && !UtilityService.checkEmpty(tableDetail) && tableDetail.tableService && /* tableDetail.tableServiceType === 0 && */ !isRechargingWallet) {
                dispatch(setMakePayment(false));
                dispatch(setShowBuzzerScreen(true));
            } else {
                dispatch(setMakePayment(true));
                emitMessage({ PAYMENT_INITIATED: null })
                dispatch(this.updateTranscation(0));
            }
        }

    static getModeId = (mode) => {
        if (mode === Constants.PAYMENT_MODES_TYPES.CASH) {
            return 1;
        }
        if (mode === Constants.PAYMENT_MODES_TYPES.CARD) {
            return 2;
        }
        if (mode === Constants.PAYMENT_MODES_TYPES.WALLET) {
            return 3;
        }
        if (mode === Constants.PAYMENT_MODES_TYPES.UPI) {
            return 4;
        }
        return 5;
    }

    static addPaymentMode = (modeType, amount, description, modeId, linkPayment, extraData, callback, isEmitMessage = true, initDQR) =>
        (dispatch, getState) => {
            let otpVerificationRequired = false;
            const payments = getState().paymentSlice.payments;
            let inputAmount = getState().paymentSlice.inputAmount;
            const customer = getState().customerSlice.customerBasicInfo;
            const remainingAmount = getState().paymentSlice.remainingAmount;
            const totalAmount = getState().paymentSlice.totalAmount;
            const { currentWalletSuggestion } = getState().orderSlice;
            const transactionDetail = getState().cartSlice.cart.transactionDetail;
            amount = parseInt(amount, 10)
            const ReceivedAmount = amount;
            let isAllowedtoAdd = true;
            payments.forEach((payment, index) => {
                if (!payment.isSwitch && payment.id === modeId || payment.amount === 0) {
                    isAllowedtoAdd = false;
                }
            });
            if (amount > remainingAmount && modeType !== Constants.PAYMENT_MODES_TYPES.CASH) {
                isAllowedtoAdd = false;
            }
            if (isAllowedtoAdd && inputAmount !== "") {
                if (modeType === Constants.PAYMENT_MODES_TYPES.WALLET) {
                    if (UtilityService.checkEmpty(customer.walletBalance)) {
                        if (callback !== null) {
                            callback(false);
                        }

                        return;
                    }
                    if (!UtilityService.checkEmpty(customer.walletBalance) && customer.walletBalance <= 0) {
                        if (callback !== null) {
                            callback(false);
                        }
                        return;
                    }
                }
                inputAmount = parseInt(inputAmount, 10);
                let remainingAmt = 0;
                amount = amount > remainingAmount ? remainingAmount : amount;
                if (!Number.isNaN(inputAmount)) {
                    amount = inputAmount > remainingAmount ? remainingAmount : inputAmount;
                }
                let payment = null;
                if (modeType === Constants.PAYMENT_MODES_TYPES.CASH) {
                    const change = ReceivedAmount > amount ? ReceivedAmount - amount : 0;
                    if (ReceivedAmount === change) {
                        if (callback !== null) {
                            callback(false);
                            return;
                        }
                    }
                    amount = ReceivedAmount - change;
                    payment = {
                        id: modeId,
                        amount,
                        paymentModeType: modeType,
                        description: { ReceivedAmount, change },
                        validateStatus: null,
                        status: amount === 0 ? Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED : Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED,
                        isSwitch: false
                    }
                } else if (modeType === Constants.PAYMENT_MODES_TYPES.CARD) {
                    payment = {
                        id: modeId,
                        amount,
                        paymentModeType: modeType,
                        description,
                        validateStatus: null,
                        status: Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED,
                        isSwitch: false
                    }
                    if([35,36].includes(modeId)){
                        payment = {
                            id: modeId,
                            amount,
                            paymentModeType: modeType,
                            description,
                            validateStatus: true,
                            status: linkPayment === true ? Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED : Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING,
                            isSwitch: false
                        }
                    }
                } else if (modeType === Constants.PAYMENT_MODES_TYPES.WALLET) {
                    if (!customer.otpVerified && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)) {
                        otpVerificationRequired = true;
                    }
                    if (customer.walletBalance < amount) {
                        amount = customer.walletBalance;
                    }
                    payment = {
                        id: modeId,
                        amount,
                        paymentModeType: modeType,
                        description: "Chaayos Wallet",
                        validateStatus: null,
                        status: Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED,
                        isSwitch: false
                    }
                } else if (modeType === Constants.PAYMENT_MODES_TYPES.UPI) {
                    payment = {
                        id: modeId,
                        amount,
                        paymentModeType: modeType,
                        description,
                        validateStatus: null,
                        status: Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED,
                        isSwitch: false
                    }

                    if (modeId === 39) {
                        payment = {
                            id: modeId,
                            amount,
                            paymentModeType: modeType,
                            description,
                            validateStatus: true,
                            // eslint-disable-next-line
                            status: linkPayment === true ? Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED : Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING,
                            isSwitch: false
                        }
                    } else if (modeId === 37) {
                        payment = {
                            id: modeId,
                            amount,
                            paymentModeType: modeType,
                            description,
                            validateStatus: true,
                            status: linkPayment === true ? Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED : Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING,
                            isSwitch: false
                        }
                    }


                } else if (modeType === Constants.PAYMENT_MODES_TYPES.EDC) {
                    payment = {
                        id: modeId,
                        amount,
                        paymentModeType: modeType,
                        description,
                        validateStatus: true,
                        status: linkPayment === true ? Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED : Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING,
                        isSwitch: false
                    }
                }
                else if (modeType === Constants.PAYMENT_MODES_TYPES.DQR) {
                    payment = {
                        id: modeId,
                        amount,
                        paymentModeType: modeType,
                        description,
                        validateStatus: true,
                        status: linkPayment === true ? Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED : Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING,
                        isSwitch: false
                    }
                }
                else {
                    payment = {
                        id: modeId,
                        amount,
                        paymentModeType: modeType,
                        description,
                        validateStatus: null,
                        status: linkPayment === true ? Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED : Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED,
                        isSwitch: false
                    }
                }

                payment = { ...payment, extraData: !UtilityService.checkEmpty(extraData) ? extraData : null };
                dispatch(addPayment(payment));
                const Payments = getState().paymentSlice.payments;
                for (let i = 0; i < payments.length; i += 1) {
                    if (payments[i].paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET && !customer.otpVerified && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)) {
                        otpVerificationRequired = true;
                    }
                }
                if ((totalAmount >= 0 && [35, 36, 37, 39].indexOf(modeId) < 0) || initDQR === false || linkPayment === true) {
                    emitMessage({
                        POS_PAYMENT_MODE_ADDED: {
                            Payments,
                            otpVerificationRequired
                        }
                    });
                }
                if (amount <= remainingAmount) {
                    remainingAmt = remainingAmount - amount;
                }
                dispatch(setRemainingAmount(remainingAmt));
                if ([35, 36, 37, 39].indexOf(modeId) < 0 || linkPayment) {
                    dispatch(setInputAmount(remainingAmt));
                }
                const PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.EDC);
                if (PaymentModeMap.includes(modeId) && !linkPayment) {
                    dispatch(this.initiatEDCPayment(modeId, amount, (status) => {
                        const idx = Payments.findIndex(currpayment => currpayment.id === modeId);
                        if (status) {
                            dispatch(PaymentActions.paymentSuccesMessage(`${Payments[idx].description} payment initiated successfully`));
                            dispatch(this.updateEDCValidateStatus(idx, status, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED));
                            dispatch(setInputAmount(remainingAmt));
                            const PaymentsArr = getState().paymentSlice.payments;
                            for (let i = 0; i < payments.length; i += 1) {
                                if (payments[i].paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET && !customer.otpVerified && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)) {
                                    otpVerificationRequired = true;
                                }
                            }
                            emitMessage({
                                POS_PAYMENT_MODE_ADDED: {
                                    Payments:PaymentsArr,
                                    otpVerificationRequired
                                }
                            });
                            callback(true);
                        } else {
                            dispatch(this.removePayment(idx, Payments[idx], false));
                            callback(false);
                        }
                    }));
                }
                if (modeId === 39 && !linkPayment && initDQR !== false) {
                    dispatch(this.initiatDQRPayment(modeId, amount, (status, qrData) => {
                        const idx = Payments.findIndex(currpayment => currpayment.id === modeId);
                        if (status) {
                            emitMessage({
                                DQR_QR_DATA: { qrData }
                            });
                            dispatch(PaymentActions.paymentSuccesMessage(`${Payments[idx].description} payment initiated successfully`));
                            // dispatch(this.updateDQRValidateStatus(idx, status, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED));
                            dispatch(setInputAmount(remainingAmt));
                            const PaymentsArr = getState().paymentSlice.payments;
                            for (let i = 0; i < payments.length; i += 1) {
                                if (payments[i].paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET && !customer.otpVerified && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)) {
                                    otpVerificationRequired = true;
                                }
                            }
                            emitMessage({
                                POS_PAYMENT_MODE_ADDED: {
                                    Payments:PaymentsArr,
                                    otpVerificationRequired
                                }
                            });
                            callback(true);
                        } else {
                            dispatch(this.removePayment(idx, Payments[idx], false));
                            callback(false);
                        }
                    }));
                }

                if (callback !== null && ([35, 36, 37, 39].indexOf(modeId) < 0 || linkPayment)) {
                    callback(true);
                }
            } else {
                dispatch(PaymentActions.paymentError(`Cannot add Payment Mode`));
                if (callback !== null && callback !== undefined) {
                    if ([39, 37, 11].includes(modeId)) {
                        callback(true);
                    } else {
                        callback(false);
                    }
                }
            }


        }

    static removePayment = (index, payment, setInputAmtFlag) =>
        (dispatch, getState) => {
            let otpVerificationRequired = false;
            const currPayments = getState().paymentSlice.payments;
            const unitPaymentModes = getState().metadataSlice.unitDetail.unitPaymentModes;
            const refsIds = [];
            currPayments.forEach((currPayment, i) => {
                const idx = unitPaymentModes.findIndex(mode => mode.id === currPayment.id);
                if ((!(currPayment.id === 10 && currPayment.id === 31 && currPayment.id === 12) && (currPayment.id !== 4 && currPayment.id !== 5 && currPayment.id !== 18)) && unitPaymentModes[idx].needsSettlementSlip) {
                    refsIds.push(i);
                }
            })
            if (refsIds.length === 1 && refsIds.includes(index)) {
                dispatch(setRefrenceNumber(null))
            }
            const remainingAmount = getState().paymentSlice.remainingAmount;
            const remainingAmt = Number.isNaN(payment.amount) ? remainingAmount : remainingAmount + parseInt(payment.amount, 10);
            const customer = getState().customerSlice.customerBasicInfo;
            const totalAmount = getState().paymentSlice.totalAmount;
            const { currentWalletSuggestion } = getState().orderSlice;
            const transactionDetail = getState().cartSlice.cart.transactionDetail;
            dispatch(setRemainingAmount(remainingAmt));
            if (setInputAmtFlag) {
                dispatch(setInputAmount(remainingAmt));
            }
            dispatch(removePayment(index));
            const Payments = getState().paymentSlice.payments;
            for (let i = 0; i < Payments.length; i += 1) {
                if (Payments[i].paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET && !customer.otpVerified && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)) {
                    otpVerificationRequired = true;
                }
            }
            if (payment.id === 39) {
                dispatch(setDQRIntialTranscationResponse(null));
                emitMessage({
                    DQR_QR_DATA: { qrData: null }
                });
            }
            if (totalAmount > 0) {
                emitMessage({
                    POS_PAYMENT_MODE_ADDED: {
                        Payments,
                        otpVerificationRequired
                    }
                });
            }
        }

    static updatePayment = (index, amount, type, desc, modeId, callback) =>
        (dispatch, getState) => {
            let otpVerificationRequired = false;
            const { payments, totalAmount, isAutoEDCInititaionFailed } = getState().paymentSlice;
            const customer = getState().customerSlice.customerBasicInfo;
            const { currentWalletSuggestion } = getState().orderSlice;
            const transactionDetail = getState().cartSlice.cart.transactionDetail;
            let isAllowedtoUpdate = true;
            amount = parseInt(amount, 10);
            if (!UtilityService.checkEmpty(modeId)) {
                payments.forEach((payment, i) => {
                    if (!payment.isSwitch && i !== index && (payment.id === modeId || payment.amount === 0)) {
                        isAllowedtoUpdate = false;
                    }
                });
            }
            if (!UtilityService.checkEmpty(type) && Number.isNaN(amount)) {
                isAllowedtoUpdate = false
            }
            if (isAllowedtoUpdate) {
                if (!UtilityService.checkEmpty(type) && type === Constants.PAYMENT_MODES_TYPES.WALLET) {
                    if (UtilityService.checkEmpty(customer.walletBalance)) {
                        dispatch(PaymentActions.paymentError(`Cannot add Payment Mode`));
                        callback(false);
                        return;
                    }
                    if (!UtilityService.checkEmpty(customer.walletBalance) && customer.walletBalance <= 0) {
                        dispatch(PaymentActions.paymentError(`Cannot add Payment Mode`));
                        callback(false);
                        return;
                    }
                }
                const paymentItem = payments[index];
                if (UtilityService.checkEmpty(type) && Number.isNaN(paymentItem.amount)) {
                    if (callback !== null) {
                        dispatch(PaymentActions.paymentError(`Cannot add Payment Mode`));
                        callback(false);
                        return;
                    }
                }
                amount = parseInt(amount, 10);
                const ReceivedAmount = amount;
                amount = Number.isNaN(amount) ? 0 : amount;
                if (!UtilityService.checkEmpty(type) && type === Constants.PAYMENT_MODES_TYPES.CASH) {
                    amount = ReceivedAmount > paymentItem.amount ? paymentItem.amount : ReceivedAmount;
                }
                let paidAmount = 0;
                payments.forEach((currPayment, i) => {
                    if (i !== index) {
                        paidAmount += parseInt(currPayment.amount, 10);
                    }
                })
                let remainingAmt = totalAmount - paidAmount - parseInt(amount, 10);

                const paymentModeType = UtilityService.checkEmpty(type) ? paymentItem.paymentModeType : type;
                const description = UtilityService.checkEmpty(desc) ? paymentItem.description : desc
                const id = UtilityService.checkEmpty(modeId) ? paymentItem.id : modeId
                let updatedPayment = null
                if (paymentModeType === Constants.PAYMENT_MODES_TYPES.CASH) {
                    const change = ReceivedAmount > paymentItem.amount ? ReceivedAmount - paymentItem.amount : 0;
                    if (ReceivedAmount === change) {
                        if (callback !== null) {
                            callback(false);
                            return;
                        }
                    }
                    if (!UtilityService.checkEmpty(type)) {
                        amount = ReceivedAmount - change;
                    }
                    updatedPayment = {
                        id,
                        amount: UtilityService.checkEmpty(type) ? ReceivedAmount : amount,
                        paymentModeType,
                        description: { ReceivedAmount, change },
                        validateStatus: null,
                        isSwitch: UtilityService.checkEmpty(desc)
                    }
                } else if (paymentModeType === Constants.PAYMENT_MODES_TYPES.CARD) {
                    updatedPayment = {
                        id,
                        amount: UtilityService.checkEmpty(type) ? ReceivedAmount : amount,
                        paymentModeType,
                        description,
                        validateStatus: null,
                        isSwitch: UtilityService.checkEmpty(desc)
                    }
                    if([35,36].includes(id)){
                        updatedPayment = {
                            id,
                            amount: UtilityService.checkEmpty(type) ? ReceivedAmount : amount,
                            paymentModeType,
                            description,
                            validateStatus: true,
                            isSwitch: UtilityService.checkEmpty(desc)
                        }
                    }
                } else if (paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET) {
                    if (!UtilityService.checkEmpty(type)) {
                        if (!customer.otpVerified && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)) {
                            otpVerificationRequired = true;
                        }
                        if (customer.walletBalance < amount) {
                            remainingAmt = remainingAmt + amount - customer.walletBalance;
                            amount = customer.walletBalance;
                        }
                    }
                    updatedPayment = {
                        id,
                        amount: UtilityService.checkEmpty(type) ? ReceivedAmount : amount,
                        paymentModeType,
                        description: "Chaayos Wallet",
                        validateStatus: null,
                        isSwitch: UtilityService.checkEmpty(desc)
                    }
                }
                else if (paymentModeType === Constants.PAYMENT_MODES_TYPES.UPI) {
                    updatedPayment = {
                        id,
                        amount: UtilityService.checkEmpty(type) ? ReceivedAmount : amount,
                        paymentModeType,
                        description,
                        validateStatus: null,
                        isSwitch: UtilityService.checkEmpty(desc)
                    }
                } else if (paymentModeType === Constants.PAYMENT_MODES_TYPES.EDC) {
                    updatedPayment = {
                        id,
                        amount: UtilityService.checkEmpty(type) ? ReceivedAmount : amount,
                        paymentModeType,
                        description,
                        validateStatus: true,
                        isSwitch: UtilityService.checkEmpty(desc)
                    }
                } else {
                    updatedPayment = {
                        id,
                        amount: UtilityService.checkEmpty(type) ? ReceivedAmount : amount,
                        paymentModeType,
                        description,
                        validateStatus: null,
                        isSwitch: UtilityService.checkEmpty(desc)
                    }
                }

                updatedPayment = {
                    ...updatedPayment,
                    status: ([35,36,37,39].includes(id)) && !UtilityService.checkEmpty(desc) ?
                        Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING :
                        Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED,
                    extraData: paymentItem.extraData
                }
                dispatch(setRemainingAmount(remainingAmt));
                if (!UtilityService.checkEmpty(type)) {
                    dispatch(setInputAmount(remainingAmt));
                }
                dispatch(updatePayment({ index, payment: updatedPayment }));
                const updatedPayments = getState().paymentSlice.payments;
                for (let i = 0; i < updatedPayments.length; i += 1) {
                    if (updatedPayments[i].paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET && !customer.otpVerified
                        && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)
                    ) {
                        otpVerificationRequired = true;
                    }
                }
                if (totalAmount > 0 && !UtilityService.checkEmpty(type) && [35, 36, 37, 39].indexOf(modeId) < 0) {
                    emitMessage({
                        POS_PAYMENT_MODE_ADDED: {
                            Payments: updatedPayments,
                            otpVerificationRequired
                        }
                    });
                }

                const PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.EDC);
                if (PaymentModeMap.includes(modeId) && !UtilityService.checkEmpty(desc)) {
                    dispatch(this.initiatEDCPayment(modeId, amount, (status) => {
                        const idx = updatedPayments.findIndex(currpayment => currpayment.id === modeId);
                        if (status) {
                            dispatch(PaymentActions.paymentSuccesMessage(`${updatedPayments[idx].description} payment initiated successfully`));
                            dispatch(this.updateEDCValidateStatus(idx, status, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED));
                            const PaymentsArr = getState().paymentSlice.payments;
                            for (let i = 0; i < updatedPayments.length; i += 1) {
                                if (updatedPayments[i].paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET && !customer.otpVerified
                                    && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)
                                ) {
                                    otpVerificationRequired = true;
                                }
                            }
                            emitMessage({
                                POS_PAYMENT_MODE_ADDED: {
                                    Payments:PaymentsArr,
                                    otpVerificationRequired
                                }
                            });
                            callback(true);
                        } else {
                            const currPayment = updatedPayments[idx];
                            const updatedPaymentObj = {
                                id: currPayment.id,
                                amount: currPayment.amount,
                                paymentModeType: currPayment.paymentModeType,
                                description: currPayment.description,
                                validateStatus: currPayment.validateStatus,
                                status: Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED,
                                isSwitch: true
                            }
                            dispatch(updatePayment({ index: idx, payment: updatedPaymentObj }));
                            callback(false);
                        }
                    }));
                }
                if (id === 39 && !UtilityService.checkEmpty(desc)) {
                    dispatch(this.initiatDQRPayment(modeId, amount, (status, qrData) => {
                        const idx = updatedPayments.findIndex(currpayment => currpayment.id === modeId);
                        if (status) {
                            emitMessage({
                                DQR_QR_DATA: { qrData }
                            });
                            dispatch(PaymentActions.paymentSuccesMessage(`${updatedPayments[idx].description} payment initiated successfully`));
                            // dispatch(this.updateEDCValidateStatus(idx, status, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED));
                            const PaymentsArr = getState().paymentSlice.payments;
                            for (let i = 0; i < updatedPayments.length; i += 1) {
                                if (updatedPayments[i].paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET && !customer.otpVerified
                                    && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)
                                ) {
                                    otpVerificationRequired = true;
                                }
                            }
                            emitMessage({
                                POS_PAYMENT_MODE_ADDED: {
                                    Payments:PaymentsArr,
                                    otpVerificationRequired
                                }
                            });
                            callback(true);
                        } else {
                            const currPayment = updatedPayments[idx];
                            const updatedPaymentObj = {
                                id: currPayment.id,
                                amount: currPayment.amount,
                                paymentModeType: currPayment.paymentModeType,
                                description: currPayment.description,
                                validateStatus: currPayment.validateStatus,
                                status: Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED,
                                isSwitch: true
                            }
                            dispatch(updatePayment({ index: idx, payment: updatedPaymentObj }));
                            callback(false);
                        }
                    }));
                }
            }
            if (!isAllowedtoUpdate) {
                dispatch(PaymentActions.paymentError(`Cannot add Payment Mode`));
            }
            if (callback !== null && [35, 36, 37, 39].indexOf(modeId) < 0) {
                callback(isAllowedtoUpdate);
            }

        }


    static isPaymentModePresent = (modeId, checkModeActive = true) => (dispatch, getState) => {
        const { unitPaymentModes, isAutoEDCInititaionFailed, isAutoDQRInititaionFailed, autoEdcEnabled } = getState().paymentSlice;
        const { UnitEDCMapping } = getState().metadataSlice;
        const unitDQRMapping = getState().metadataSlice.UnitDQRMapping;
        let isFoundInPaymentMapping = false;
        let isFoundInEdcMapping = false;
        let isFoundInDQRMapping = false;
        let autoEDCInitiateFailed = isAutoEDCInititaionFailed;
        let autoDQRInitiateFailed = isAutoDQRInititaionFailed;
        if (checkModeActive) {
            Object.values(unitPaymentModes).forEach((mode, i) => {
                if (mode.id === modeId && mode.status === Constants.ACTIVE) {
                    isFoundInPaymentMapping = true;
                }
            })
        } else {
            isFoundInPaymentMapping = true;
        }
        const PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.EDC);
        if (PaymentModeMap.includes(modeId)) {
            if (!UtilityService.checkEmpty(UnitEDCMapping)) {
                Object.values(UnitEDCMapping).forEach((mapping, i) => {
                    if (mapping.terminalId === UtilityService.terminalId.toString() && mapping.status === Constants.ACTIVE) {
                        isFoundInEdcMapping = true;
                    }
                })
            }
        } else {
            isFoundInEdcMapping = true;
            autoEDCInitiateFailed = false;
        }
        if (modeId === 39) {
            if (!UtilityService.checkEmpty(unitDQRMapping)) {
                isFoundInDQRMapping = true;
            }
        } else {
            isFoundInDQRMapping = true;
            autoDQRInitiateFailed = false;
        }

        return isFoundInPaymentMapping && isFoundInEdcMapping && isFoundInDQRMapping && !autoEDCInitiateFailed && autoEdcEnabled && !autoDQRInitiateFailed;
    }

    static getPaymentModeDetailByModeID = (modeId) => (dispatch, getState) => {
        const { unitPaymentModes } = getState().paymentSlice;
        let data = {};
        Object.values(unitPaymentModes).forEach((mode, i) => {
            if (mode.id === modeId) {
                data = mode;
            }
        })
        return data;
    }

    static openDrawerForWalletSuggestion = () =>
        (dispatch, getState) => {
            const transactionDetail = getState().cartSlice.cart.transactionDetail;
            if (transactionDetail.paidAmount > 0) {
                dispatch(setRightSideBar({ open: true, code: Constants.RIGTHSIDE_BAR.WALLET_SUGGESTION, data: {} }))
            }
        }

    static setPaymentsData = (callback) =>
        (dispatch, getState) => {
            const transactionDetail = getState().cartSlice.cart.transactionDetail;
            const paymentModes = getState().metadataSlice.unitDetail.unitPaymentModes;
            const unitEdcMapping = getState().metadataSlice.UnitEDCMapping;
            const unitDQRMapping = getState().metadataSlice.UnitDQRMapping;
            let totalAmount = transactionDetail.paidAmount;
            const customer = getState().customerSlice.customerBasicInfo;
            const appliedCoupon = getState().offersSlice.appliedCoupon;
            const { isRechargingWallet, walletRechargeData, autoEdcEnabled } = getState().paymentSlice;
            if (isRechargingWallet) {
                totalAmount = walletRechargeData.rechargeAmount;
            }
            dispatch(setDQRPaymentStatus(null));
            dispatch(setMakePayment(false));
            dispatch(setRefrenceNumber(null))
            dispatch(setUnitPaymentMode(paymentModes));
            dispatch(setTotalAmount(totalAmount));
            dispatch(setRemainingAmount(totalAmount));
            dispatch(setPayments([]));
            dispatch(setInputAmount(null));
            dispatch(setEdcExternalTranscationId(null));
            dispatch(setDqrExternalTranscationId(null));
            dispatch(setPaymentType(null))
            dispatch(setSelectedWalletDenomination(0));
            const paymentModeTypes = [];
            let stop = false;
            if (!autoEdcEnabled && !UtilityService.checkEmpty(unitEdcMapping)) {
                unitEdcMapping.forEach(item => {
                    if (parseInt(item.terminalId, 10) === UtilityService.getTerminalId()) {
                        const PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.EDC);
                        paymentModes?.forEach(paymentMode => {
                            if (!stop && PaymentModeMap.indexOf(paymentMode.id) >= 0 && (UtilityService.checkEmpty(appliedCoupon) || (!UtilityService.checkEmpty(appliedCoupon) && paymentMode.applicableOnDiscountedOrders))) {
                                paymentModeTypes.push({ id: 1, name: Constants.PAYMENT_MODES_TYPES.EDC });
                                stop = true;
                            }
                        })

                    }
                });
            }
            else {
                paymentModeTypes.push({ id: 1, name: Constants.PAYMENT_MODES_TYPES.EDC, show: false });
            }
            stop = false;
            let PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.CASH);
            paymentModes?.forEach(paymentMode => {
                if (!stop && PaymentModeMap.indexOf(paymentMode.id) >= 0 && (UtilityService.checkEmpty(appliedCoupon) || (!UtilityService.checkEmpty(appliedCoupon) && paymentMode.applicableOnDiscountedOrders))) {
                    paymentModeTypes.push({ id: 2, name: Constants.PAYMENT_MODES_TYPES.CASH });
                    stop = true;
                }
            })
            stop = false;
            PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.CARD);
            paymentModes?.forEach(paymentMode => {
                if (!stop && PaymentModeMap.indexOf(paymentMode.id) >= 0 && (UtilityService.checkEmpty(appliedCoupon) || (!UtilityService.checkEmpty(appliedCoupon) && paymentMode.applicableOnDiscountedOrders))) {
                    paymentModeTypes.push({ id: 3, name: Constants.PAYMENT_MODES_TYPES.CARD });
                    stop = true;
                }
            })

            if (!isRechargingWallet && !UtilityService.checkEmpty(customer) && customer.walletBalance > 0) {
                stop = false;
                PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.WALLET);
                paymentModes?.forEach(paymentMode => {
                    if (!stop && PaymentModeMap.indexOf(paymentMode.id) >= 0 && (UtilityService.checkEmpty(appliedCoupon) || (!UtilityService.checkEmpty(appliedCoupon) && paymentMode.applicableOnDiscountedOrders))) {
                        paymentModeTypes.push({ id: 4, name: Constants.PAYMENT_MODES_TYPES.WALLET });
                        stop = true;
                    }
                })
            }
            stop = false;
            PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.UPI);
            paymentModes?.forEach(paymentMode => {
                if (!stop && PaymentModeMap.indexOf(paymentMode.id) >= 0 && (UtilityService.checkEmpty(appliedCoupon) || (!UtilityService.checkEmpty(appliedCoupon) && paymentMode.applicableOnDiscountedOrders))) {
                        paymentModeTypes.push({ id: 5, name: Constants.PAYMENT_MODES_TYPES.UPI });
                        stop = true;
                }
            })

            stop = false;
            PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.UPI);
            paymentModes?.forEach(paymentMode => {
                if (!stop && PaymentModeMap.indexOf(paymentMode.id) >= 0 && (UtilityService.checkEmpty(appliedCoupon) || (!UtilityService.checkEmpty(appliedCoupon) && paymentMode.applicableOnDiscountedOrders))) {
                    if (paymentMode.id !== 39 || (paymentMode.id === 39 && !UtilityService.checkEmpty(unitDQRMapping))) {
                        paymentModeTypes.push({ id: 5, name: Constants.PAYMENT_MODES_TYPES.DQR,showOnApp:false });
                        stop = true;
                    }

                }
            })

            if (!isRechargingWallet) {
                stop = false;
                PaymentModeMap = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.OTHERS);
                paymentModes?.forEach(paymentMode => {
                    if (!stop && PaymentModeMap.indexOf(paymentMode.id) >= 0 && (UtilityService.checkEmpty(appliedCoupon) || (!UtilityService.checkEmpty(appliedCoupon) && paymentMode.applicableOnDiscountedOrders))) {
                        paymentModeTypes.push({ id: 6, name: Constants.PAYMENT_MODES_TYPES.OTHERS });
                        stop = true;
                    }
                })
            }

            emitMessage({
                DQR_QR_DATA: { qrData: null }
            });

            emitMessage({
                CLEAR_PAYMENTS_ARR: {}
            })


            dispatch(setUnitPaymentModeTypes(paymentModeTypes));
            dispatch(setEDCIntialTranscationResponse(null));
            if(callback !== null && callback !== undefined){
                callback();
            }

        }

    static autoLinkPaymentIfPresent = () => (dispatch, getState) => {
        const dqrIntialTranscationResponseMap = { ...getState().paymentSlice.DQRIntialTranscationResponse };
        const dqrIntialTranscationResponse = dqrIntialTranscationResponseMap[39] || null;
        const { lastOrderIdForEdcPayment } = getState().paymentSlice;
        const totalAmount = getState().paymentSlice.totalAmount;
        const endTime = new Date().getMinutes();
        // dispatch(setRemainingAmount(totalAmount));
        if (dqrIntialTranscationResponse != null && this.isPaymentModePresent(39, true) && dqrIntialTranscationResponse.totalAmount === totalAmount && (endTime - dqrIntialTranscationResponse.currentTime) <= 5) {
            dispatch(this.addDQRPayment(dqrIntialTranscationResponse));
            dispatch(setStartPollForPaymentStatus(true));
        } else {
            dispatch(setStartPollForPaymentStatus(false));
            dispatch(setDQRIntialTranscationResponse(null));
        }
        if (!UtilityService.checkEmpty(lastOrderIdForEdcPayment)) {
            dispatch(this.linkPayment(lastOrderIdForEdcPayment, () => { }))
        }
    }

    static updateTranscation = (index) =>
        (dispatch, getState) => {
            const payments = getState().paymentSlice.payments;
            const currPayment = payments[index];
            if (index < payments.length - 1) {
                if (currPayment.status !== Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED) {
                    const paymentObj = { ...currPayment, status: Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING }
                    dispatch(updatePayment({ index, payment: paymentObj }));
                    const updatedPaymentsArray = getState().paymentSlice.payments;
                    // emitMessage({
                    //     POS_PAYMENT_MODE_ADDED: {
                    //         Payments: updatedPaymentsArray,
                    //         otpVerificationRequired: false
                    //     }
                    // });
                    setTimeout(() => {
                        const payment = { ...currPayment, status: Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED }
                        dispatch(updatePayment({ index, payment }));
                        dispatch(this.updateTranscation(index + 1))
                        const updatedPayments = getState().paymentSlice.payments;
                        // emitMessage({
                        //     POS_PAYMENT_MODE_ADDED: {
                        //         Payments: updatedPayments,
                        //         otpVerificationRequired: false
                        //     }
                        // });

                    }, 2000)
                } else {
                    dispatch(this.updateTranscation(index + 1))
                }
            } else {
                CustomerAction.applyCcFlatCouponForCustomerIfExists().then((code) => {
                    CartAction.checkout(code, (status) => {
                        const payment = { ...currPayment, status: Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED }
                        dispatch(updatePayment({ index, payment }));
                        const updatedPayments = getState().paymentSlice.payments;
                        // emitMessage({
                        //     POS_PAYMENT_MODE_ADDED: {
                        //         Payments: updatedPayments,
                        //         otpVerificationRequired: false
                        //     }
                        // });
                        dispatch(setMakePayment(!status));
                    });
                    const paymentObj = { ...currPayment, status: Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING }
                    dispatch(updatePayment({ index, payment: paymentObj }));
                    const updatedPaymentsArray = getState().paymentSlice.payments;
                    // emitMessage({
                    //     POS_PAYMENT_MODE_ADDED: {
                    //         Payments: updatedPaymentsArray,
                    //         otpVerificationRequired: false
                    //     }
                    // });
                });
            }

        }

    static initiatEDCPayment = (modeId, amount, callback) =>
        (dispatch, getState) => {
            // dispatch(this.updateEDCValidateStatus(index, false, Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING))
            const unitPaymentMode = getState().paymentSlice.unitPaymentModes;
            let mode = Constants.EDC_PAYMENT_CONSTANTS.CARD;
            const customer = getState().customerSlice.customerBasicInfo
            const unitEdcMapping = getState().metadataSlice.UnitEDCMapping;
            const { EDCIntialTranscationResponse, autoEdcEnabled } = getState().paymentSlice;

            unitPaymentMode.forEach((paymentMode) => {
                if (paymentMode.id === modeId &&
                    paymentMode.type === Constants.EDC_PAYMENT_CONSTANTS.SETTLEMENT_TYPE_UPI) {
                    mode = Constants.EDC_PAYMENT_CONSTANTS.QR
                }
            })

            let currentTerminalEDCMapping = null;

            unitEdcMapping.forEach(item => {
                if (parseInt(item.terminalId, 10) === UtilityService.getTerminalId()) {
                    currentTerminalEDCMapping = item;
                }
            })

            const requestObj = {
                paymentModeId: modeId,
                paymentModeName: mode,
                paidAmount: amount,
                customerId: !UtilityService.checkEmpty(customer) && !UtilityService.checkEmpty(customer.id) ? customer.id : 5,
                customerName: !UtilityService.checkEmpty(customer) && !UtilityService.checkEmpty(customer.name) ? customer.name : "xyz",
                contactNumber: !UtilityService.checkEmpty(customer) && !UtilityService.checkEmpty(customer.contact) ? customer.contact : "2222222222",
                paymentSource: Constants.EDC_PAYMENT_CONSTANTS.PAYMENT_SOURCE,
                paytmMid: !UtilityService.checkEmpty(currentTerminalEDCMapping.merchantId) ? currentTerminalEDCMapping.merchantId : "1",
                paytmTid: !UtilityService.checkEmpty(currentTerminalEDCMapping.tid) ? currentTerminalEDCMapping.tid : "1",
                version: !UtilityService.checkEmpty(currentTerminalEDCMapping.version) ? currentTerminalEDCMapping.version : "3.1",
                merchantKey: !UtilityService.checkEmpty(currentTerminalEDCMapping.merchantKey) ? currentTerminalEDCMapping.merchantKey : "NO_KEY"
            }

            RestService.postJSON(APIConstants.getEndpoints().payments.initiatePayment, requestObj)
                .then(response => {
                    dispatch(setIsAutoEDCInititaionFailed(true));
                    if (!UtilityService.checkEmpty(response) &&
                        !UtilityService.checkEmpty(response.paytmEDCTransactionResponse) &&
                        !UtilityService.checkEmpty(response.paytmEDCTransactionResponse.body) &&
                        !UtilityService.checkEmpty(response.paytmEDCTransactionResponse.body.resultInfo) &&
                        response.paytmEDCTransactionResponse.body.resultInfo.resultCodeId === "0009") {
                        response = { ...response, merchantKey: !UtilityService.checkEmpty(currentTerminalEDCMapping.merchantKey) ? currentTerminalEDCMapping.merchantKey : "NO_KEY" }
                        const initResMap = {
                            ...EDCIntialTranscationResponse,
                            [modeId]: response,
                        }
                        dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.INITIATED));
                        dispatch(setLastOrderIdForEdcPayment(response.merchantTransactionId))
                        dispatch(setEDCIntialTranscationResponse(initResMap));
                        callback(true);
                    } else {
                        if (!autoEdcEnabled) {
                            dispatch(this.paymentError("EDC Payment Cannot initiated , try another Payment Mode"));
                        }
                        dispatch(setDQRPaymentStatus(null));
                        callback(false);
                    }

                }).catch(error => {
                    const response = JSON.parse(error);
                    if (!UtilityService.checkEmpty(response) && !UtilityService.checkEmpty(response.data) && !UtilityService.checkEmpty(response.data.message) && response.data.message === "Payment  Multiple payment request not allowed for same terminal..!") {
                        dispatch(this.paymentError("Multiple Payment request not allowed for same terminal..!"));
                    } else if (!autoEdcEnabled) {
                        dispatch(this.paymentError("EDC Payment Cannot initiated , try another Payment Mode"));
                    }
                    dispatch(setDQRPaymentStatus(null));
                    dispatch(setIsAutoEDCInititaionFailed(true))
                    callback(false);
                })
        }

    static initiatDQRPayment = (modeId, amount, callback) =>
        (dispatch, getState) => {
            const customer = getState().customerSlice.customerBasicInfo;
            const { DQRIntialTranscationResponse } = getState().paymentSlice;
            const unitDQRMapping = getState().metadataSlice.UnitDQRMapping;
            const totalAmount = getState().paymentSlice.totalAmount;
            const requestObj = {
                paymentModeId: modeId,
                paymentModeName: Constants.PAYMENT_MODES_TYPES.DQR,
                paidAmount: amount,
                customerId: !UtilityService.checkEmpty(customer) && !UtilityService.checkEmpty(customer.id) ? customer.id : 5,
                customerName: !UtilityService.checkEmpty(customer) && !UtilityService.checkEmpty(customer.name) ? customer.name : "xyz",
                contactNumber: !UtilityService.checkEmpty(customer) && !UtilityService.checkEmpty(customer.contact) ? customer.contact : "2222222222",
                paymentSource: Constants.EDC_PAYMENT_CONSTANTS.PAYMENT_SOURCE,
                posId: `T${UtilityService.getTerminalId()}_${UtilityService.getUnitId()}`,
                merchantKey: !UtilityService.checkEmpty(unitDQRMapping) && !UtilityService.checkEmpty(unitDQRMapping[0].merchantKey) ? unitDQRMapping[0].merchantKey : "NO_KEY",
                merchantId: !UtilityService.checkEmpty(unitDQRMapping) && !UtilityService.checkEmpty(unitDQRMapping[0].merchantId) ? unitDQRMapping[0].merchantId : "NO_KEY"

            }
            RestService.postJSON(APIConstants.getEndpoints().payments.initiateDQRPayment, requestObj)
                .then(response => {
                    // dispatch(setIsAutoDQRInititaionFailed(true));
                    // dispatch(setIsAutoEDCInititaionFailed(true));
                    if (!UtilityService.checkEmpty(response?.paytmDQRTransactionResponse?.body?.resultInfo) &&
                        response.paytmDQRTransactionResponse.body.resultInfo.resultCode === "QR_0001") {
                        const lastResponse = { ...response, totalAmount, currentTime: new Date().getMinutes() }
                        const initResMap = {
                            ...DQRIntialTranscationResponse,
                            [modeId]: lastResponse,
                        }
                        dispatch(setDQRIntialTranscationResponse(initResMap));
                        dispatch(setStartPollForPaymentStatus(true))
                        dispatch(setLastOrderIdForEdcPayment(response.orderId))
                        dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.INITIATED));
                        callback(true, response.paytmDQRTransactionResponse.body.qrData);
                    } else {
                        dispatch(setDQRPaymentStatus(null));
                        callback(false)
                        // TODO handle failure
                    }
                    dispatch(setShowDQRPayment(false))
                }).catch(error => {
                    // dispatch(setIsAutoDQRInititaionFailed(true));
                    // dispatch(setIsAutoEDCInititaionFailed(true));
                    dispatch(setShowDQRPayment(false));
                    dispatch(setDQRPaymentStatus(null));
                    // const response = JSON.parse(error);
                    // TODO handle failure
                    callback(false);
                })
        }


    static validateEdcPayment = (index, modeId) =>
        (dispatch, getState) => {
            const payments = getState().paymentSlice.payments;
            const currPayment = payments[index];
            dispatch(this.updateEDCValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.PROCESSING))
            const edcIntialTranscationResponseMap = { ...getState().paymentSlice.EDCIntialTranscationResponse };

            const edcIntialTranscationResponse = edcIntialTranscationResponseMap[modeId];
            dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.CHECKING));

            const request = {
                paytmMid: edcIntialTranscationResponse.paytmMid,
                paytmTid: edcIntialTranscationResponse.paytmTid,
                merchantKey: edcIntialTranscationResponse.merchantKey,
                merchantTransactionId: edcIntialTranscationResponse.merchantTransactionId,
                paytmEDCTransactionResponse: edcIntialTranscationResponse.paytmEDCTransactionResponse
            }
            RestService.postJSON(APIConstants.getEndpoints().payments.checkEdcTransactionStatus, request)
                .then(response => {
                    if (!UtilityService.checkEmpty(response) &&
                        !UtilityService.checkEmpty(response.body) &&
                        !UtilityService.checkEmpty(response.body.resultInfo) &&
                        response.body.resultInfo.resultCodeId === "0000") {
                        dispatch(this.updateEDCValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED))
                        delete edcIntialTranscationResponseMap[modeId];
                        dispatch(setEDCIntialTranscationResponse(edcIntialTranscationResponseMap));
                        const edcExternalTransactionId = {
                            id: response.body.merchantTransactionId,
                            amount: parseInt(edcIntialTranscationResponse.transactionAmount, 10) / 100
                        }
                        const payload = {
                            key: currPayment.id,
                            value: edcExternalTransactionId
                        }
                        dispatch(setEdcExternalTranscationId(payload));
                        dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.SUCCESS));
                        emitMessage({ EDC_PAYMENT_STATUS: { status: Constants.DQR_STATUS.SUCCESS } });
                    } else {
                        if(response.body.resultInfo.resultStatus === "PENDING"){
                            dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.PENDING));
                        }
                        dispatch(this.paymentError(`Payment Status : ${response.body.resultInfo.resultStatus}`))
                        dispatch(this.updateEDCValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED))
                    }

                }).catch(error => {
                    dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.FAILED));
                    dispatch(this.paymentError(`Payment Status Validation Failure`))
                    dispatch(this.updateEDCValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED))
                })
        }

    static validateDQRPayment = (index, modeId) =>
        (dispatch, getState) => {
            const payments = getState().paymentSlice.payments;
            const dqrPaymentStatus = getState().paymentSlice.dqrPaymentStatus;
            const startPollForPaymentStatus = getState().paymentSlice.startPollForPaymentStatus;

            if(dqrPaymentStatus === Constants.DQR_STATUS.FAILED){
                dispatch(setStartPollForPaymentStatus(false))
                return;
            }

            const currPayment = payments[index];
            const dqrIntialTranscationResponseMap = { ...getState().paymentSlice.DQRIntialTranscationResponse };
            const dqrIntialTranscationResponse = dqrIntialTranscationResponseMap[modeId];
            const unitDQRMapping = getState().metadataSlice.UnitDQRMapping;

            const request = {
                orderId: dqrIntialTranscationResponse.orderId,
                paytmEDCTransactionResponse: dqrIntialTranscationResponse.paytmDQRTransactionResponse,
                merchantKey: !UtilityService.checkEmpty(unitDQRMapping) && !UtilityService.checkEmpty(unitDQRMapping[0].merchantKey) ? unitDQRMapping[0].merchantKey : "NO_KEY",
                merchantId: !UtilityService.checkEmpty(unitDQRMapping) && !UtilityService.checkEmpty(unitDQRMapping[0].merchantId) ? unitDQRMapping[0].merchantId : "NO_KEY"
            }
            RestService.postJSON(APIConstants.getEndpoints().payments.validateDQRPayment, request)
                .then(response => {
                    if (!UtilityService.checkEmpty(response?.body?.resultInfo.resultStatus)) {
                        if (response.body.resultInfo.resultStatus === "TXN_SUCCESS") {
                            dispatch(this.updateDQRValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED, (p) => {
                                emitMessage({
                                    POS_PAYMENT_MODE_ADDED: {
                                        Payments: p,
                                        otpVerificationRequired: false
                                    }
                                });
                            }))
                            // delete dqrIntialTranscationResponse[modeId];
                            dispatch(setDQRIntialTranscationResponse(null));
                            const dqrExternalTransactionId = {
                                id: response.body.orderId,
                                amount: parseInt(dqrIntialTranscationResponse.amount, 10)
                            }
                            const payload = {
                                key: currPayment.id,
                                value: dqrExternalTransactionId
                            }
                            dispatch(setDqrExternalTranscationId(payload));
                            dispatch(setStartPollForPaymentStatus(false))
                            emitMessage({ DQR_PAYMENT_STATUS: { status: Constants.DQR_STATUS.SUCCESS } });
                            dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.SUCCESS));
                        } else if (response.body.resultInfo.resultStatus === "PENDING") {
                            emitMessage({ DQR_PAYMENT_STATUS: { status: Constants.DQR_STATUS.PENDING } });
                            dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.PENDING));
                            if(!startPollForPaymentStatus){
                                dispatch(this.paymentError(`Payment Status : ${response.body.resultInfo.resultStatus}`))
                                dispatch(this.updateDQRValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED))
                            }
                        } else {
                            emitMessage({ DQR_PAYMENT_STATUS: { status: Constants.DQR_STATUS.FAILED  } });
                            dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.FAILED));
                            dispatch(setStartPollForPaymentStatus(false));
                            dispatch(setDQRIntialTranscationResponse(null));
                            dispatch(this.paymentError("Payment Status : Failed"));
                            dispatch(this.updateDQRValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED))
                        }
                    } else {
                        emitMessage({ DQR_PAYMENT_STATUS: { status: Constants.DQR_STATUS.FAILED  } });
                        dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.FAILED));
                        dispatch(setStartPollForPaymentStatus(false))
                        dispatch(setDQRIntialTranscationResponse(null));
                        dispatch(this.paymentError("Payment Status : Failed"));
                        dispatch(this.updateDQRValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED))
                    }
                }).catch(error => {
                    emitMessage({ DQR_PAYMENT_STATUS: { status: Constants.DQR_STATUS.FAILED  } });
                    dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.FAILED));
                    dispatch(setStartPollForPaymentStatus(false));
                    dispatch(setDQRIntialTranscationResponse(null));
                    dispatch(this.paymentError(`Payment Status Validation Failure`))
                    dispatch(this.updateDQRValidateStatus(index, true, Constants.PAYMENT_MODE_PROCESSING_STATUS.NOT_PROCESSED))
                })
        }

    static checkPayBillStatus = () =>
        (dispatch, getState) => {
            const payments = getState().paymentSlice.payments;
            const remainingAmount = getState().paymentSlice.remainingAmount;
            const customer = getState().customerSlice.customerBasicInfo;
            const { currentWalletSuggestion } = getState().orderSlice;
            const transactionDetail = getState().cartSlice.cart.transactionDetail;
            if (remainingAmount > 0 || remainingAmount < 0) {
                return true;
            }
            for (let i = 0; i < payments.length; i += 1) {
                if ([35, 36, 37].includes(payments[i].id) && payments[i].status !== Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED) {
                    return true;
                }
                if (payments[i].id === 39 && payments[i].status !== Constants.PAYMENT_MODE_PROCESSING_STATUS.COMPLETED) {
                    return true;
                }
                if (payments[i].paymentModeType === Constants.PAYMENT_MODES_TYPES.WALLET && !customer.otpVerified && (UtilityService.checkEmpty(currentWalletSuggestion) || currentWalletSuggestion?.extraPay < transactionDetail.paidAmount)) {
                    return true;
                }
                if (payments[i].isSwitch) {
                    return true;
                }
            }
            return false;


        }

    static updateEDCValidateStatus = (index, status, paymentState) =>
        (dispatch, getState) => {
            const payments = getState().paymentSlice.payments;
            const currEDCPayment = payments[index];
            const updatedPayment = { id: currEDCPayment.id, amount: currEDCPayment.amount, paymentModeType: currEDCPayment.paymentModeType, description: currEDCPayment.description, validateStatus: status, status: paymentState, isSwitch: false }
            dispatch(updatePayment({ index, payment: updatedPayment }));
        }

    static updateDQRValidateStatus = (index, status, paymentState, callBack) =>
        (dispatch, getState) => {
            const payments = JSON.parse(JSON.stringify(getState().paymentSlice.payments));
            const currDQRPayment = payments[index];
            const updatedPayment = { id: currDQRPayment.id, amount: currDQRPayment.amount, paymentModeType: currDQRPayment.paymentModeType, description: currDQRPayment.description, validateStatus: status, status: paymentState, isSwitch: false }
            dispatch(updatePayment({ index, payment: updatedPayment }));
            if (callBack != null) {
                payments[index] = updatedPayment;
                callBack(payments)
            }
        }

    static linkPayment = (transactionId, callBack) =>
        (dispatch, getState) => {
            const url = `${APIConstants.getEndpoints().payments.validateTransactionId}/${transactionId}`;
            RestService.getJSON(url)
                .then(response => {
                    if (!UtilityService.checkEmpty(response.data)) {
                        if (!UtilityService.checkEmpty(response.data.message)) {
                            callBack(false);
                        }
                        if (!UtilityService.checkEmpty(response.data) &&
                            !UtilityService.checkEmpty(response.data.paymentSource) &&
                            response.data.paymentSource === "KETTLE_SERVICE" &&
                            !UtilityService.checkEmpty(response.data.paymentStatus) &&
                            (response.data.paymentStatus === "ACCEPTED_SUCCESS" ||
                                response.data.paymentStatus === "SUCCESS") &&
                            !UtilityService.checkEmpty(response.data.transactionAmount) &&
                            response.data.transactionAmount > 0
                        ) {
                            const unitEdcMapping = getState().metadataSlice.UnitEDCMapping;
                            let currentTerminalEDCMapping = null;
                            unitEdcMapping?.forEach(item => {
                                if (parseInt(item.terminalId, 10) === UtilityService.getTerminalId()) {
                                    currentTerminalEDCMapping = item;
                                }
                            })
                            let reqObj = {
                                "paytmMid": !UtilityService.checkEmpty(currentTerminalEDCMapping?.merchantId) ? currentTerminalEDCMapping.merchantId : "1",
                                "paytmTid": !UtilityService.checkEmpty(currentTerminalEDCMapping?.tid) ? currentTerminalEDCMapping.tid : "1",
                                "version": !UtilityService.checkEmpty(currentTerminalEDCMapping?.version) ? currentTerminalEDCMapping.version : "3.1",
                                "merchantKey": !UtilityService.checkEmpty(currentTerminalEDCMapping?.merchantKey) ? currentTerminalEDCMapping.merchantKey : "NO_KEY",
                                "merchantTransactionId": transactionId,
                                "paytmEDCTransactionResponse": {
                                    "head": {
                                        "requestTimeStamp": moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
                                        "channelId": "EDC",
                                        "checksum": "",
                                        "version": ""
                                    },
                                    "body": {
                                        "paytmMid": !UtilityService.checkEmpty(currentTerminalEDCMapping?.merchantId) ? currentTerminalEDCMapping.merchantId : "1",
                                        "paytmTid": !UtilityService.checkEmpty(currentTerminalEDCMapping?.merchantKey) ? currentTerminalEDCMapping.merchantKey : "1",
                                        "transactionDateTime": null,
                                        "merchantTransactionId": transactionId,
                                        "merchantReferenceNo": null,
                                        "transactionAmount": null,
                                        "acquirementId": null,
                                        "retrievalReferenceNo": null,
                                        "authCode": null,
                                        "issuerMaskCardNo": null,
                                        "issuingBankName": null,
                                        "bankResponseCode": null,
                                        "bankResponseMessage": null,
                                        "bankMid": null,
                                        "bankTid": null,
                                        "acquiringBank": null,
                                        "merchantExtendedInfo": null,
                                        "extendedInfo": null,
                                        "aid": null,
                                        "payMethod": null,
                                        "cardType": null,
                                        "cardScheme": null,
                                        "resultInfo": {
                                            "resultStatus": "ACCEPTED_SUCCESS",
                                            "resultCodeId": "0009",
                                            "resultCode": "A",
                                            "resultMsg": "ACCEPTED_SUCCESS"
                                        }
                                    }
                                }
                            }
                            const isDQRPayment = response.data.paymentModeName === Constants.PAYMENT_MODES_TYPES.DQR;
                            let validateUrl = APIConstants.getEndpoints().payments.checkEdcTransactionStatus;
                            if (isDQRPayment) {
                                const unitDQRMapping = getState().metadataSlice.UnitDQRMapping;
                                validateUrl = APIConstants.getEndpoints().payments.validateDQRPayment;
                                reqObj = {
                                    orderId: transactionId,
                                    merchantKey: !UtilityService.checkEmpty(unitDQRMapping) && !UtilityService.checkEmpty(unitDQRMapping[0].merchantKey) ? unitDQRMapping[0].merchantKey : "NO_KEY",
                                    merchantId: !UtilityService.checkEmpty(unitDQRMapping) && !UtilityService.checkEmpty(unitDQRMapping[0].merchantId) ? unitDQRMapping[0].merchantId : "NO_KEY"
                                }
                            }
                            RestService.postJSON(validateUrl, reqObj)
                                .then(validationResponse => {
                                    if (!UtilityService.checkEmpty(validationResponse?.body?.resultInfo)
                                        && (validationResponse.body.resultInfo.resultCodeId === "0000"
                                            || validationResponse.body.resultInfo.resultCode === "01") &&
                                        (isDQRPayment || !UtilityService.checkEmpty(validationResponse.body.merchantTransactionId))) {
                                        const unitPaymentModes = getState().paymentSlice.unitPaymentModes;
                                        let description = null;
                                        let modeType = null;
                                        unitPaymentModes.forEach(paymentMode => {
                                            if (paymentMode.id === response.data.paymentModeId) {
                                                description = paymentMode.description
                                                if(paymentMode.id === 39){
                                                    modeType = Constants.PAYMENT_MODES_TYPES.UPI;
                                                    description = "On screen QR"
                                                }else if(paymentMode.id === 37){
                                                    modeType = Constants.PAYMENT_MODES_TYPES.UPI;
                                                    description = "EDC Machine QR"
                                                }else if([35,36].includes(paymentMode.id)){
                                                    modeType = Constants.PAYMENT_MODES_TYPES.CARD
                                                }
                                            }
                                        });
                                        dispatch(setInputAmount(null));
                                        if(isDQRPayment){
                                            dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.SUCCESS));
                                        }
                                        dispatch(this.addPaymentMode(modeType, response.data.transactionAmount, description, response.data.paymentModeId, true, null, (status) => {
                                            if (status) {
                                                if (isDQRPayment) {
                                                    const dqrExternalTransactionId = {
                                                        id: validationResponse.body.orderId,
                                                        amount: parseInt(validationResponse.body.txnAmount, 10)
                                                    }
                                                    const payload = {
                                                        key: response.data.paymentModeId,
                                                        value: dqrExternalTransactionId
                                                    }
                                                    dispatch(setDqrExternalTranscationId(payload));
                                                } else {
                                                    const edcExternalTransactionId = {
                                                        id: validationResponse.body.merchantTransactionId,
                                                        amount: parseInt(response.data.transactionAmount, 10)
                                                    }
                                                    const payload = {
                                                        key: response.data.paymentModeId,
                                                        value: edcExternalTransactionId
                                                    }
                                                    dispatch(setEdcExternalTranscationId(payload));
                                                }
                                                callBack(true);
                                            } else {
                                                callBack(false);
                                            }
                                        }))

                                    }
                                    else {
                                        callBack(false);

                                    }
                                }).catch(error => {
                                    callBack(false);

                                })

                        } else {
                            callBack(false);

                        }

                    } else {
                        callBack(false);

                    }
                }).catch(error => {
                    callBack(false);

                });
        }

    /**
     * wallet suggestion payment confirmed
     * @returns
     */
    static walletSuggestionPayment = (src) =>
        (dispatch, getState) => {
            console.log("---- ---- wallet suggestion ---- ----", src);
            let unitPaymentModeTypes = [...getState().paymentSlice.unitPaymentModeTypes];
            unitPaymentModeTypes = unitPaymentModeTypes.filter(paymentMode => paymentMode.name !== Constants.PAYMENT_MODES_TYPES.WALLET)
            dispatch(setRefrenceNumber(null));
            dispatch(setUnitPaymentModeTypes(unitPaymentModeTypes));
            dispatch(setCreWalletSuggestionResponse(true));
            dispatch(setIsWalletOrder(true));
            dispatch(setRightSideBar({ open: false }));
            const { currentWalletSuggestion } = getState().orderSlice;
            const transactionDetail = getState().cartSlice.cart.transactionDetail;
            const customer = getState().customerSlice.customerBasicInfo;
            let totalAmount = currentWalletSuggestion.suggestedAmount;
            if (!UtilityService.checkEmpty(customer) && customer.walletBalance > 0) {
                if (customer.walletBalance > transactionDetail.paidAmount) {
                    totalAmount = transactionDetail.paidAmount + currentWalletSuggestion.suggestedAmount;

                }
                else {
                    totalAmount = customer.walletBalance + currentWalletSuggestion.suggestedAmount;

                }
            }
            emitMessage({
                OPEN_PAYMENT_SCREEN: {
                    totalAmount:currentWalletSuggestion.suggestedAmount,
                    PaymentModeTypes: unitPaymentModeTypes,
                    PaymentType: Constants.PaymentType.WITHWALLET,
                    isWalletPayment: customer.walletBalance > 0
                }
            });
            dispatch(setTotalAmount(totalAmount));
            dispatch(setRemainingAmount(totalAmount));
            dispatch(setPaymentType(Constants.PaymentType.WITHWALLET));
            const PaymentModesWallet = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.WALLET);
            if (!UtilityService.checkEmpty(customer)) {
                if (customer.walletBalance > transactionDetail.paidAmount) {
                    dispatch(this.addPaymentMode(Constants.PAYMENT_MODES_TYPES.WALLET, transactionDetail.paidAmount, null, PaymentModesWallet[0], false, null, null, false))
                }
                else if (customer.walletBalance > 0) {
                    dispatch(this.addPaymentMode(Constants.PAYMENT_MODES_TYPES.WALLET, customer.walletBalance, null, PaymentModesWallet[0], false, null, null, false))

                }
            }

            dispatch(this.autoLinkPaymentIfPresent());

            //  Goal Tracking
            if (src === 'customer') {
                ProgressTabButtonsAction.updateBoolInPushJson('walletPurchasedByCustomer');
                ProgressTabButtonsAction.updateBoolInPushJson('walletPurchasedByCRE', false);
                ProgressTabButtonsAction.keyIncrementer(['walletsPurchasedByCus']);
                ProgressTabButtonsAction.keyResetter(['walletsPurchasedByCRE']);
            } else {
                ProgressTabButtonsAction.updateBoolInPushJson('walletPurchasedByCRE');
                ProgressTabButtonsAction.updateBoolInPushJson('walletPurchasedByCustomer', false);
                ProgressTabButtonsAction.keyIncrementer(['walletsPurchasedByCRE']);
                ProgressTabButtonsAction.keyResetter(['walletsPurchasedByCus']);
            }
        }

    static paymentError = (message, waitFor) =>
        (dispatch) => {
            dispatch(UtilityService.showSnackBar({
                open: true,
                snackType: Constants.SNACK_TYPE.ERROR,
                message,
                autoHideDuration: waitFor || 3000
            }));
        }

    static paymentSuccesMessage = (message) =>
        (dispatch) => {
            dispatch(UtilityService.showSnackBar({
                open: true,
                snackType: Constants.SNACK_TYPE.SUCCESS,
                message,
            }));
        }

    static addPaymentLoyatea = () =>
        (dispatch, getState) => {
            const unitPaymentMode = getState().metadataSlice.unitDetail.unitPaymentModes;
            const transactionDetail = getState().cartSlice.cart.transactionDetail;
            const totalAmount = transactionDetail.paidAmount;
            const PaymentModesWallet = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.CASH);
            if (totalAmount === 0 && unitPaymentMode.filter(paymetMode => paymetMode.id === PaymentModesWallet[0]).length > 0) {
                dispatch(this.addPaymentMode(Constants.PAYMENT_MODES_TYPES.CASH, 0, null, PaymentModesWallet[0], false, null, null))
            }
        }

    static directPayment = () =>
        (dispatch, getState) => {
            const transactionDetail = CartAction.getOrderInfo().transactionDetail;
            const customer = getState().customerSlice.customerBasicInfo;
            const unitPaymentModeTypes = getState().paymentSlice.unitPaymentModeTypes;
            const totalAmount = getState().paymentSlice.totalAmount;
            dispatch(setPayments([]));

            dispatch(this.autoLinkPaymentIfPresent());

            if (UtilityService.checkEmpty(customer?.name) &&
                CommunicationService.getOrderType() !== Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER &&
                CommunicationService.getOrderType() !== Constants.ORDER_TYPE.WASTAGE_ORDER &&
                CommunicationService.getOrderType() !== Constants.ORDER_TYPE.COMPLIMENTRY_ORDER
            ) {
                dispatch(setRightSideBar({
                    open: true, code: Constants.RIGTHSIDE_BAR.CUSTOMER_DETAILS,
                    data: {
                        totalAmount,
                        OPEN_PAYMENT_SCREEN: {
                            totalAmount: transactionDetail.paidAmount,
                            PaymentModeTypes: unitPaymentModeTypes,
                            isWalletPayment: false
                        }
                    }
                }))
            }
            else {
                dispatch(setIsWalletOrder(false));
                if (totalAmount >= 0) {
                    emitMessage({
                        OPEN_PAYMENT_SCREEN: {
                            totalAmount: transactionDetail.paidAmount,
                            PaymentModeTypes: unitPaymentModeTypes,
                            isWalletPayment: totalAmount === 0
                        }
                    });
                }
                dispatch(setPaymentType(Constants.PaymentType.WITHOUTWALLET));
                dispatch(this.addPaymentLoyatea());
            }
            // dispatch(setDroolsData({ ...droolsData, WALLET_SECTION: [] }));
        }

    static updatePaymentSwitchStatus = (val, index) =>
        (dispatch, getState) => {
            const payments = getState().paymentSlice.payments;
            const currPayment = payments[index];
            const updatedPayment = { id: currPayment.id, amount: currPayment.amount, paymentModeType: currPayment.paymentModeType, description: currPayment.description, validateStatus: currPayment.validateStatus, status: currPayment.status, isSwitch: val }
            dispatch(updatePayment({ index, payment: updatedPayment }));
        }

    static searchPayments = (callback) =>
        (dispatch, getState) => {
            const customer = getState().customerSlice.customerBasicInfo;
            RestService.postJSON(APIConstants.getEndpoints().payments.getHangingPayments, customer.contact)
                .then(response => {
                    if (!UtilityService.checkEmpty(response)) {
                        dispatch(setHangingPayments(response));
                        callback(true)
                    } else {
                        dispatch(this.paymentError("No Result Found"));
                        callback(false)
                    }

                }).catch(error => {
                    dispatch(this.paymentError("No Result Found"));
                    callback(false)
                })
        }

    static addHangingPayment = (payment, callback) =>
        (dispatch, getState) => {
            dispatch(this.addPaymentMode(Constants.PAYMENT_MODES_TYPES.APP, payment.transactionAmount, payment.paymentPartner, payment.paymentModeId, true,
                {
                    externalOrderId: payment.externalOrderId,
                    orderPaymentDetailId: payment.orderPaymentDetailId
                }
                , callback))

        }

    static sendWalletSuggestionResponse = (response) =>
        (dispatch, getState) => {
            const transactionDetail = CartAction.getOrderInfo().transactionDetail;
            const paymentModes = getState().paymentSlice.unitPaymentModeTypes;

            emitMessage({
                WALLET_SUGGESTION_DRAWER_RESPONSE: {
                    response,
                    paymentModes,
                    totalAmount: transactionDetail.paidAmount,
                }
            });
        }

    static payViaWallet = () =>
        (dispatch, getState) => {
            const transactionDetail = CartAction.getOrderInfo().transactionDetail;
            const paymentModes = getState().paymentSlice.unitPaymentModeTypes;
            const PaymentModesWallet = UtilityService.getPaymentModeIdsByModeType(Constants.PAYMENT_MODES_TYPES.WALLET);
            emitMessage({
                OPEN_PAYMENT_SCREEN: {
                    totalAmount: transactionDetail.paidAmount,
                    PaymentModeTypes: paymentModes,
                    PaymentType: Constants.PaymentType.WITHOUTWALLET,
                    isWalletPayment: true
                }
            });
            dispatch(this.addPaymentMode(Constants.PAYMENT_MODES_TYPES.WALLET, transactionDetail.paidAmount, null, PaymentModesWallet[0], false, null, null, true));
            dispatch(setDQRIntialTranscationResponse(null));
            dispatch(setIsWalletOrder(false));
            dispatch(setPaymentType(Constants.PaymentType.WITHOUTWALLET));
        }

    static addDQRPayment = (response) =>
        (dispatch, getState) => {
            const DQRData = response.paytmDQRTransactionResponse.body.qrData;

            const paymentMode = dispatch(this.getPaymentModeDetailByModeID(39));

            dispatch(this.addPaymentMode(Constants.PAYMENT_MODES_TYPES.UPI, parseInt(response.amount, 10), paymentMode.description, 39, false, null, null, true, false));
            dispatch(setDQRPaymentStatus(Constants.DQR_STATUS.INITIATED));
            emitMessage({
                DQR_QR_DATA: { qrData: DQRData }
            });
        }




}

export default PaymentActions;