import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  productBasicDetail: null,
  productCategory: null,
  catIdMap: {},
  unitSubscriptionProducts: [],
  productImageMap: null,
  productCustomization: null,
  productPrice: null,
  generatedOrderId: null,
  productInventory: null,
  unitMenu: null,
  unitMenuDetails:null,
  walletDenominationOffer:null,
  directWalletOffer:null,
  walletSuggestionData:null,
  currentWalletSuggestion:null,
  currentWalletSuggestionDummy:null,
  walletSuggestionTimeDetails:{},
  orderTimeDetail:null,
  isWalletOrder:false,
  recommendedProductData:{},
  recommendedProductDataIndex:{}  ,
  nextOffer:null,
  lastNOrders:[],
  isLoadingProducts:false,
  isCustomisingFromCartScreen:false,
  dimSelectedFromCart:"",
  paidAddonSelectedFromCart:{},
  recomOfferData: {
    // "resultAPC": 500,
    // "discountType": "PERCENTAGE",
    // "discountValue": 40,
    // "offerTag": "FLAT 40% OFF",
    // "productIds": [10,80,130,151,992,20,1201,660,1109,460,1028]
  },
   claimedRecomProductData :{},
  customizationTimeDetails:{},
  cashbackAwardedAmount:0,
  ccFlatCoupon:{
    value : false ,
    couponCode : null
  },
  exploreMoreOptionsProducts:[],
  emoselectedProductId:null,
  exploreMoreOptionsCartIndexMap:null,
  allExploreMoreOptionsProductList:{},
  allSavedChaiRecommendationsList:{},
  currentMenuCategory:0,
  showRecomAnimation:false,
  isServiceChargeApplied:true,
  removeServiceChargeBtn:false,
  serviceChargeRemovedKey: null,
  isRecommendationInCart: false,
  cartRulesData:null,
  };

const slice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    setProductBasicDetail(state, action) {
      state.productBasicDetail = action.payload;
    },
    setProductCategory(state, action) {
      state.productCategory = action.payload;
    },
    setCatIdMap(state, action) {
      state.catIdMap = action.payload;
    },
    setUnitSubscriptionProducts(state, action) {
      state.unitSubscriptionProducts = action.payload;
    },
    setProductImageMap(state, action) {
      state.productImageMap = action.payload;
    },
    setProductCustomization(state, action) {
      state.productCustomization = action.payload;
    },
    setProductPrice(state, action) {
      state.productPrice = action.payload;
    },
    setGeneratedOrderId(state, action) {
      state.generatedOrderId = action.payload;
    },
    setProductInventory(state, action) {
      state.productInventory = action.payload;
    },
    setUnitMenu(state, action) {
      state.unitMenu = action.payload;
    },
    setUnitMenuDetails(state,action){
      state.unitMenuDetails = action.payload;
    },
    setWalletDenominationOffer(state, action) {
      state.walletDenominationOffer = action.payload;
    },
    setDirectWalletOffer(state, action) {
      state.directWalletOffer = action.payload;
    },
    setWalletSuggestionData(state, action) {
      state.walletSuggestionData = action.payload;
    },
    setCurrentWalletSuggestion(state, action) {
      state.currentWalletSuggestion = action.payload;
    },
    setOrderTimeDetail(state, action){
      state.orderTimeDetail = action.payload;
    },
    setIsWalletOrder(state, action){
      state.isWalletOrder = action.payload;
    },
    setRecommendedProductData(state, action){
      state.recommendedProductData = action.payload; 
    },
    setRecommendedProductDataIndex(state, action){
      state.recommendedProductDataIndex = action.payload; 
    },
    updateNextOffer(state, action){
      state.nextOffer = action.payload;
    },
    setLastNOrders(state, action){
      state.lastNOrders = action.payload; 
    },
    setIsLoadingProducts(state, action){
      state.isLoadingProducts = action.payload;
    },
    setRecomOfferData(state, action){
      state.recomOfferData = action.payload;
    },
    setWalletSuggestionStartTime(state, action)
    {
      state.walletSuggestionTimeDetails.startTime = action.payload;
    },
    setWalletSuggestionEndTime(state, action)
    {
      state.walletSuggestionTimeDetails.endTime = action.payload;
    },
    setCustomizationStartTime(state, action){
      state.customizationTimeDetails.startTime = action.payload;
    },
    setCustomizationEndTime(state, action){
      state.customizationTimeDetails.startTime = action.payload;
    },
    setClaimedRecomProductData(state, action)
    {
      state.claimedRecomProductData = action.payload;
    },
    setCashbackAwardedAmount(state, action)
    {
      state.cashbackAwardedAmount = action.payload;
    },
    setIsCustomisingFromCartScreen(state, action) {
      state.isCustomisingFromCartScreen = action.payload
    },
    setDimSelectedFromCart(state, action) {
      state.dimSelectedFromCart = action.payload
    },
    setPaidAddonSelectedFromCart(state, action) {
      state.paidAddonSelectedFromCart = action.payload
    },
    setCurrentWalletSuggestionDummy(state, action) {
      state.currentWalletSuggestionDummy = action.payload
    },
    setCcFlatCoupon(state, action) {
      state.ccFlatCoupon = action.payload;
    },
    setExploreMoreOptionsProducts(state,action){
      state.exploreMoreOptionsProducts = action.payload
    },
    setexoSelectedProductId(state,action){
      state.emoselectedProductId = action.payload
    },
    setExploreMoreOptionCartIndexMap(state,action){
      state.exploreMoreOptionsCartIndexMap = action.payload
    },
    setAllExploreMoreOptionsProductList(state,action){
      state.allExploreMoreOptionsProductList = action.payload;
    },
    setCurrentMenuCategory(state, action){
      state.currentMenuCategory = action.payload;
    }
    ,setAllSavedChaiRecommendationsList(state,action){
      state.allSavedChaiRecommendationsList = action.payload;
    },
    setShowRecomAnimation(state,action){
      state.showRecomAnimation = action.payload
    },
    setIsServiceChargeApplied(state,action){
      state.isServiceChargeApplied = action.payload
    },
    setRemoveServiceChargeBtn(state,action){
      state.removeServiceChargeBtn = action.payload;
    },
    setServiceChargeRemovedKey(state, action) {
      state.serviceChargeRemovedKey = action.payload;
    },
    setIsRecommendationInCart(state, action) {
      state.isRecommendationInCart = action.payload;
    },
    setCartRulesData(state, action) {
      state.cartRulesData = action.payload;
    }
  },
});
export const {
  setProductBasicDetail,
  setProductCategory,
  setUnitSubscriptionProducts,
  setCatIdMap,
  setProductImageMap,
  setProductCustomization,
  setProductPrice,
  setGeneratedOrderId,
  setProductInventory,
  setUnitMenu,
  setWalletDenominationOffer,
  setWalletSuggestionData,
  setCurrentWalletSuggestion,
  setOrderTimeDetail,
  setIsWalletOrder,
  setRecommendedProductData,
  setRecommendedProductDataIndex,
  updateNextOffer,
  setLastNOrders,
  setIsLoadingProducts,
  setRecomOfferData,
  setDirectWalletOffer,
  setWalletSuggestionStartTime,
  setWalletSuggestionEndTime,
  setCustomizationStartTime,
  setCustomizationEndTime,
  setClaimedRecomProductData,
  setIsCustomisingFromCartScreen,
  setDimSelectedFromCart,
  setPaidAddonSelectedFromCart,
  setCashbackAwardedAmount,
  setCurrentWalletSuggestionDummy,
  setCcFlatCoupon,
  setExploreMoreOptionsProducts,
  setexoSelectedProductId,
  setExploreMoreOptionCartIndexMap,
  setAllExploreMoreOptionsProductList,
  setCurrentMenuCategory,
  setUnitMenuDetails,
  setAllSavedChaiRecommendationsList,
  setShowRecomAnimation,
  setIsServiceChargeApplied,
  setRemoveServiceChargeBtn,
  setServiceChargeRemovedKey,
  setIsRecommendationInCart,
  setCartRulesData
} = slice.actions;

export default slice.reducer;
