/* eslint-disable import/no-cycle */
import UtilityService from '../../services/UtilityService';
import * as OrderSlice from './OrderSlice';
import RestService from '../../services/RestService';
import APIConstants from '../../utils/APIConstants';
import { store } from '../../redux/store';
import StorageService from '../../services/StorageService';
import Constants from '../../utils/Constants';
import { updateProductsQty } from '../../sections/cart/CartSlice';
import CartAction from '../../sections/cart/CartAction';
import { setMenuUpdated } from '../../redux/MetadataSlice';
import { emitMessage } from '../../cafeAppCommunication/cafeAppCommunication';
import LoginActions from '../../pages/auth/LoginActions';
import * as MetadataSlice from '../../redux/MetadataSlice';
import { recommendationService } from '../../services/RecommendationService';

class OrderAction {
  static loadProductDetail = () => (dispatch) => {
    const currentUnit =  StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.CURRENT_UNIT);
    const unitId = UtilityService.getUnitId();
    if(currentUnit !== unitId)
    {
      StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.MENU_SEQUENCE);
      StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_BASIC_DETAIL);
      StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_CATEGORY);
      StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_CUSTOMISATION);
      StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_IMAGE_MAP);
      StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_PRICE);
    }
    dispatch(this.loadProductBasicDetail());
    dispatch(this.loadProductCategory());
    dispatch(this.loadProductImageMap());
    dispatch(this.loadProductCustomization());
    dispatch(this.loadProductPrice());
    dispatch(this.loadUnitMenu());
    dispatch(LoginActions.getAllCafeAppProperties());

  };

  static reloadProductDetail = () => (dispatch) => {

    StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.MENU_SEQUENCE);
    StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_BASIC_DETAIL);
    StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_CATEGORY);
    StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_CUSTOMISATION);
    StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_IMAGE_MAP);
    StorageService.deleteKey(Constants.LOCAL_STORAGE_KEYS.PRODUCT_PRICE);
    dispatch(this.loadProductDetail());
    emitMessage({REFRESH_CACHE:""})
    dispatch(setMenuUpdated(false));
    dispatch(
      UtilityService.showSnackBar({
        open: true,
        snackType: Constants.SNACK_TYPE.SUCCESS,
        message: 'Menu Data Updated',
        autoHideDuration: 2000,
      })
    );
  };

  static loadProductBasicDetail = () => (dispatch) => {
    const localData = StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.PRODUCT_BASIC_DETAIL);
    let subscriptionProducts = StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.SUBSCRIPTION_PRODUCTS);
    if (!UtilityService.checkEmpty(localData) && !UtilityService.checkEmpty(subscriptionProducts)) {
      dispatch(OrderSlice.setProductBasicDetail(localData));
      dispatch(OrderSlice.setUnitSubscriptionProducts(subscriptionProducts))
      return;
    }
    
    const url = RestService.constructURL(
      APIConstants.getEndpoints().productData.productBasicDetail,
      {},
      {}
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) &&
          UtilityService.checkHttpResponseValid(response.data)
        ) {
          subscriptionProducts = {};
          let productBasicDetail = {products:{}}
          Object.values(response.data.body.products).forEach((product) => {
            if(product.subType === Constants.SUBSCRIPTION_INFO.subType)
            {
              subscriptionProducts[product?.id?.id] = { ...product, productId:product?.id?.id };   
              subscriptionProducts[product?.skuCode] = { ...product, productId:product?.id?.id };     
            }

            productBasicDetail = {
              products: {...productBasicDetail.products,[product?.id?.id]:{...product, productAliasName:product.productAliasName || product.name}}
            }
          });
          dispatch(OrderSlice.setProductBasicDetail(productBasicDetail));
          StorageService.setItem(
            Constants.LOCAL_STORAGE_KEYS.PRODUCT_BASIC_DETAIL,
            productBasicDetail
          );
          dispatch(OrderSlice.setUnitSubscriptionProducts(subscriptionProducts));
          StorageService.setItem(
            Constants.LOCAL_STORAGE_KEYS.SUBSCRIPTION_PRODUCTS,
            subscriptionProducts
          );
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static loadProductCategory = () => (dispatch) => {
    const localData = StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.PRODUCT_CATEGORY);
    if (!UtilityService.checkEmpty(localData)) {
      dispatch(OrderSlice.setProductCategory(localData));
      dispatch(OrderSlice.setCatIdMap(this.setCatIdToCategoryMap(localData)));
      return;
    }
    const url = RestService.constructURL(
      APIConstants.getEndpoints().productData.productCategory,
      {},
      {}
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) &&
          UtilityService.checkHttpResponseValid(response.data)
        ) {
          dispatch(OrderSlice.setProductCategory(response.data.body));
          dispatch(OrderSlice.setCatIdMap(this.setCatIdToCategoryMap(response.data.body)));
          StorageService.setItem(
            Constants.LOCAL_STORAGE_KEYS.PRODUCT_CATEGORY,
            response.data.body
          );
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static setCatIdToCategoryMap = (productCategoryData) => {
    const newMap = {};
    if(!UtilityService.checkEmpty(productCategoryData)) {
      productCategoryData.forEach((ele, index) => {
        if(ele.id != null) {
          newMap[ele.id] = {...ele};
        }
      });
    }
    return newMap;
  }

  static loadProductImageMap = () => (dispatch) => {
    const localData = StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.PRODUCT_IMAGE_MAP);
    if (!UtilityService.checkEmpty(localData)) {
      dispatch(OrderSlice.setProductImageMap(localData));
      return;
    }
    const url = RestService.constructURL(
      APIConstants.getEndpoints().productData.productImageMap,
      {},
      {}
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) &&
          UtilityService.checkHttpResponseValid(response.data)
        ) {
          dispatch(OrderSlice.setProductImageMap(response.data.body));
          StorageService.setItem(
            Constants.LOCAL_STORAGE_KEYS.PRODUCT_IMAGE_MAP,
            response.data.body
          );
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static loadProductCustomization = () => (dispatch) => {
    const localData = StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.PRODUCT_CUSTOMISATION);
    if (!UtilityService.checkEmpty(localData)) {
      dispatch(OrderSlice.setProductCustomization(localData));
      return;
    }
    const url = RestService.constructURL(
      APIConstants.getEndpoints().productData.productCustomization,
      {},
      {}
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) &&
          UtilityService.checkHttpResponseValid(response.data)
        ) {
          dispatch(OrderSlice.setProductCustomization(response.data.body));
          StorageService.setItem(
            Constants.LOCAL_STORAGE_KEYS.PRODUCT_CUSTOMISATION,
            response.data.body
          );
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static loadProductPrice = () => (dispatch) => {
    const localData = StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.PRODUCT_PRICE);
    if (!UtilityService.checkEmpty(localData)) {
      dispatch(OrderSlice.setProductPrice(localData));
      return;
    }
    const url = RestService.constructURL(
      APIConstants.getEndpoints().productData.productPrice,
      {},
      {}
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) &&
          UtilityService.checkHttpResponseValid(response.data)
        ) {
          dispatch(OrderSlice.setProductPrice(response.data.body));
          StorageService.setItem(Constants.LOCAL_STORAGE_KEYS.PRODUCT_PRICE, response.data.body);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static loadUnitMenu = () => (dispatch) => {
    const localData = StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.MENU_SEQUENCE);
    const localUnitMenuDetails = StorageService.getItem(Constants.LOCAL_STORAGE_KEYS.UNIT_MENU_DETAILS);
    
    if(!UtilityService.checkEmpty(localUnitMenuDetails)){
      dispatch(OrderSlice.setUnitMenuDetails(localUnitMenuDetails));
    }
    if (!UtilityService.checkEmpty(localData)) {
      dispatch(OrderSlice.setUnitMenu(localData));
      return;
    }
    const url = RestService.constructURL(
      APIConstants.getEndpoints().unit.unitmenu,
      {},
      { partnerId: 1 }
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) 
        ) {
          const data = [];
          // eslint-disable-next-line
          Object.values(response.data.category).map((value) => {
            // if (value.show) {
            data.push(value);
            // }
          });
          dispatch(OrderSlice.setUnitMenu(data.sort((a, b) => a.index - b.index)));
          dispatch(OrderSlice.setUnitMenuDetails(response?.data));
          StorageService.setItem(Constants.LOCAL_STORAGE_KEYS.MENU_SEQUENCE, data);
          StorageService.setItem(Constants.LOCAL_STORAGE_KEYS.UNIT_MENU_DETAILS, response?.data);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static loadProductInventory = (callBack) => (dispatch) => {
    const { unitId, liveInventoryEnabled } = store.getState().metadataSlice.unitDetail;
    const  unitZone = store.getState().metadataSlice.unitDetail.unitZone;
    let url = RestService.constructURL(
      APIConstants.getEndpoints().productData.productInventoryForNonLiveUnit,
      {},
      {}
    );
    if (liveInventoryEnabled) {
      url = RestService.constructURL(
        APIConstants.getEndpoints().productData.productInventory,
        { zone: unitZone.toLowerCase() },
        {}
      );
    }
    RestService.postJSON(url, unitId)
      .then((response) => {
        if (!UtilityService.checkEmpty(response)) {
          const data = {};
          response.forEach((val) => {
            data[val.id] = { ...val };
          });
          dispatch(OrderSlice.setProductInventory(data));
          dispatch(this.setProductQuantityMap(0,data))
          CartAction.sendMessageToCafeApp({ INVENTORY_DATA: data})
          if (callBack) {
            callBack(data);
          }
        }else{
          dispatch(this.handleErrorLoadingInventory())
        }
      })
      .catch((error) => {
        dispatch(this.handleErrorLoadingInventory())
        console.log(error);
      });
  };

  static handleErrorLoadingInventory=()=>(dispatch)=>{
    const products = JSON.parse(JSON.stringify(store.getState().orderSlice.productBasicDetail.products))
    dispatch(UtilityService.showSnackBar({open:true, snackType: Constants.SNACK_TYPE.ERROR, message:"Inventory is down. We have adjusted the default quantity"}));
    const data={};
    if(!UtilityService.checkEmpty(products)){
      Object.keys(products).forEach((key)=>{
        if(!UtilityService.checkEmpty(products[key].inventory) && products[key].inventory){
          data[key] = {id:key, quantity:2, ex:0};
        }
      })
    }
    dispatch(OrderSlice.setProductInventory(data));
    CartAction.sendMessageToCafeApp({ INVENTORY_DATA: data});
    dispatch(this.setProductQuantityMap(0,data))
  }

  static setProductQuantityMap=(val, inventory)=>(dispatch)=>{
    if(UtilityService.checkEmpty(inventory)){
      inventory = JSON.parse(JSON.stringify(store.getState().orderSlice.productInventory))
    }
    if(UtilityService.checkEmpty(inventory)){
      return;
    }
    const productQuantityInCartMap={}
    Object.values(inventory).forEach((item) => {
      productQuantityInCartMap[item.id] = val;
    });
    dispatch(updateProductsQty(productQuantityInCartMap));
    CartAction.sendMessageToCafeApp({ INITIAL_PRODUCT_QUANTITY_IN_CART: productQuantityInCartMap});
  }

  static loadWalletDenominationOffers = (callBack) => (dispatch) => {
    const url = RestService.constructURL(
      APIConstants.getEndpoints().offers.walletDenominationOffer,
      {},
      {}
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) &&
          UtilityService.checkHttpResponseValid(response.data)
        ) {
          dispatch(OrderSlice.setWalletDenominationOffer(response.data.body));
          if (callBack) {
            callBack();
          }
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static loadDirectWalletOffers = (callBack) => (dispatch) => {
    const url = RestService.constructURL(
      APIConstants.getEndpoints().offers.directWalletOffers,
      {},
      {}
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) &&
          UtilityService.checkHttpResponseValid(response.data)
        ) {
          dispatch(OrderSlice.setDirectWalletOffer(response.data.body));
          if (callBack) {
            callBack();
          }
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static loadLastNOrders = (callBack) => (dispatch) => {
    const url = RestService.constructURL(
      APIConstants.getEndpoints().order.lastNOrders,
      {},
      { size: 6 }
    );
    RestService.getJSON(url, {})
      .then((response) => {
        if (
          !UtilityService.checkEmpty(response) &&
          UtilityService.checkHttpResponseValid(response.data)
        ) {
          dispatch(OrderSlice.setLastNOrders(response.data.body));
          if (callBack) {
            callBack(response.data.body);
          }
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  static getCafeStatus = (unitId,refreshed=false) => (dispatch) => {
    RestService.getJSON(APIConstants.getEndpoints().unit.cafeStatusForUnit, { unitId })
      .then((response) => {
        if (!UtilityService.checkEmpty(response)) {
          dispatch(MetadataSlice.setCafeStatus(response.data));
          if(refreshed){
            dispatch(
              UtilityService.showSnackBar({
                open: true,
                snackType: Constants.SNACK_TYPE.SUCCESS,
                message: 'Channel Partner Status Updated',
                autoHideDuration: 2000,
              })
            ); 
          }
        }

      })
      .catch((error) => {
        console.log(error);
        dispatch(
          UtilityService.showSnackBar({
            open: true,
            snackType: Constants.SNACK_TYPE.ERROR,
            message: 'Error while getting Channel Partner Status',
            autoHideDuration: 2000,
          })
        ); 
      });
  };

}

export default OrderAction;
