import { Box, Button, InputAdornment, Stack, TextField, Typography, useTheme } from '@mui/material';
import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UnSelectedBackground } from '../../../layouts/baseStyles';
import UtilityService from '../../../services/UtilityService';
import CartAction from '../../../sections/cart/CartAction';
import ProgressTabButtonsAction from '../../progressTabButtons/ProgressTabButtonsAction';
import Constants from '../../../utils/Constants';
import { setRightSideBar } from '../../../pages/home/<USER>';
import CustomerAction from '../../customerInfo/customerAction';
import { ProductService } from '../../../services/ProductService';
import CommunicationService from '../../../cafeAppCommunication/communicationService';
import OrderAction from '../OrderAction';
import VegNonvegTag from '../../vegNonvegTag/VegNonvegTag';
import { setCurrentMenuCategory } from '../OrderSlice';
import CustomToolTip from '../../tooltip/CustomTooltip';
import Image from '../../image';
import { iconConfig } from '../../../icon-config';
import { StyledIcon } from '../../nav-section/mini/styles';
import RecomendationProductCard from '../../productCards/RecomendationProductCard';
import SavedChaiProductCardV2 from '../../productCards/SavedChaiProductCardV2';
import { setTableFooterMenuModalState } from '../../TableService/TableSlice';
import ProductCard from './ProductCard';
import RecommendationsView from './RecommendationsView';
import { recommendationService } from '../../../services/RecommendationService';
import { setNewRecmData } from '../../../sections/coupon/OffersSlice';

export default function TableOrderCategorySection() {
  const dispatch = useDispatch();
  const {
    productBasicDetail,
    unitMenu,
    productInventory,
    recommendedProductData,
    recommendedProductDataIndex,
    productPrice,
    productCustomization,
    recomOfferData,
    unitSubscriptionProducts,
    allExploreMoreOptionsProductList,
    isRecommendationInCart
  } = useSelector((store) => store.orderSlice);
  const { unitDetail, loyalTeaRedemptionAllowed } = useSelector((store) => store.metadataSlice);
  const freeLoyaltyTea = useSelector((store) => store.customerSlice.freeLoyaltyTea);
  const { loyalTeaInCart, productsQty } = useSelector((store) => store.cartSlice.cart);
  const [selectedProductId, setSelectedProductId] = useState();
  const [isVeg, setIsVeg] = useState(false);
  const [isNonVeg, setIsNonVeg] = useState(false);
  const [isShowVegNonVegFilter, setIsShowVegNonVegFilter] = useState(false);
  const [categoryWiseProduct, setCategoryWiseProduct] = useState({});
  const [showCategorySelection, setShowCategorySelection] = useState(true);
  const savedChais = useSelector((store) => store.customerSlice.savedChais);

  const customer = useSelector((store) => store.customerSlice.customerBasicInfo);
  const theme = useTheme();
  const currentSelectedValue = useSelector((store) => store.orderSlice.currentMenuCategory);
  const [recommendationPannelState, setRecommendationPannelState] = useState([true, true]); // 0 index -> saved chai's and 1index -> recommendations
  const selectedComplReason = useSelector(
    (store) => store.complimentaryOrdersSlice.selectedComplimentryReason
  );
  const { isTablefooterMenuModalOpen } = useSelector((store) => store.tableSlice);
  const { showOrderSummary } = useSelector((state) => state.cartSlice);
  const { previousOrders } = useSelector((store) => store.customerSlice);
  const [showBlurScreen, setShowBlurScreen] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const { cartItems } = useSelector((state) => state.cartSlice.cart);
  const newRecomData = useSelector((state)=>state.offersSlice.newRecomData);
  const cartRulesData = useSelector((state)=>state.orderSlice.cartRulesData);
  // function priceExits(productId) {
  //   const price =
  //     productPrice?.prices[productId]?.prices[
  //       ProductService.getDefaultDimension(productCustomization[productId])
  //     ];
  //   return price?.price;
  // }
  const [searchReasonInput, setSearchReasonInput] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const categoryRefs = useRef([]);
  const categoryContainerRef = useRef(null);
  const [selectedFilters, setSelectedFilters] = useState({});
  const [tempSelectedFilters, setTempSelectedFilters] = useState({});

  // get current scroll direction
  let direction = 'down'
  let prevYPosition = 0;

  const setScrollDirection = () => {
    if (categoryContainerRef.current?.scrollTop > prevYPosition) {
      direction = 'down'
    } else {
      direction = 'up'
    }
    // console.log('Setting scroll dir', direction, ": YPOS", categoryContainerRef.current?.scrollTop, "  ", prevYPosition);

    prevYPosition = categoryContainerRef.current?.scrollTop
  }

  const shouldUpdate = (dir, entry) => {
    if (dir === 'down' && !entry.isIntersecting) {
      return true
    }

    if (dir === 'up' && entry.isIntersecting) {
      return true
    }

    return false
  }

  useLayoutEffect(() => {
    // console.log("RUNNING LAYOUT", categoryRefs.current);
    categoryRefs.current = categoryRefs.current.slice(0, Object.values(unitMenu).length);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [Object.values(unitMenu).length]);

  useEffect(() => {
    // console.log("Container Reference", categoryContainerRef.current);
    const currentRefs = categoryRefs?.current;
    // console.log("Childrens References", currentRefs);
    const observerOptions = {
      root: categoryContainerRef?.current,
      rootMargin: '-10px 0px 0px 0px',
      threshold: 0,
    };

    const getTargetSection = (entry) => {
      const index = currentRefs?.findIndex((section) => section === entry.target)

      if (index >= currentRefs.length - 1) {
        return entry.target.id
      }
      return currentRefs[index + 1].id

    }
    const observerCallback = (entries) => {
      // let leastTop = Number.POSITIVE_INFINITY;
      let targetCategoryId = selectedCategory;
      entries?.forEach((entry) => {

        setScrollDirection();
        // const dir = 'down';
        // let dir = direction
        const target = direction === 'down' ? getTargetSection(entry) : entry.target.id

        if (shouldUpdate(direction, entry)) {
          targetCategoryId = parseInt(target, 10);
          // console.log("Setting new", targetCategoryId);
          if (UtilityService.allowEvent) {
            setSelectedCategory(targetCategoryId);
          }
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    currentRefs.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => {
      currentRefs.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryRefs.current]);



  useEffect(() => {
    dispatch(OrderAction.loadWalletDenominationOffers());
    dispatch(OrderAction.loadDirectWalletOffers());
    handleCategoryChange(currentSelectedValue);
    // dispatch(OrderDataModel.getRecommendation());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!UtilityService.checkEmpty(unitMenu) && UtilityService.checkEmpty(selectedCategory)) {
      setSelectedCategory(Object.values(unitMenu)[0]?.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [unitMenu]);

  // useEffect(() => {
  //   let data = [false, false];
  //   if (!UtilityService.checkEmpty(recommendedProductData) && !recommendationPannelState[0]) {
  //     data[1] = true;
  //   }
  //   // else if (!UtilityService.checkEmpty(savedChais) && countSavedChais() > 0 && !recommendationPannelState[1]) {
  //   //   data[0] = true;
  //   // }
  //   if (!data[0] && !data[1]) {
  //     data = recommendationPannelState;
  //   }
  //   setRecommendationPannelState(data);
  //   // eslint-disable-next-line
  // }, [recommendedProductData]);

  useEffect(() => {
    let categoryWiseProductMap = JSON.parse(JSON.stringify(unitMenu));
    // eslint-disable-next-line
    if (
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER ||
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.WASTAGE_ORDER ||
      CommunicationService.getOrderType() === Constants.ORDER_TYPE.UNSTATISFIED_CUSTOMER_ORDER
    ) {
      let filteredCategoryWiseProductMap = {};
      Object.values(categoryWiseProductMap).forEach((category, index) => {
        const ids = [];
        if (categoryWiseProductMap[index].id !== 7) {
          category?.pids.forEach((id) => {
            let defaultDimension = null;
            if (
              CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER &&
              selectedComplReason.id === 2110
            ) {
              if (id === 10) {
                defaultDimension = ProductService.getDefaultDimension(productCustomization[id]);
                if (defaultDimension && productPrice.prices[id]?.prices[defaultDimension]) {
                  ids.push(id);
                }
              }
            } else {
              defaultDimension = ProductService.getDefaultDimension(productCustomization[id]);
              if (
                [1043, 1044, 11, 12, 50, 14, 15, 1292, 1293, 1294].indexOf(id) < 0 &&
                productBasicDetail?.products[id]?.taxCode !== 'GIFT_CARD' &&
                productBasicDetail?.products[id]?.type !== 8 &&
                productBasicDetail?.products[id] !== 1202 &&
                productBasicDetail?.products[id]?.subType !== 3810
              ) {
                if (defaultDimension && productPrice.prices[id]?.prices[defaultDimension]) {
                  ids.push(id);
                }
              }
            }
          });
          if (ids.length > 0) {
            const key = Object.keys(filteredCategoryWiseProductMap).length;
            categoryWiseProductMap[index].pids = ids;
            filteredCategoryWiseProductMap = {
              ...filteredCategoryWiseProductMap,
              [key]: categoryWiseProductMap[index],
            };
          }
        }
      });
      setCategoryWiseProduct({ ...filteredCategoryWiseProductMap });
    } else {
      Object.values(categoryWiseProductMap).forEach((category, index) => {
        const ids = [];
        category?.pids.forEach((id) => {
          const defaultDimension = ProductService.getDefaultDimension(productCustomization[id]);
          if (defaultDimension && productPrice.prices[id]?.prices[defaultDimension]) {
            ids.push(id);
          }
        });
        category?.subCategoriesProduct?.forEach((subCategory, idx) => {
          const subIds = [];
          subCategory?.pids.forEach((id) => {
            const defaultDimension = ProductService.getDefaultDimension(productCustomization[id]);
            if (defaultDimension && productPrice.prices[id]?.prices[defaultDimension]) {
              subIds.push(id);
              if (Constants.DESI_CHAI_ID === id) {
                categoryWiseProductMap[index].subCategoriesProduct[idx].showSavedChais = true;
              }
            }
          });
          categoryWiseProductMap[index].subCategoriesProduct[idx].pids = subIds;
        });

        categoryWiseProductMap[index].pids = ids;
      });
      categoryWiseProductMap = categoryWiseProductMap.sort((a, b) => a.index - b.index);

      setCategoryWiseProduct({ ...categoryWiseProductMap });
    }

    return () => {
      dispatch(setTableFooterMenuModalState(false));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [unitMenu, productCustomization, productPrice]);

  const addProduct = useCallback((productId, isRecommProduct) => {
    const productDetail = productBasicDetail?.products[productId];
    const customization = productCustomization[productId];
    const selectedDimension = ProductService.getDefaultDimension(customization);

    if (productDetail?.taxCode === Constants.TAX_CODE.COMBO) {
      dispatch(
        setRightSideBar({
          open: true,
          code: Constants.RIGTHSIDE_BAR.COMBO_ITEM_SELECTION,
          data: { productDetail, customization },
        })
      );
    } else if (
      UtilityService.isChaiEligibleForSpectatorMode(productDetail.id.id) ||
      CustomerAction.isEligibleForLoyaltyBurn(productDetail.id.id, selectedDimension)
    ) {
      dispatch(
        setRightSideBar({
          open: true,
          code: Constants.RIGTHSIDE_BAR.CUSTOMIZE_ORDER,
          data: { productDetail, customization, isCameFromSpectatorMode: false, isRecommProduct },
        })
      );

      if (!UtilityService.checkEmpty(Constants.MILK_SELECTION[productDetail.id.id])) {
        // dc customised
        ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.desiChaiCustomised, false);
      }
    } else {
      let isCustomisable = false;
      if (UtilityService.checkEmpty(unitSubscriptionProducts[productId])) {
        isCustomisable = true;
      } else {
        isCustomisable = false;
      }
      CartAction.addItemToCart(
        CartAction.getProcessedItem({
          productData: { productDetail, customization },
          recProd: isRecommProduct || false,
          selectedVariants: setDefaultVariant(customization, selectedDimension),
          selectedMilkType: setDefaultMilkSelection(productDetail.id.id),
          isCustomisable,
          selectedDimension,
        })
      );
      if (isRecommProduct === true) {
        try {
          ProgressTabButtonsAction.keyIncrementer(['recomUsed']);
          ProgressTabButtonsAction.updateBoolInPushJson('recomUsed');
          ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.recommendationAdded, true);
        } catch (e) {
          console.error('Error while updating recom goal', e);
        }
      }
    }
  }, [productCustomization, productBasicDetail, unitSubscriptionProducts, dispatch]);

  const setDefaultVariant = (customization, dim) => {
    const data = {};
    customization.prices[dim]?.variant?.forEach((variant) => {
      data[variant.name] = { ...variant.options[0], variantName: variant.name };
    });
    return data;
  };

  const setDefaultMilkSelection = (id) => {
    const parentIndex = UtilityService.getParentIdForMilkSelection(id);
    if (parentIndex > -1) {
      return parentIndex.toString();
    }
    return null;
  };

  const handleCategoryChange = (newSelectedValue) => {
    console.log('newSelectedValue - ', newSelectedValue);
    dispatch(setCurrentMenuCategory(newSelectedValue));
    let isAttrFound = false;
    if (CommunicationService.getOrderType() === Constants.ORDER_TYPE.COMPLIMENTRY_ORDER) {
      if (
        !UtilityService.checkEmpty(productBasicDetail[newSelectedValue]) &&
        !UtilityService.checkEmpty(productBasicDetail[newSelectedValue].productIds)
      ) {
        productBasicDetail[newSelectedValue].productIds.forEach((productId, index) => {
          if (!UtilityService.checkEmpty(productBasicDetail?.products[productId]?.attr)) {
            isAttrFound = true;
          }
        });
      }
      setIsShowVegNonVegFilter(isAttrFound);
    } else {
      if (
        !UtilityService.checkEmpty(unitMenu[newSelectedValue]) &&
        !UtilityService.checkEmpty(unitMenu[newSelectedValue].pids)
      ) {
        unitMenu[newSelectedValue].pids.forEach((productId, index) => {
          if (!UtilityService.checkEmpty(productBasicDetail?.products[productId]?.attr)) {
            isAttrFound = true;
          }
        });
      }
      setIsShowVegNonVegFilter(isAttrFound);
    }
  };

  const checkFilterExists = useCallback((productId, tags) => {
    const commonFilters = Object.keys(selectedFilters)?.filter((tag) => tags?.[tag]);
    if (UtilityService.checkEmpty(commonFilters)) {
      return false;
    }
    return true;
  }, [selectedFilters]);

  const filteredProducts = useCallback((map) => {
    const ids = map.pids;
    const startIds = [];
    const endIds = [];
    if (UtilityService.checkEmpty(productInventory)) {
      return ids;
    }
    // eslint-disable-next-line
    ids.map((productId) => {
      if (
        (productId === Constants.DESI_CHAI_ID ||
          productId === Constants.LEMON_GRASS_TEA_ID ||
          UtilityService.getParentIdForMilkSelection(productId)) &&
        ((isVeg &&
          (productBasicDetail.products[productId].attr === 'VEG' ||
            UtilityService.checkEmpty(productBasicDetail.products[productId].attr))) ||
          (isNonVeg && productBasicDetail.products[productId].attr === 'NON_VEG') ||
          (!isVeg && !isNonVeg)) &&
        (ProductService.isAnyWordExists(
          searchReasonInput,
          productBasicDetail.products[productId]?.name
        ) ||
          searchReasonInput === JSON.stringify(productId) ||
          ProductService.isAnyWordExists(
            searchReasonInput,
            productBasicDetail.products[productId]?.productAliasName
          )) &&
        (UtilityService.checkEmpty(selectedFilters) ||
          checkFilterExists(productId, productBasicDetail?.products[productId]?.productTagMap))
      ) {
        if (
          UtilityService.checkEmpty(productInventory[productId]) ||
          productInventory[productId].quantity > 0
        ) {
          startIds.push(productId);
        } else {
          endIds.push(productId);
        }
      }
    });
    return startIds.concat(endIds);
  }, [productInventory, isVeg, productBasicDetail, searchReasonInput, isNonVeg, selectedFilters, checkFilterExists]);

  const countSavedChais = () => {
    let size = 0;
    savedChais?.forEach((savedChai, index) => {
      if (!UtilityService.checkEmpty(CartAction.priceExits(savedChai.productId))) {
        size += 1;
      }
    });
    return size;
  };

  const handleFilters = () => {
    setShowFilters(true);
    dispatch(setTableFooterMenuModalState(true));
    setShowBlurScreen(true);
    setTempSelectedFilters(selectedFilters);
  };

  const handleAddSavedChai = useCallback((product) => {
    let selectedMilkType;
    const selectedFreeAddons = {};
    const selectedPaidAddons = {};
    const selectedVariants = {};
    let selectedDimension = product?.dimension;
    const productDetail = productBasicDetail?.products[product.productId];
    const customization = productCustomization[product.productId];
    const productData = { productDetail, customization, isCameFromSpectatorMode: false };

    if (UtilityService.checkEmpty(selectedDimension)) {
      selectedDimension = ProductService.getDefaultDimension(
        productCustomization[product.productId]
      );
    }
    if (UtilityService.getParentIdForMilkSelection(productData.productDetail.id.id) > -1) {
      selectedMilkType = product.productId;
      const pIdForMilkSelection = UtilityService.getParentIdForMilkSelection(
        productData.productDetail.id.id
      );
      let newMilkSelection = {};
      Object.keys(Constants.MILK_SELECTION).forEach((pid) => {
        if (
          !UtilityService.checkEmpty(
            productCustomization[pid]?.prices[product.dimension]?.aliasProductName
          )
        ) {
          newMilkSelection = {
            ...newMilkSelection,
            [pid]: productCustomization[pid]?.prices[product.dimension]?.aliasProductName,
          };
        } else {
          newMilkSelection = { ...newMilkSelection, [pid]: Constants.DEFAULT_MILK_SELECTION[pid] };
        }
      });
      Constants.MILK_SELECTION = newMilkSelection;
      let newDoodhSelection = {};
      Object.keys(Constants.DOODH_SELECTION[pIdForMilkSelection]).forEach((pid) => {
        if (
          !UtilityService.checkEmpty(
            productCustomization[pid]?.prices[product.dimension]?.aliasProductName
          )
        ) {
          newDoodhSelection = {
            ...newDoodhSelection,
            [pid]: productCustomization[pid]?.prices[product.dimension]?.aliasProductName,
          };
        } else {
          newDoodhSelection = {
            ...newDoodhSelection,
            [pid]: Constants.DEFAULT_DOODH_SELECTION[pIdForMilkSelection][pid],
          };
        }
      });
      Constants.DOODH_SELECTION = {
        ...Constants.DOODH_SELECTION,
        [pIdForMilkSelection]: newDoodhSelection,
      };
    }

    product.chaiCustomizationList.forEach((customizationObject) => {
      const item = CartAction.getCustomizationObject(product, customizationObject);
      if (!UtilityService.checkEmpty(item)) {
        if (customizationObject.type === 'VARIANT') {
          selectedVariants[item.variantName] = item;
        } else if (customizationObject.type === 'FREE_ADDON') {
          selectedFreeAddons[customizationObject.name] = item;
        } else if (customizationObject.type === 'PAID_ADDON') {
          selectedPaidAddons[customizationObject.name] = item;
        }
      }
    });

    CartAction.addItemToCart(
      CartAction.getProcessedItem({
        productData,
        selectedMilkType,
        selectedDimension,
        selectedFreeAddons,
        selectedPaidAddons,
        selectedVariants,
        isCustomisable: true,
        loyalTea: false,
        recProd: false,
        savedChaiItem: product,
      })
    );
  }, [productBasicDetail, productCustomization]);


  const getMainContainer = useMemo(() => {
    console.log("");
    return (
        <Stack
          sx={{
            display: 'flex',
            position: 'relative',
            flex: 9,
            justifyContent: 'center',
            m: 2,
            mb: 1,
            mt: 2.5,
          }}
        >
          <Stack
            sx={{
              display: 'flex',
              justifyContent: 'flex-start',
              flexDirection: 'column',
              // flexWrap: 'wrap',
              position: 'absolute',
              alignContent: 'baseline',
              top: '0',
              // p: 1.25,
              pt: 0,
              bottom: '0',
              overflow: 'scroll',
              width: (!UtilityService.checkEmpty(newRecomData)) && CartAction.getValidRecommendations(newRecomData).length > 0 ? '74%' : '98%',
              // border: '1px solid red',
              '&::-webkit-scrollbar-track': {
                background: 'transparent',
                border: 'none',
                boxShadow: 'none',
              } 
            }}
            ref={categoryContainerRef}
          >
            {categoryWiseProduct &&
              Object.values(categoryWiseProduct)?.map(
                (subCategoryWiseProduct, subCategoryIndex) => (
                  <Stack
                    // sx={{ border: '1px solid' }}
                    id={subCategoryWiseProduct?.id}
                    ref={(el) => {
                      if (el) {
                        categoryRefs.current[subCategoryIndex] = el;
                        // console.log("REFFING", categoryRefs.current[subCategoryIndex]);
                      }
                    }}
                  >
                    {subCategoryWiseProduct &&
                      Object.values(subCategoryWiseProduct?.subCategoriesProduct)?.map(
                        (category, idx) => {
                          const filteredProductsList = filteredProducts(category);
                          return (
                            <Stack
                              id={category?.id}
                              sx={{
                                flexDirection: 'column',
                                width: '100%',
                                // border: '1px solid',
                              }}
                            >
                              {filteredProductsList.length +
                                (category?.showSavedChais
                                  ? savedChais.length
                                  : 0) >
                                0 ? (
                                <Stack
                                  sx={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    pr: 5,
                                    my: 1,
                                    mb: 2,
                                    // border:"1px solid black"
                                  }}
                                >
                                  <Typography
                                    variant="subtitle1"
                                    sx={{
                                      // mb: 0.5,
                                      fontSize: 20,
                                      fontWeight: 600,
                                      letterSpacing: 0.5
                                    }}
                                  >
                                    {category?.name}
                                  </Typography>
                                  <Box
                                    sx={{
                                      flex: 0.85,
                                      height: '1px',
                                      ml: 1,
                                      background: theme.palette.neutral.lightGrey2,
                                    }}
                                  />
                                </Stack>
                              ) : null}
                              <Stack
                                sx={{
                                  flexDirection: 'row',
                                  flexWrap: 'wrap',
                                }}
                              >
                                {/* Save Chai Special Card */}
                                {!UtilityService.checkEmpty(category) &&
                                  !UtilityService.checkEmpty(
                                    category?.showSavedChais
                                  ) &&
                                  !UtilityService.checkEmpty(savedChais) ? (
                                  savedChais?.map((savedChai, index) => (
                                    <>
                                      {(savedChai.productId === Constants.DESI_CHAI_ID ||
                                        savedChai.productId === Constants.LEMON_GRASS_TEA_ID ||
                                        UtilityService.getParentIdForMilkSelection(
                                          savedChai.productId
                                        )) &&
                                        ((isVeg &&
                                          (productBasicDetail?.products[savedChai.productId]
                                            .attr === 'VEG' ||
                                            UtilityService.checkEmpty(
                                              productBasicDetail?.products[savedChai.productId]
                                                .attr
                                            ))) ||
                                          (isNonVeg &&
                                            productBasicDetail?.products[savedChai.productId]
                                              .attr === 'NON_VEG') ||
                                          (!isVeg && !isNonVeg)) &&
                                        (ProductService.isAnyWordExists(
                                          searchReasonInput,
                                          productBasicDetail?.products[savedChai.productId]?.name
                                        ) ||
                                          searchReasonInput ===
                                          JSON.stringify(savedChai.productId) ||
                                          ProductService.isAnyWordExists(
                                            searchReasonInput,
                                            savedChai?.tagType
                                          )) &&
                                        (UtilityService.checkEmpty(selectedFilters) ||
                                          checkFilterExists(
                                            savedChai.productId,
                                            productBasicDetail?.products[savedChai.productId]
                                              ?.productTagMap
                                          )) ? (
                                        <Box
                                          key={index}
                                          sx={{
                                            width: 'fit-content',
                                          }}
                                        >
                                          {/* {(productBasicDetail?.[productId].status=== Constants.STATUS.ACTIVE)? */}
                                          <CustomToolTip
                                            title={
                                              savedChai.tagType.length > 12 ? savedChai.tagType : ''
                                            }
                                            position="top"
                                            child={
                                              <UnSelectedBackground
                                                onClick={() => {
                                                  setSelectedProductId(savedChai.productId);
                                                  if (
                                                    CartAction.isInventoryAvalable(
                                                      savedChai.productId
                                                    )
                                                  ) {
                                                    handleAddSavedChai(
                                                      savedChai
                                                    );
                                                  } else {
                                                    dispatch(
                                                      UtilityService.showSnackBar({
                                                        open: true,
                                                        snackType: Constants.SNACK_TYPE.ERROR,
                                                        message:
                                                          'More Inventory Not Available for the product ',
                                                      })
                                                    );
                                                  }
                                                }}
                                                key={index}
                                                sx={{
                                                  flexDirection: 'column',
                                                  cursor: 'pointer',
                                                  position: 'relative',
                                                  height: '124px',
                                                  width: '132px',
                                                  p: 1,
                                                  mr: 3,
                                                  mb: 3,
                                                  alignItems: 'start',
                                                  border: '1px solid',
                                                  borderColor: theme.palette.grey[400],
                                                  borderRadius: 1,
                                                  ...(UtilityService.checkEmpty(previousOrders?.orderProducts?.[savedChai.productId])
                                                    && {
                                                    background: theme.palette.error.lighter,
                                                    borderColor: theme.palette.clayRed[500],
                                                    color: theme.palette.common.black,
                                                  }),
                                                  ...(selectedProductId ===
                                                    savedChai.productId && {
                                                    background:
                                                      theme.palette.specialColors
                                                        .SELECTED_BACKGROUND,
                                                    borderColor: theme.palette.leafGreen[500],
                                                    color: theme.palette.leafGreen[500],
                                                  }),
                                                }}
                                              >
                                                {!CartAction.isInventoryAvalable(
                                                  savedChai.productId
                                                ) ? (
                                                  <Typography
                                                    variant="h8"
                                                    sx={{
                                                      height: '124px',
                                                      width: '132px',
                                                      position: 'absolute',
                                                      top: 0,
                                                      right: 0,
                                                      bgcolor: 'red',
                                                      zIndex: 5,
                                                      borderRadius: 1,
                                                      display: 'flex',
                                                      justifyContent: 'center',
                                                      alignItems: 'center',
                                                      color: 'transparent',
                                                      background:
                                                        theme.palette.specialColors
                                                          .UNSELECTED_BACKGROUND,
                                                      '&:hover': {
                                                        color: theme.palette.neutral.black,
                                                        background:
                                                          theme.palette.specialColors
                                                            .DISABLE_RED,
                                                      },
                                                    }}
                                                  >
                                                    Stock Out
                                                  </Typography>
                                                ) : (
                                                  <Box />
                                                )}
                                                {CartAction.isEligibleForRecommendationOffer(
                                                  savedChai.productId
                                                ) ? (
                                                  <Box
                                                    sx={{
                                                      height: '124px',
                                                      width: '132px',
                                                      position: 'absolute',
                                                      display: 'flex',
                                                      alignItems: 'center',
                                                      justifyContent: 'center',
                                                      overflow: 'hidden',
                                                      top: '-3px',
                                                      left: '-2px',
                                                    }}
                                                  >
                                                    <Typography
                                                      sx={{
                                                        height: '20px',
                                                        width: '132px',
                                                        position: 'absolute',
                                                        top: 20,
                                                        right: 35,
                                                        bgcolor: 'red',
                                                        zIndex: 5,
                                                        transform: 'rotate(315deg)',
                                                        display: 'flex',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                        color: 'white',
                                                        background: theme.palette.clayRed[500],
                                                        boxShadow: '3px 2px 5px 0px #8d9898',
                                                      }}
                                                    >
                                                      <Typography
                                                        variant="caption1"
                                                        sx={{
                                                          position: 'fixed',
                                                          width: 'max-content',
                                                          translate: '33%',
                                                          letterSpacing: '1px',
                                                          // animation:'marquee 12s linear 0s infinite',
                                                          // '@keyframes marquee': {
                                                          //     '0%': { left: '-33%' },
                                                          //     '100%': { left: '-135%' },
                                                          //   },
                                                        }}
                                                      >
                                                        {recomOfferData?.offerTag}
                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                        {recomOfferData?.offerTag}
                                                      </Typography>
                                                    </Typography>
                                                  </Box>
                                                ) : (
                                                  <Box />
                                                )}
                                                {/* {(allExploreMoreOptionsProductList[savedChai.productId]
                                          ?.recomReason ||
                                          !UtilityService.checkEmpty(
                                            productBasicDetail?.products?.[savedChai.productId]?.tag
                                          )) &&
                                          savedChai.productId !== Constants.DESI_CHAI_ID &&
                                          savedChai.productId !== Constants.PRODUCTID.BLACK_TEA &&
                                          savedChai.productId !== Constants.PRODUCTID.GREEN_TEA ? ( */}
                                                <Typography
                                                  variant="caption"
                                                  sx={{
                                                    height: '20px',
                                                    width: '100px',
                                                    position: 'absolute',
                                                    top: -10,
                                                    left: -5,
                                                    bgcolor: 'red',
                                                    borderRadius: '6px 0 6px 0',
                                                    zIndex: 5,
                                                    textAlign: 'center',
                                                    color: 'white',
                                                    background: theme.palette.leafGreen[500],
                                                    textTransform: 'capitalize',
                                                  }}
                                                >
                                                  {/* {allExploreMoreOptionsProductList[
                                              savedChai.productId
                                            ]?.recomReason?.toLowerCase() ||
                                              productBasicDetail?.products?.[
                                                savedChai.productId
                                              ]?.tag[0]?.toLowerCase()} */}
                                                  Saved Chai
                                                </Typography>
                                                {/* ) : (
                                          <Box />
                                        )} */}

                                                <Image
                                                  sx={{
                                                    height: '75px',
                                                    width: '75px',
                                                    borderRadius: 1,
                                                    objectFit: 'contain',
                                                    ...(CartAction.isEligibleForRecommendationOffer(
                                                      savedChai.productId
                                                    ) && {
                                                      alignSelf: 'center',
                                                    }),
                                                  }}
                                                  src={ProductService.getProductImage(
                                                    UtilityService.getParentIdForMilkSelection(
                                                      savedChai.productId
                                                    ),
                                                    Constants.IMAGE_TYPES.GRID_MENU_400X400,
                                                    currentSelectedValue
                                                  )}
                                                />
                                                <Typography
                                                  variant="body3"
                                                  sx={{
                                                    height: '60px',
                                                    // border:"1px solid",
                                                    justifyContent: 'start',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    ...(CartAction.isEligibleForRecommendationOffer(
                                                      savedChai.productId
                                                    ) && {
                                                      alignSelf: 'center',
                                                      textAlign: 'center',
                                                    }),
                                                  }}
                                                >
                                                  {UtilityService.getEllipsisText(
                                                    savedChai.tagType, 12
                                                  )}
                                                </Typography>
                                                {(loyalTeaRedemptionAllowed && (Constants.LOYALTEA_CHAIS.includes(
                                                  savedChai.productId
                                                ) &&
                                                  freeLoyaltyTea > loyalTeaInCart &&
                                                  loyalTeaInCart < 5) ||
                                                  CustomerAction.isEligibleForLoyaltyBurn(
                                                    savedChai.productId,
                                                    ProductService.getDefaultDimensionById(
                                                      savedChai.productId
                                                    )
                                                  )) ? (
                                                  <StyledIcon
                                                    sx={{
                                                      position: 'absolute',
                                                      right: -12,
                                                      top: -12,
                                                      height: 24,
                                                      width: 24,
                                                      color: theme.palette.clayRed[500],
                                                    }}
                                                  >
                                                    {iconConfig.loyaltyBookmark}
                                                  </StyledIcon>
                                                ) : (
                                                  <Box />
                                                )}
                                                {!UtilityService.checkEmpty(productInventory) &&
                                                  !UtilityService.checkEmpty(
                                                    productInventory[savedChai.productId]
                                                  ) ? (
                                                  <Stack
                                                    spacing={0.5}
                                                    sx={{ position: 'absolute', right: 5 }}
                                                  >
                                                    <Typography
                                                      variant="caption"
                                                      sx={{
                                                        height: 25,
                                                        minWidth: 25,
                                                        bgcolor: theme.palette.leafGreen[300],
                                                        p: 0.3,
                                                        borderRadius: 0.7,
                                                        textAlign: 'center',
                                                        color: theme.palette.neutral.white,
                                                        ...(productInventory[
                                                          savedChai.productId
                                                        ].quantity < 10 && {
                                                          bgcolor: theme.palette.warning.main,
                                                        }),
                                                      }}
                                                    >
                                                      {productInventory[savedChai.productId]
                                                        .quantity -
                                                        productsQty[savedChai.productId]}
                                                    </Typography>
                                                  </Stack>
                                                ) : (
                                                  <Box />
                                                )}
                                              </UnSelectedBackground>
                                            }
                                          />
                                        </Box>
                                      ) : (
                                        <Box />
                                      )}
                                    </>
                                  ))
                                ) : (
                                  <Box />
                                )}

                                {/* Main Cards */}
                                {!UtilityService.checkEmpty(category) &&
                                  !UtilityService.checkEmpty(category?.pids) ? (
                                  filteredProductsList?.map((productId, index) => (
                                    <ProductCard
                                      productId={productId}
                                      index={index}
                                      productBasicDetail={productBasicDetail}
                                      customer={customer}
                                      recomOfferData={recomOfferData}
                                      allExploreMoreOptionsProductList={allExploreMoreOptionsProductList}
                                      previousOrders={previousOrders}
                                      loyalTeaInCart={loyalTeaInCart}
                                      productInventory={productInventory}
                                      freeLoyaltyTea={freeLoyaltyTea}
                                      addProduct={addProduct}
                                      setSelectedProductId={setSelectedProductId}
                                      isSelected={selectedProductId === productId}
                                      currentSelectedValue={currentSelectedValue}
                                      qty={productsQty[productId]}
                                      loyalTeaRedemptionAllowed={loyalTeaRedemptionAllowed}
                                    />
                                  ))
                                ) : (
                                  <Box />
                                )}
                              </Stack>
                            </Stack>
                          );
                        }
                      )}
                  </Stack>
                )
              )}
          </Stack>
          {!UtilityService.checkEmpty(categoryWiseProduct) && (!UtilityService.checkEmpty(newRecomData) && CartAction.getValidRecommendations(newRecomData).length > 0)? <UnSelectedBackground
            sx={{
              display: 'flex',
              position: 'absolute',
              // bottom: 8,
              right: '0%',
              background: theme.palette.neutral.white,
              borderColor: !isRecommendationInCart && theme.palette.clayRed[500],
              height: '100%',
              // minWidth: '250px',
              width: '25%',
              // py: 1,
              paddingBottom: 1,
              flexDirection: 'column',
            }}
          >
            <RecommendationsView />
          </UnSelectedBackground> : <Box />}
        </Stack>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryWiseProduct, productsQty, savedChais, addProduct, isNonVeg, isVeg, loyalTeaInCart, customer,
    previousOrders, allExploreMoreOptionsProductList, searchReasonInput, selectedFilters, checkFilterExists, productBasicDetail,
    productInventory, filteredProducts, freeLoyaltyTea, recomOfferData, recommendedProductData, selectedProductId,
    handleAddSavedChai, currentSelectedValue, isRecommendationInCart,newRecomData
  ]);

  const getCartTypeAndAmount = React.useCallback(() => {
    let OrderType = null;
    let bev_count = 0;
    let food_count = 0;
    let Bakery_count = 0;
    let cartAmount = 0;

    if(UtilityService.checkEmpty(cartItems)){
      return {
        OrderType:"DEFAULT",
        cartAmount:0
      }
    }

    cartItems?.forEach((item) => {
      if (item.productType === 7) {
        food_count += 1;
      } else if (item.productType === 6 || item.productType === 5) {
        bev_count += 1;
      } else if (item.productType === 10) {
        Bakery_count += 1;
      }
      cartAmount += item.amount;
    });

    if (food_count > 0 && bev_count === 0) {
      OrderType = "FOOD_ONLY";
    } else if (bev_count > 0 && food_count === 0) {
      OrderType = "BEV_ONLY";
    } else if((bev_count > 0 && food_count > 0)){
      OrderType = "FOOD_BEV";
    }else{
      OrderType = "DEFAULT";
    }

    return { OrderType, cartAmount };
  }, [cartItems]);

  const recommendationData = useCallback((data) => {
    const orderedProductsArray = [];
    data?.forEach((product) => {
      if (product.id.id >= 0) {
        orderedProductsArray.push({
          productId: product.id.id,
          display: true,
          recomReason: null,
          ruleNumber: !UtilityService.checkEmpty(product.ruleNumber) ? product.ruleNumber : null
        });
      }
    });
    dispatch(setNewRecmData(orderedProductsArray));
  }, [dispatch]);


  useEffect(() => {
    if (!UtilityService.checkEmpty(cartRulesData)) {

      const { OrderType, cartAmount } = getCartTypeAndAmount();
      const res = recommendationService.getDetailedRecommendations(OrderType, cartAmount);
      console.log("res::::🔥🔥🔥",res);
      recommendationData(res?.products);
    }
  }, [cartItems, getCartTypeAndAmount, recommendationData, dispatch, cartRulesData]);

  return (
    <Stack
      sx={{
        position: 'relative',
        flexDirection: 'column',
        width: '100%',
      }}
    >
      {isTablefooterMenuModalOpen || showOrderSummary ? (
        <Box
          sx={{
            position: 'absolute',
            zIndex: 6,
            height: '98%',
            top: '1%',
            width: '100%',
            // backdropFilter: `blur(2.5px)`,
            // backgroundColor: theme.palette.specialColors.BLUR_BLACK_SCREEN,
            borderRadius: '10px',
          }}
          onClick={() => {
            setShowBlurScreen(false);
            setShowFilters(false);
            dispatch(setTableFooterMenuModalState(false));
          }}
        />
      ) : (
        <Box />
      )}
      <Stack sx={{
        flex: 5, flexDirection: 'row', pt: 1.25,
      }}>

        <Stack sx={{
          flex: 6.5, mr: 1.25,
        }}>
          <Stack
            sx={{
              flex: 9,
              // pb: 1,
              mb: 0.4,
              border: '1px solid',
              flexDirection: 'column',
              borderColor: theme.palette.grey[400],
              // border:"1px solid"
              borderRadius: '10px'
            }}
          >
            {/* Top Categories */}
            <Stack
              direction="row"
              sx={{
                flexWrap: 'wrap',
                px: 1,
                pt: 1.5,
                // border:"1px solid black"
              }}
            >
              {showCategorySelection &&
                Object.values(categoryWiseProduct)?.map((category) => (

                  <Typography
                    // variant="body6"
                    key={`categoryWiseProduct_${category?.name}`}
                    sx={{
                      lineHeight: 1.3,
                      p: 0.2,
                      borderRadius: 1,
                      mb: 1.25,
                      mx: 1,
                      width: 140,
                      height: 46,
                      display: 'flex',
                      textAlign: 'center',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                      backgroundColor: theme.palette.neutral.white,
                      border: `1px solid ${theme.palette.grey[400]}`,
                      fontFamily: 'Nunito-SemiBold',
                      fontSize: 16,
                      // boxShadow: `0.5px 0.5px 0.5px ${theme.palette.neutral.grey}`,
                      // eslint-disable-next-line eqeqeq
                      ...((selectedCategory == category?.id) && {
                        backgroundColor: theme.palette.leafGreen[500],
                        color: theme.palette.neutral.white,
                        border: 0,
                        boxShadow: `0px 0px 0px ${theme.palette.leafGreen[500]}`,
                      }),
                    }}
                    onClick={() => {
                      setSelectedCategory(category?.id);
                      // can be optimised
                      const foundIndex = Object.values(categoryWiseProduct).indexOf(category);
                      const catRef = categoryRefs.current[foundIndex]

                      UtilityService.allowEvent = false;
                      catRef?.scrollIntoView();
                      setTimeout(() => {
                        UtilityService.allowEvent = true;
                        console.log('reseting ::: ');
                      }, 1000);
                    }}
                  >
                    {category?.name}
                  </Typography>
                ))}
            </Stack>

            {/* Filters */}
            <Stack
              direction="row"
              sx={{
                position: 'relative',
                flexWrap: 'wrap',
                // TEMP
                // border: '1px solid',
                px: 3,
                width: '100%',
                maxWidth: 880,
                marginLeft: -1
              }}
            >
              <Stack sx={{
              }}>
                <Button
                  variant="outlined"
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minWidth: 'fit-content',
                    borderColor: theme.palette.neutral.lightGrey2,
                    color: theme.palette.neutral.black,
                    mr: 1,
                    '&:hover': {
                      borderColor: theme.palette.neutral.black,
                    },
                    ...(!UtilityService.checkEmpty(selectedFilters) && {
                      borderColor: theme.palette.clayRed[500],
                      color: theme.palette.clayRed[500],
                      '&:hover': {
                        borderColor: theme.palette.clayRed[500],
                      },
                    }),
                  }}
                  onClick={() => handleFilters()}
                >
                  <StyledIcon
                    sx={{
                      mr: 0.5,
                      height: 15,
                      width: 15,
                    }}
                  >
                    {iconConfig.filterIcon}
                  </StyledIcon>
                  <Typography variant='body6' sx={{}}>Filters</Typography>
                  <StyledIcon
                    sx={{
                      ml: 0.5,
                      height: 15,
                      width: 15,
                      ...(showFilters && {
                        transform: `rotate(180deg)`,
                      }),
                    }}
                  >
                    {iconConfig.downArrow}
                  </StyledIcon>
                </Button>
              </Stack>
              <Stack
                sx={{
                  flex: 1,
                  flexDirection: 'row',
                  overflowX: 'scroll',
                  // q1
                }}
              >
                <Stack
                  sx={{
                    flex: 1,
                    // flexWrap: 'wrap',
                    // q1
                    overflowX: 'scroll',
                  }}
                  direction="row"
                >
                  {Object.values(selectedFilters)?.map((productFilter, index) => (
                    <Button
                      variant="outlined"
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        minWidth: 'fit-content',
                        cursor: 'pointer',
                        borderColor: theme.palette.neutral.grey,
                        color: theme.palette.neutral.black,
                        mr: 1,
                        ...(selectedFilters[productFilter?.name] && {
                          borderColor: theme.palette.clayRed[500],
                          color: theme.palette.clayRed[500],
                          '&:hover': {
                            borderColor: theme.palette.clayRed[500],
                          },
                        }),
                        '&:hover': {
                          borderColor: theme.palette.neutral.grey,
                        },
                      }}
                      onClick={() => {
                        if (UtilityService.checkEmpty(selectedFilters?.[productFilter?.name])) {
                          setSelectedFilters((prev) => ({
                            ...prev,
                            [productFilter?.name]: productFilter,
                          }));
                        } else {
                          const updatedSelectedFilters = JSON.parse(
                            JSON.stringify(selectedFilters)
                          );
                          delete updatedSelectedFilters?.[productFilter?.name];
                          setSelectedFilters(updatedSelectedFilters);
                        }
                      }}
                    >
                      <Typography variant='body6' sx={{}}>{productFilter?.name}</Typography>
                      {selectedFilters[productFilter?.name] && (
                        <StyledIcon
                          sx={{
                            ml: 1,
                            height: 18,
                            width: 18,
                            background: theme.palette.clayRed[500],
                            borderRadius: 12,
                            color: theme.palette.neutral.white,
                          }}
                        >
                          {iconConfig.crossIcon}
                        </StyledIcon>
                      )}
                    </Button>
                  ))}
                  {Object.keys(unitDetail?.tagMap)?.map((tag) => (
                    <>
                      {Object.values(unitDetail?.tagMap?.[tag])?.map((productFilter, index) => (
                        <>
                          {productFilter?.attributes?.defaultVisibility === "Y"
                            && UtilityService.checkEmpty(selectedFilters[productFilter?.name]) ?
                            <Button
                              variant="outlined"
                              sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                minWidth: 'fit-content',
                                cursor: 'pointer',
                                borderColor: theme.palette.neutral.lightGrey2,
                                color: theme.palette.neutral.black,
                                mr: 1,
                                ...(selectedFilters[productFilter?.name] && {
                                  borderColor: theme.palette.clayRed[500],
                                  color: theme.palette.clayRed[500],
                                  '&:hover': {
                                    borderColor: theme.palette.clayRed[500],
                                  },
                                }),
                                '&:hover': {
                                  borderColor: theme.palette.neutral.grey,
                                },
                              }}
                              onClick={() => {
                                if (UtilityService.checkEmpty(selectedFilters?.[productFilter?.name])) {
                                  setSelectedFilters((prev) => ({
                                    ...prev,
                                    [productFilter?.name]: productFilter,
                                  }));
                                } else {
                                  const updatedSelectedFilters = JSON.parse(
                                    JSON.stringify(selectedFilters)
                                  );
                                  delete updatedSelectedFilters?.[productFilter?.name];
                                  setSelectedFilters(updatedSelectedFilters);
                                }
                              }}
                            >
                              <Typography variant='body6' sx={{}}>{productFilter?.name}</Typography>
                              {selectedFilters[productFilter?.name] && (
                                <StyledIcon
                                  sx={{
                                    ml: 1,
                                    height: 18,
                                    width: 18,
                                    background: theme.palette.clayRed[500],
                                    borderRadius: 12,
                                    color: theme.palette.neutral.white,
                                  }}
                                >
                                  {iconConfig.crossIcon}
                                </StyledIcon>
                              )}
                            </Button>
                            :
                            <Box />
                          }
                        </>
                      ))}
                    </>
                  ))}
                </Stack>
                {!showCategorySelection ? (
                  <Stack sx={{
                    pl: 2,
                  }}>
                    <Button
                      variant={UtilityService.checkEmpty(selectedFilters) ? "disabled" : "outlined"}
                      sx={{
                        borderColor: theme.palette.clayRed[500],
                        color: theme.palette.clayRed[500],
                        '&:hover': {
                          borderColor: theme.palette.neutral.grey,
                        },
                      }}
                      onClick={() => {
                        setSelectedFilters({});
                      }}
                    >
                      X Clear Filters
                    </Button>
                  </Stack>
                ) : (
                  <Box />
                )}
              </Stack>

              {showFilters && isTablefooterMenuModalOpen && (
                <Stack
                  sx={{
                    position: 'absolute',
                    top: `calc(100% + 10px)`,
                    background: 'white',
                    zIndex: 7,
                    p: 2,
                    maxWidth: '65%',
                    borderRadius: 2,
                  }}
                >
                  {Object.keys(unitDetail?.tagMap)?.map((tag) => (
                    <Stack>
                      <Typography
                        variant="subtitle6"
                        sx={{
                          mb: 2,
                        }}
                      >
                        {tag}
                      </Typography>
                      <Stack
                        sx={{
                          flexWrap: 'wrap',
                        }}
                        direction="row"
                      >
                        {Object.values(unitDetail?.tagMap?.[tag])?.map((productFilter, index) => (
                          <Button
                            variant="outlined"
                            sx={{
                              display: 'flex',
                              justifyContent: 'center',
                              borderColor: theme.palette.grey[400],
                              color: theme.palette.grey[10],
                              mr: 2,
                              mb: 1.7,
                              cursor: 'pointer',
                              ...(tempSelectedFilters[productFilter?.name] && {
                                borderColor: theme.palette.clayRed[500],
                                color: theme.palette.clayRed[500],
                                '&:hover': {
                                  borderColor: theme.palette.clayRed[500],
                                },
                              }),

                              '&:hover': {
                                borderColor: theme.palette.neutral.black,
                              },
                            }}
                            onClick={() => {
                              if (
                                UtilityService.checkEmpty(
                                  tempSelectedFilters?.[productFilter?.name]
                                )
                              ) {
                                setTempSelectedFilters((prev) => ({
                                  ...prev,
                                  [productFilter?.name]: productFilter,
                                }));
                              } else {
                                const updatedTempSelectedFilters = JSON.parse(
                                  JSON.stringify(tempSelectedFilters)
                                );
                                delete updatedTempSelectedFilters?.[productFilter?.name];
                                setTempSelectedFilters(updatedTempSelectedFilters);
                              }
                            }}
                          >
                            <Typography
                              variant="body6"
                            >{productFilter?.name}</Typography>
                            {tempSelectedFilters[productFilter?.name] && (
                              <StyledIcon
                                sx={{
                                  ml: 1,
                                  height: 18,
                                  width: 18,
                                  background: theme.palette.clayRed[500],
                                  borderRadius: 12,
                                  color: theme.palette.neutral.white,
                                }}
                              >
                                {iconConfig.crossIcon}
                              </StyledIcon>
                            )}
                          </Button>
                        ))}
                      </Stack>
                    </Stack>
                  ))}
                  <Stack
                    sx={{
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      mt: 1,
                      flexDirection: 'row',
                    }}
                  >
                    <Button
                      variant={UtilityService.checkEmpty(tempSelectedFilters) ? "disabled" : "outlined"}
                      sx={{
                        borderColor: theme.palette.clayRed[500],
                        color: theme.palette.clayRed[500],
                        width: '48%',
                        '&:hover': {
                          borderColor: theme.palette.neutral.grey,
                        }
                      }}
                      onClick={() => {
                        setSelectedFilters({});
                        dispatch(setTableFooterMenuModalState(false));
                        setShowBlurScreen(false);
                        setShowFilters(false);
                      }}
                    >
                      X Clear Filters
                    </Button>
                    <Button
                      variant="contained"
                      sx={{
                        width: '48%',
                      }}
                      onClick={() => {
                        setSelectedFilters(tempSelectedFilters);
                        dispatch(setTableFooterMenuModalState(false));
                        setShowBlurScreen(false);
                        setShowFilters(false);
                      }}
                    >
                      Apply Filter
                    </Button>
                  </Stack>
                </Stack>
              )}
            </Stack>

            {/* Search Bar */}
            <Stack
              direction="row"
              sx={{
                flex: 1,
                width: '95%',
                justifyContent: 'space-between',
                alignItems: 'center',
                mx: 1,
                mb: -3,
                // TEMP
                // border: '1px solid',
                background: theme.palette.neutral.white,
              }}
            >
              {/* <OrderSearchBar
                categoryData={categoryWiseProduct}
                products={productBasicDetail?.products}
                addProduct={(id) => {
                  addProduct(id);
                }}
              /> */}
              <Stack sx={{ display: 'flex', flex: 6, px: 1 }}>
                <TextField
                  placeholder="Search"
                  value={searchReasonInput}
                  onChange={(event) => {
                    setSearchReasonInput(event.target.value);
                  }}
                  onFocus={() => {
                    setShowCategorySelection(false);
                  }}
                  InputProps={{
                    style: { height: 40, textAlign: 'center' },
                    startAdornment: (
                      <InputAdornment position="start">
                        <StyledIcon sx={{ color: theme.palette.neutral.black, height: 18, width: 18}}>
                          {iconConfig.search}
                        </StyledIcon>
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <StyledIcon
                          sx={{ color: theme.palette.neutral.grey, cursor: 'pointer' }}
                          onClick={() => {
                            setSearchReasonInput('');
                          }}
                        >
                          {iconConfig.crossIcon}
                        </StyledIcon>
                      </InputAdornment>
                    ),
                  }}
                    sx={{
                    '& .MuiInputBase-input::placeholder': {
                      color: 'rgba(1,1,1,1)',
                    },
                  }}
                />
              </Stack>
              <Stack
                sx={{
                  mx: 1,
                }}
              >
                {showCategorySelection ? (
                  <Button
                    variant="outlined"
                    disabled={UtilityService.checkEmpty(selectedFilters)}
                    sx={{
                      borderColor: theme.palette.clayRed[500],
                      color: theme.palette.clayRed[500],
                      height: 40,
                      '&:hover': {
                        borderColor: theme.palette.neutral.grey,
                      }
                    }}
                    onClick={() => {
                      setSelectedFilters({});
                    }}
                  >
                    X Clear Filters
                  </Button>
                ) : (
                  <Button
                    variant="outlined"
                    sx={{
                      borderColor: theme.palette.clayRed[500],
                      color: theme.palette.clayRed[500],
                      '&:hover': {
                        borderColor: theme.palette.neutral.grey,
                      },
                    }}
                    onClick={() => {
                      setSearchReasonInput('');
                      setShowCategorySelection(true);
                    }}
                  >
                    Close Search
                  </Button>
                )}
              </Stack>
              {isShowVegNonVegFilter ? (
                <Stack
                  sx={{
                    flexDirection: 'row',
                    flex: 1,
                    alignItems: 'center',
                  }}
                >
                  <Typography variant="caption" sx={{ minWidth: '55px', textAlign: 'center' }}>
                    {isVeg ? 'Veg' : ''}
                    {isNonVeg ? 'Non-Veg' : ''}
                  </Typography>
                  <VegNonvegTag
                    isVeg={isVeg}
                    isNonVeg={isNonVeg}
                    onVegClicked={(val) => {
                      setIsVeg(val);
                      setIsNonVeg(false);
                    }}
                    onNonVegClicked={(val) => {
                      setIsVeg(false);
                      setIsNonVeg(val);
                    }}
                  />
                </Stack>
              ) : (
                <Box />
              )}
            </Stack>

            {/* Main Product Container */}
            {
              getMainContainer
            }

          </Stack>
        </Stack>

        {false &&
          (!UtilityService.checkEmpty(recommendedProductData) ||
            !UtilityService.checkEmpty(savedChais)) ? (
          <UnSelectedBackground
            sx={{
              // width: '38%',
              // position: 'absolute',
              top: '0',
              right: 0,
              flexDirection: 'column',
              display: 'flex',
              flex: 2.5,
              // bottom: '0',
              p: 1,
              mb: 1.25,
              borderRadius: '12px',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                position: 'relative',
                flex: 3,
                justifyContent: 'center',
                width: '100%',
                mb: 2,
              }}
            >
              <Stack
                sx={{
                  display: 'flex',
                  flex: 7,
                  position: 'absolute',
                  top: '0',
                  bottom: '0',
                  overflowY: 'auto',
                  alignItems: 'center',
                  width: '100%',
                }}
              >
                {!UtilityService.checkEmpty(savedChais) && countSavedChais() > 0 ? (
                  <Stack sx={{ width: '100%' }}>
                    <Stack
                      direction="row"
                      justifyContent="space-between"
                      alignItems="center"
                      sx={{
                        width: '100%',
                        pr: 3,
                        // cursor: 'pointer'
                      }}
                    // onClick={()=>{
                    //   if(recommendationPannelState[0] && !recommendationPannelState[1])
                    //   {
                    //     setRecommendationPannelState([!recommendationPannelState[0],!recommendationPannelState[1]])
                    //   }
                    //   else{
                    //     setRecommendationPannelState([!recommendationPannelState[0],recommendationPannelState[1]])
                    //   }
                    // }}
                    >
                      <Typography variant="body4" sx={{ ml: 1, color: theme.palette.neutral.grey }}>
                        SAVED ITEMS
                      </Typography>
                      {/* <StyledIcon
                    sx={{ color: theme.palette.neutral.grey,
                      transform: 'rotate(0deg)',
                        ...(recommendationPannelState[0] && { transform: 'rotate(180deg)' }), }}
                      >
                      {iconConfig.downArrow}
                    </StyledIcon> */}
                    </Stack>
                    {recommendationPannelState[0] ? (
                      <Stack
                        spacing={1}
                        sx={{
                          display: 'flex',
                          width: '100%',
                          justifyContent: 'flex-start',
                          flexDirection: 'column',
                          overflowY: 'auto',
                          p: 1,
                        }}
                      >
                        {savedChais?.map((savedChai, index) =>
                          !UtilityService.checkEmpty(CartAction.priceExits(savedChai.productId)) ? (
                            <Box key={`savedChais${index}`} sx={{ position: 'relative' }}>
                              {!CartAction.isInventoryAvalable(savedChai.productId) ? (
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    height: '100%',
                                    width: '100%',
                                    borderRadius: '12px',
                                    top: 0,
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    right: 0,
                                    zIndex: 1,
                                    color: 'transparent',
                                    background: theme.palette.specialColors.UNSELECTED_BACKGROUND,
                                    '&:hover': {
                                      color: theme.palette.neutral.black,
                                      background: theme.palette.specialColors.DISABLE_GREY,
                                      ...(!CartAction.isInventoryAvalable(savedChai.productId) && {
                                        background: theme.palette.specialColors.DISABLE_RED,
                                      }),
                                    },
                                  }}
                                >
                                  Stock Out
                                </Box>
                              ) : (
                                <Box />
                              )}
                              <SavedChaiProductCardV2
                                product={savedChai}
                                index={index}
                                onAdd={() => { }}
                              />
                            </Box>
                          ) : (
                            <Box />
                          )
                        )}
                      </Stack>
                    ) : (
                      <Box />
                    )}
                  </Stack>
                ) : (
                  <Box />
                )}

                {!UtilityService.checkEmpty(savedChais) && countSavedChais() > 0 ? (
                  <Box
                    sx={{
                      width: '96%',
                      mx: '2%',
                      my: 2,
                      height: '2px',
                      backgroundColor: theme.palette.neutral.lightGrey2,
                    }}
                  >
                    &nbsp;
                  </Box>
                ) : (
                  <Box />
                )}

                {!UtilityService.checkEmpty(recommendedProductData) ? (
                  <Stack sx={{ width: '100%' }}>
                    <Stack
                      direction="row"
                      justifyContent="space-between"
                      alignItems="center"
                      sx={{
                        width: '100%',
                        pr: 3,
                        //  cursor: 'pointer'
                      }}
                    // onClick={() => {
                    //   if (!recommendationPannelState[0] && recommendationPannelState[1]) {
                    //     setRecommendationPannelState([
                    //       !recommendationPannelState[0],
                    //       !recommendationPannelState[1],
                    //     ]);
                    //   } else {
                    //     setRecommendationPannelState([
                    //       recommendationPannelState[0],
                    //       !recommendationPannelState[1],
                    //     ]);
                    //   }
                    // }}
                    >
                      <Typography variant="body4" sx={{ ml: 1, color: theme.palette.neutral.grey }}>
                        RECOMMENDATIONS
                      </Typography>
                      {/* <StyledIcon
                        sx={{
                          color: theme.palette.neutral.grey,
                          transform: 'rotate(0deg)',
                          visibility: 'hidden',
                          ...(recommendationPannelState[1] && { transform: 'rotate(180deg)' }),
                        }}
                      >
                        {iconConfig.downArrow}
                      </StyledIcon> */}
                    </Stack>
                    <Stack
                      spacing={1}
                      sx={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'flex-start',
                        flexDirection: 'column',
                        overflowY: 'auto',
                        p: 1,
                      }}
                    >
                      {recommendationPannelState[1] || true ? (
                        Object.keys(recommendedProductDataIndex)?.map((index) =>
                          !UtilityService.checkEmpty(
                            CartAction.priceExits(recommendedProductDataIndex[index])
                          ) &&
                            recommendedProductData[recommendedProductDataIndex[index]].display &&
                            CartAction.isInventoryAvalable(recommendedProductDataIndex[index]) ? (
                            <Box
                              key={`recommendedProductData${index}`}
                              sx={{ position: 'relative' }}
                            >
                              <RecomendationProductCard
                                productId={recommendedProductDataIndex[index]}
                                addProduct={(productId) => addProduct(productId, true)}
                              />
                            </Box>
                          ) : (
                            ''
                          )
                        )
                      ) : (
                        <Box />
                      )}
                      {recommendationPannelState[1] ? (
                        Object.keys(recommendedProductDataIndex)?.map((index) =>
                          !UtilityService.checkEmpty(
                            CartAction.priceExits(recommendedProductDataIndex[index])
                          ) &&
                            (!recommendedProductData[recommendedProductDataIndex[index]].display ||
                              !CartAction.isInventoryAvalable(recommendedProductDataIndex[index])) ? (
                            <Box
                              key={`notRecommendedProductData${index}`}
                              sx={{ position: 'relative' }}
                            >
                              {!recommendedProductData[recommendedProductDataIndex[index]]
                                .display ||
                                !CartAction.isInventoryAvalable(
                                  recommendedProductDataIndex[index]
                                ) ? (
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    height: '100%',
                                    width: '100%',
                                    borderRadius: '12px',
                                    top: 0,
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    right: 0,
                                    zIndex: 1,
                                    color: 'transparent',
                                    background: theme.palette.specialColors.UNSELECTED_BACKGROUND,
                                    '&:hover': {
                                      color: theme.palette.neutral.black,
                                      background: theme.palette.specialColors.DISABLE_GREY,
                                      ...(!CartAction.isInventoryAvalable(
                                        recommendedProductDataIndex[index]
                                      ) && {
                                        background: theme.palette.specialColors.DISABLE_RED,
                                      }),
                                    },
                                  }}
                                >
                                  {!CartAction.isInventoryAvalable(
                                    recommendedProductDataIndex[index]
                                  )
                                    ? 'Stock Out'
                                    : 'Not Recommended'}
                                </Box>
                              ) : (
                                ''
                              )}
                              <RecomendationProductCard
                                productId={recommendedProductDataIndex[index]}
                                addProduct={(productId) => addProduct(productId, true)}
                              />
                            </Box>
                          ) : (
                            ''
                          )
                        )
                      ) : (
                        <Box />
                      )}
                    </Stack>
                  </Stack>
                ) : (
                  <Box />
                )}
              </Stack>
            </Box>
          </UnSelectedBackground>
        ) : (
          <Box />
        )}
      </Stack>
    </Stack>
  );
}
