import React, { useState, useEffect } from 'react';
import { Box, Stack, Typography, useTheme } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import UtilityService from '../../../services/UtilityService';
import CartAction from '../../../sections/cart/CartAction';
import RecomendationProductCard from '../../productCards/RecomendationProductCard';
import { ProductService } from '../../../services/ProductService';
import Constants from '../../../utils/Constants';
import { setRightSideBar } from '../../../pages/home/<USER>';
import CustomerAction from '../../customerInfo/customerAction';
import ProgressTabButtonsAction from '../../progressTabButtons/ProgressTabButtonsAction';
import { emitMessage } from '../../../cafeAppCommunication/cafeAppCommunication';

export default function RecommendationsView() {
  const theme = useTheme();
  const dispatch = useDispatch();
  const {
    productBasicDetail,
    unitMenu,
    productInventory,
    productPrice,
    productCustomization,
    recomOfferData,
    productImageMap,
    unitSubscriptionProducts,
    allExploreMoreOptionsProductList,
    isRecommendationInCart,
  } = useSelector((store) => store.orderSlice);

  const newRecomData = useSelector((state) => state.offersSlice.newRecomData);
  const recommendedProductData = Array.isArray(newRecomData) ? newRecomData : [];

  const [recommendationPannelState, setRecommendationPannelState] = useState([true,true]);
  // Emit to Cafe app whenever newRecomData is updated
  useEffect(() => {
    if (
      !UtilityService.checkEmpty(newRecomData) &&
      Array.isArray(newRecomData)
    ) {
      const validRecoms = CartAction.getValidRecommendations(newRecomData);
      if (validRecoms.length > 0) {
        emitMessage({ NEW_RECOMMENDATION_DATA: validRecoms });
      }
    }
  }, [newRecomData]);
  
  if (
    UtilityService.checkEmpty(recommendedProductData) ||
    !Array.isArray(recommendedProductData) ||
    recommendedProductData.length === 0 ||
    CartAction.getValidRecommendations(recommendedProductData).length === 0
  ) {
    return null;
  }

  const addProduct = (productId, isRecommProduct) => {
    const productDetail = productBasicDetail.products[productId];
    const customization = productCustomization[productId];
    const selectedDimension = ProductService.getDefaultDimension(customization);
    const recommendedProduct = recommendedProductData.find(p => p.productId === productId);
    const ruleNumber = recommendedProduct?.ruleNumber;

    if (productDetail?.taxCode === Constants.TAX_CODE.COMBO) {
      dispatch(
        setRightSideBar({
          open: true,
          code: Constants.RIGTHSIDE_BAR.COMBO_ITEM_SELECTION,
          data: { productDetail, customization },
        })
      );
    } else if (
      UtilityService.isChaiEligibleForSpectatorMode(productDetail.id.id) ||
      CustomerAction.isEligibleForLoyaltyBurn(productDetail.id.id, selectedDimension)
    ) {
      dispatch(
        setRightSideBar({
          open: true,
          code: Constants.RIGTHSIDE_BAR.CUSTOMIZE_ORDER,
          data: { productDetail, customization, isCameFromSpectatorMode: false, isRecommProduct },
        })
      );

      if (!UtilityService.checkEmpty(Constants.MILK_SELECTION[productDetail.id.id])) {
        ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.desiChaiCustomised, false);
      }
    } else {
      const isCustomisable = UtilityService.checkEmpty(unitSubscriptionProducts[productId]);

      CartAction.addItemToCart(
        CartAction.getProcessedItem({
          productData: { productDetail, customization },
          recProd: isRecommProduct || false,
          selectedVariants: ProductService.setDefaultVariant(customization, selectedDimension),
          selectedMilkType: ProductService.setDefaultMilkSelection(productDetail.id.id),
          isCustomisable,
          selectedDimension,
          ruleNumber,
        })
      );

      if (isRecommProduct === true) {
        try {
          ProgressTabButtonsAction.keyIncrementer(['recomUsed']);
          ProgressTabButtonsAction.updateBoolInPushJson('recomUsed');
          ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.recommendationAdded, true);
        } catch (e) {
          console.error('Error while updating recom goal', e);
        }
      }
    }
  };

  return (
    <Stack
      sx={{
        width: '100%',
        position: 'relative',
        overflowY: 'auto',
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          width: '100%',
          pr: 3,
          zIndex: 1,
          background: !isRecommendationInCart && theme.palette.clayRed[500],
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          height: '60px',
        }}
      >
        <Typography
          variant="body4"
          sx={{
            ml: 2,
            marginY: 1,
            color: isRecommendationInCart
              ? theme.palette.leafGreen[500]
              : theme.palette.neutral.white,
          }}
        >
          Recommendations ⭐
        </Typography>
      </Stack>

      <Stack
        spacing={1}
        sx={{
          mt: 1,
          display: 'flex',
          width: '100%',
          justifyContent: 'flex-start',
          flexDirection: 'column',
          overflow: 'scroll',
          px: 1,
          '&::-webkit-scrollbar': {
            width: '7px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
            border: 'none',
            boxShadow: 'none',
          },
          '&::-webkit-scrollbar-thumb': {
            background: isRecommendationInCart
              ? `linear-gradient(180deg, ${theme.palette.leafGreen[500]}, ${theme.palette.leafGreen[200]})`
              : `linear-gradient(180deg, ${theme.palette.clayRed[500]}, ${theme.palette.clayRed[200]})`,
          },
        }}
      >
        {recommendationPannelState[1] && 
          recommendedProductData?.map((product, index) => {
            const id = product.productId;

            const isPriceAvailable = !UtilityService.checkEmpty(CartAction.priceExits(id));
            const isDisplay = product.display;
            const isInventoryAvailable = CartAction.isInventoryAvalable(id);

            if (!isPriceAvailable && !isDisplay && !isInventoryAvailable) return null;

            const shouldShowOverlay = isPriceAvailable && (!isDisplay || !isInventoryAvailable);

            return (
              <Box key={`recommendedProductData_${id}`} sx={{ position: 'relative' }}>
                {shouldShowOverlay && (
                  <Box
                    sx={{
                      position: 'absolute',
                      height: '100%',
                      width: '100%',
                      borderRadius: '12px',
                      top: 0,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      right: 0,
                      zIndex: 1,
                      color: 'transparent',
                      background: theme.palette.specialColors.UNSELECTED_BACKGROUND,
                      '&:hover': {
                        color: theme.palette.neutral.black,
                        background: !isInventoryAvailable
                          ? theme.palette.specialColors.DISABLE_RED
                          : theme.palette.specialColors.DISABLE_GREY,
                      },
                    }}
                  >
                    {!isInventoryAvailable ? 'Stock Out' : 'Not Recommended'}
                  </Box>
                )}

                <RecomendationProductCard
                  productId={id}
                  addProduct={(productId) => addProduct(productId, true)}
                />
              </Box>
            );
          })}

      </Stack>
    </Stack>
  );
}
