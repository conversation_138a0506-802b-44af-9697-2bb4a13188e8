import { CircularProgress, Typography, useTheme } from '@mui/material';
import { Box, Stack } from '@mui/system';
import PropTypes from 'prop-types';
import { useMemo } from 'react';
import { UnSelectedBackground } from '../../layouts/baseStyles';
import { ProductService } from '../../services/ProductService';
import Constants from '../../utils/Constants';
import { StyledIcon } from '../../layouts/dashboard/header/styles';
import { iconConfig } from '../../icon-config';
import { useSelector } from '../../redux/store';
import UtilityService from '../../services/UtilityService';
import typography from '../../theme/typography';
import Image from '../image/Image';
import CartAction from '../../sections/cart/CartAction';

RecomendationProductCard.propTypes = {
  productId: PropTypes.number,
  addProduct: PropTypes.func,
};

export default function RecomendationProductCard({ productId, addProduct }) {
  const theme = useTheme();
  const { productBasicDetail, productPrice, productCustomization, recomOfferData, recommendedProductData: recomData, allExploreMoreOptionsProductList, isRecommendationInCart } = useSelector(
    (store) => store.orderSlice
  );
  const newRecomData = useSelector((state)=>state.offersSlice.newRecomData);
  const defaultDimension = ProductService.getDefaultDimension(productCustomization[productId]);
  let recommendedProductData;
  
  if (UtilityService.checkEmpty(newRecomData)) {
    recommendedProductData = recomData;
  } else {
    recommendedProductData = newRecomData;
  }
  if (
    UtilityService.checkEmpty(defaultDimension) ||
    UtilityService.checkEmpty(productPrice.prices[productId])
  ) {
    return <Box />;
  }
  return (
    <UnSelectedBackground
      onClick={() => addProduct(productId)}
      sx={{
        display: 'flex',
        cursor: 'pointer',
        borderRadius: '12px',
        justifyContent: 'space-between',
        alignItems: 'center',
        p: 1.125,
      }}
    >
      <Stack
        direction="row"
        flex={2}
        sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
      >

        {(allExploreMoreOptionsProductList[productId]?.recomReason || (!UtilityService.checkEmpty(productBasicDetail?.products?.[productId]?.tag) && recommendedProductData[productId]?.display))
          && (productId !== Constants.DESI_CHAI_ID && productId !== Constants.PRODUCTID.BLACK_TEA && productId !== Constants.PRODUCTID.GREEN_TEA) 
        ?

              <Box  
              sx={{
                position:'absolute',
                top:'-3.5px',
                left:'11px',
                minWidth:'100px',
                display:'flex',
                zIndex:1,
                height:'12px',
                // width:'27.8%',
                width:'fit-content',
                justifyContent:'center',
                borderTop: `20px solid ${theme.palette.leafGreen[500]}`,
                borderLeft: '8px solid transparent',
                borderRight: '8px solid transparent',
                  p:0.1}}
                >
                  <Box 
                  sx={{
                    position:'absolute',
                    top:'-28px',
                    right:'-11px',
                    display:'flex',
                    // minWidth:'118px',
                    zIndex:1,
                    height:'12px',
                    // width:'30.5%',
                    width:`calc(100% + 22.5px)`,
                    borderBottom: `4px solid ${theme.palette.leafGreen[500]}`,
                    borderLeft: '4px solid transparent',
                    borderRight: '4px solid transparent',
                    color:'transparent'
                  }} />

                  <Typography 
                  variant='subtitle3'
                  sx={{
                    // width:'27%',
                    width:'fit-content',
                    display:'flex',
                    justifyContent:'center',
                    zIndex:1,
                    mt:'-20px',
                    height:'11px',
                    fontSize:'14px',
                    color:theme.palette.neutral.white,
                    textTransform:'capitalize'
                  }}>
                    {allExploreMoreOptionsProductList[productId]?.recomReason?.toLowerCase() || productBasicDetail?.products?.[productId]?.tag[0]?.toLowerCase()}
                  </Typography>
              </Box>
              :
              <Box />
            } 

        <Image
          src={ProductService.getProductImage(productId, Constants.IMAGE_TYPES.GRID_MENU_100X100)}
          sx={{
            flex: '4',
            height: '60px',
            width: '60px',
            mr: 2,
            borderRadius: '9px',
            objectFit: 'contain',
          }}
        />
      </Stack>
      <Stack sx={{ flex: '6' }}>
        <Typography sx={{ ...typography.body6, fontWeight: 700, flex: 4, mr: 2, lineHeight: 1.2 }}>
          {productBasicDetail.products[productId].productAliasName}
        </Typography>
        <Typography sx={{ ...typography.body4 , width:'100%' }}>
          &#8377;&nbsp;{productPrice.prices[productId]?.prices[defaultDimension]?.price.toFixed(0)}
          {CartAction.isEligibleForRecommendationOffer(productId)? (
            <Typography
              variant="caption"
              sx={{
                height: '20px',
                width: '120px',
                bgcolor: 'red',
                borderRadius: 0.5,
                zIndex: 5,
                mx: 0.4,
                px: 0.3,
                textAlign: 'center',
                color: 'white',
                background: theme.palette.clayRed[500],
              }}
            >
              {recomOfferData?.offerTag}
            </Typography>
          ) : (
            <Box />
          )}
        </Typography>
      </Stack>
      {/* <CircularProgress sx={{margin: 'auto', color: theme.palette.neutral.grey}}/> */}
      {/* <Stack
        direction="row"
        flex={1}
        sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
      >
        <StyledIcon sx={{ color: theme.palette.leafGreen[500] }}>{iconConfig.addCircle}</StyledIcon>
      </Stack> */}
    </UnSelectedBackground>
  );
}
