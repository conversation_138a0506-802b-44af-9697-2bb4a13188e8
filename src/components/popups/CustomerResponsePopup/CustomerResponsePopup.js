import React, { useEffect, useState } from 'react';
import { Box, Button, Typography, useTheme } from '@mui/material';
import PropTypes from 'prop-types';
import { Stack, styled } from '@mui/system';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { MuiOtpInput } from 'mui-one-time-password-input';

import { PATH_HOME, PATH_MENU } from '../../../routes/paths';
import { dispatch, useSelector } from '../../../redux/store';
import { setCustomerResponsePopup, setMembershipOptionDialogue } from '../PopupsSlice';
import { StyledIcon } from '../../../layouts/dashboard/header/styles';
import { iconConfig } from '../../../icon-config';
import Constants from '../../../utils/Constants';
import { updateAutoClickOnProceedBtn } from '../../../sections/cart/CartSlice';
import CommunicationService from '../../../cafeAppCommunication/communicationService';
import HomeAction from '../../../pages/home/<USER>';
import { emitMessage } from '../../../cafeAppCommunication/cafeAppCommunication';
import { setCurrentPath, setExploreMoreOptionDrawer, setRightSideBar } from '../../../pages/home/<USER>';
import { setOrderStartTimer } from '../../../redux/MetadataSlice';
import UtilityService from '../../../services/UtilityService';
import ComplimentryOrdersActions from '../../../pages/complimentry-orders/ComplimentryOrdersActions';
import CartAction from '../../../sections/cart/CartAction';


CustomerResponsePopups.propTypes = {
  open: PropTypes.bool,
  code: PropTypes.string,
  data: PropTypes.oneOf(PropTypes.object, PropTypes.array, PropTypes.func, PropTypes.number),
};

const StyledBox = styled(Box)(({ theme1, showPadding,code }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  position: 'absolute',
  top: '50%',
  left: '50%',
  zIndex: theme1.zIndex.appBar * 4.5,
  backgroundColor: theme1.palette.mintGreen[100],
  width: '20%',
  minHeight: (code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.DELETE_SAVED_CHAI
            || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.TAKE_OVER_ORDER
  ) ? '30%' :'50%',
  opacity: '1',
  transition: '',
  borderRadius: '20px',
  overflow: 'clip',
  transform: `translate(-50%,-50%)`,
  animation: 'popup 0.3s forwards',
  ...(showPadding && {
    padding: '4px',
  }),
  animationIterationCount: 1,
  '@keyframes popup': {
    '0%': {
      width: '0%',
    },
    '100%': {
      width: code === Constants.CUSTOMER_RESPONSE_POPUP_CODE. SERVICE_CHARGE ? '25%' : '20%',
      minWidth: '300px',
    },
  },
}));

function CustomerResponsePopups({ open, code, data }) {
  const { connectionStatus } = useSelector((store) => store.metadataSlice);
  let customer = useSelector((store) => store.customerSlice.customerBasicInfo);
  const employeeContactNumber = useSelector((store) => store.complimentaryOrdersSlice.employeeContactNumber);
  const isServiceChargeApplied = useSelector(store => store.orderSlice.isServiceChargeApplied);
  const cartItems = useSelector(store => store.cartSlice.cart.cartItems);
  const tdetail = useSelector((store) => store.cartSlice.cart.transactionDetail);
  const allCafeAppProperties = useSelector(store => store.metadataSlice.allCafeAppProperties);

  const [sendOtp, setSendOtp] = useState(true);
  const [otp, setOtp] = useState('');
  const [otpVerified, setOtpVerified] = useState(false);
  const [timeCount, setTimeCount] = useState(59);
  const [getOtp, setGetOtp] = useState(true);

  if(!UtilityService.checkEmpty(customer)){
    customer = {
      ...customer,
      name:customer?.name?.split(" ")[0] || customer?.name
    }
  }
  const [leftTime, setLeftTime] = useState(0);
  const theme = useTheme();
  const navigate = useNavigate();
  const incTime = () => {
    setLeftTime((prevSec) => {
      if (data.timeLeft - prevSec <= 0) {
        dispatch(
          setCustomerResponsePopup({
            open: false,
            code: '',
            data: null,
          })
        );
        handleHomeScreen();
      }
      return prevSec + 1;
    });
  };
  useEffect(() => {
    let idleScreen;
    if (code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IDLE_SCREEN && data.timeLeft - leftTime > 0) {
      idleScreen = setInterval(() => incTime(), 1000);
      dispatch(setCustomerResponsePopup({ open, code, data: { ...data, idleScreen } }))
    }

    return () => {
      setLeftTime(0);
      clearInterval(idleScreen);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, !open]);

  useEffect(() => {
    let interval = null;
    if(code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.SERVICE_CHARGE && !sendOtp){
      setOtp('');
      interval = setInterval(() => {
        setTimeCount((prev) => {
          if (prev > 0) {
            return prev - 1;
          }
          setSendOtp(true);
          return prev;
        });
      }, 1000);
      
    }
    return () => {
      clearInterval(interval)
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sendOtp]);

  if (!open) return <Box />;

  const handleCloseCustomerResponsePopup = () => {
    dispatch(
      setCustomerResponsePopup({
        open: false,
        code: '',
        data: null,
      })
    );
  };
  const handleBtnResponse = () => {
    dispatch(setCustomerResponsePopup({ open: false, code: '', data: null, }));
    if (code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.UPDATE_POS_APP) {
      if (Constants.getEnv() !== Constants.ENV.LOCAL) {
        const currentPath = `${window.location.origin}/kettle-ui/${data.version}`;
        window.location.href = currentPath;
      }
    }
  };
  const handleHomeScreen = () => {
    dispatch(setMembershipOptionDialogue({ open: false }))
    dispatch(setExploreMoreOptionDrawer({ open: false }))
    dispatch(setRightSideBar({ open: false }))
    dispatch(setCurrentPath(RouterLink));
    navigate(PATH_HOME.root);
    emitMessage({ HOME_PAGE: {} });
    dispatch(setOrderStartTimer(false));
    handleCloseCustomerResponsePopup();

  };

  const handleSkipOtp = () => {
    handleCloseCustomerResponsePopup();
    if (!isServiceChargeApplied) {
      CartAction.serviceChargeRemoval(false);
    }
    else {
      CartAction.serviceChargeRemoval(true);
    }
  }

  const handleSubmitOtp = () => {
    if (otp?.length === 4) {
      ComplimentryOrdersActions.verifyOtp(employeeContactNumber, otp, (boolflag) => {
        if (boolflag) {
          setOtpVerified(true);
          handleCloseCustomerResponsePopup();
          if (!isServiceChargeApplied) {
            CartAction.serviceChargeRemoval(cartItems,Constants.N);
          }
          else {
            CartAction.serviceChargeRemoval(cartItems,Constants.Y);
          }
        } else {
          setOtpVerified(false);
          dispatch(
            UtilityService.showSnackBar({
              open: true,
              snackType: Constants.SNACK_TYPE.ERROR,
              message: 'Invalid OTP. Please try again.',
              autoHideDuration: 3000
            }));
          setOtp('');
        }
      });
    }
    else {
      setOtpVerified(false);
    }
  }
const handleGetOtp = () => {
    setSendOtp(false);
    setTimeCount(59);
    dispatch(ComplimentryOrdersActions.setAmDamDetails((status) => {
      if(status){
        setSendOtp(false);
        setTimeCount(59);
        setGetOtp(false);
      }else{
        setSendOtp(true);
        setTimeCount(0);
        setGetOtp(false);
      }
    }))

  }

  const handleOtpChange = (val) => {
    setOtp(val);
  }

  return (
    <Box
      onClick={(e) => {
        // dispatch(setCustomerResponsePopup({ open : false }))
      }}
      sx={{
        overflowY: 'scroll',
        position: 'fixed',
        zIndex: theme.zIndex.appBar * 4,
        height: '100%',
        top: '0',
        width: '100%',
        backgroundColor: theme.palette.specialColors.BLUR_BLACK_SCREEN,
        '&::-webkit-scrollbar': { width: 0 },
      }}
    >
      <StyledBox
        theme1={theme}
        showPadding={code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IDLE_SCREEN}
        code={code}
        onClick={(e) => e.stopPropagation()}
        sx={{border:'0px solid red'}}
      >
        {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IDLE_SCREEN ? (
          <Stack
            sx={{
              background: `conic-gradient(transparent -45deg,${theme.palette.clayRed[500]} 120deg,transparent 0deg )`,
              animation: 'spinn 5s linear infinite',
              position: 'absolute',
              height: '150%',
              width: '200%',
              zIndex: -1,
              borderRadius: '20px',
              '@keyframes spinn': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' },
              },
            }}
          />
        ) : (
          <Box />
        )}
        {Constants.CUSTOMER_RESPONSE_POPUP_CODE.UPDATE_CAFE_APP === code ? (
          <Stack
            sx={{
              flex: '3',
              alignItems: 'center',
              justifyContent: 'center',
              p: 0.5,
            }}
          >
            <Box>
              {connectionStatus ? (
                <Box />
              ) : (
                <StyledIcon
                  onClick={() => handleCloseCustomerResponsePopup()}
                  sx={{
                    position: 'absolute',
                    top: '5%',
                    right: '5%',
                    flex: '1',
                    zIndex: 1,
                    cursor: 'pointer',
                    color: theme.palette.neutral.grey,
                    '&:active': {
                      color: theme.palette.grey[400],
                    },
                  }}
                >
                  {iconConfig.crossIcon}
                </StyledIcon>
              )}
            </Box>
            <Box
              sx={{
                transform: `rotate(45deg)`,
                border: `0.5px solid ${theme.palette.mintGreen[500]}`,
                borderRadius: '50%',
                width: '150px',
                height: '150px',
                justifyContent: 'center',
                alignItems: 'center',
                display: 'flex',
              }}
              variant="body1"
            >
              <Box
                sx={{
                  border: `0.5px solid ${theme.palette.leafGreen[500]}`,
                  borderRadius: '50%',
                  width: '126.77px',
                  height: '126.77px',
                  justifyContent: 'center',
                  alignItems: 'center',
                  display: 'flex',
                }}
                variant="body1"
              >
                <Typography
                  sx={{
                    border: '3px solid  rgba(255, 255, 255, 0)',
                    borderRadius: '50%',
                    borderTop: `3px solid ${theme.palette.leafGreen[500]}`,
                    width: '109.97px',
                    height: '109.97px',
                    WebkitAnimation: 'spin 2s linear infinite',
                    animation: 'spin 2s linear infinite',
                    '@keyframes spin': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    },
                  }}
                />
              </Box>
            </Box>
          </Stack>
        ) : (
          <Stack
            sx={{
              flex: '3',
              alignItems: 'center',
              justifyContent: 'center',
              p: 0.5,
              width: '100%',
              borderRadius: '20px 20px 0px 0px',
              background: code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.SERVICE_CHARGE ? theme.palette.neutral.white : theme.palette.mintGreen[100],
            }}
          >
            { code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IAMDONE
              || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EXPLOREMORE
              || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.UPDATE_CAFE_APP
              || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.UPDATE_POS_APP
              || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IDLE_SCREEN
              || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EDC_FAILED
              || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.DQR_FAILED ?
              <Box
                sx={{
                  display: 'flex',
                  flex: 1,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Box
                  sx={{
                    border: `0.5px solid ${theme.palette.mintGreen[500]}`,
                    borderRadius: '50%',
                    width: '150px',
                    height: '150px',
                    justifyContent: 'center',
                    alignItems: 'center',
                    display: 'flex',
                  }}
                  variant="body1"
                >
                  <Box
                    sx={{
                      border: `0.5px solid ${theme.palette.leafGreen[300]}`,
                      borderRadius: '50%',
                      width: '126.77px',
                      height: '126.77px',
                      justifyContent: 'center',
                      alignItems: 'center',
                      display: 'flex',
                    }}
                    variant="body1"
                  >
                    <Box
                      sx={{
                        // border: '3px solid  rgba(255, 255, 255, 0)',
                        borderRadius: '50%',
                        background: `linear-gradient( 145deg , ${theme.palette.leafGreen[500]}, ${theme.palette.leafGreen[100]})`,
                        width: '109.97px',
                        height: '109.97px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          width: '80%',
                          height: '80%',
                          background: theme.palette.mintGreen[100],
                          borderRadius: '50%',
                        }}
                      >
                        {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IDLE_SCREEN ? (
                          <Typography
                            variant="h4"
                            sx={{
                              color: theme.palette.leafGreen[500],
                            }}
                          >
                            {`${data.timeLeft - leftTime}`}
                          </Typography>
                        ) : (
                          <Box />
                        )}
                        {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IAMDONE ||
                          code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EXPLOREMORE ? (
                          <StyledIcon
                            sx={{
                              width: '45px',
                              height: '30px',
                              color: theme.palette.leafGreen[500],
                            }}
                          >
                            {iconConfig.tickMark}
                          </StyledIcon>
                        ) : (
                          <Box />
                        )}
                        {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EDC_FAILED 
                        || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.DQR_FAILED ? (
                          <StyledIcon
                            sx={{
                              width: '45px',
                              height: '30px',
                              color: theme.palette.clayRed[500],
                            }}
                          >
                            {iconConfig.crossIcon}
                          </StyledIcon>
                        ) : (
                          <Box />
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
              :
              <Box />
            }


            {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IAMDONE ? (
              <Stack
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: 1,
                }}
              >
                <Typography
                  variant="subtitle6"
                  sx={{
                    textAlign: 'center',
                  }}
                >
                  {customer?.name ? customer?.name : 'Customer'}{!UtilityService.checkEmpty(customer?.name) &&
                <StyledIcon
                  sx={{
                    color: theme.palette.leafGreen[500],
                    height: 30,
                    width: 35,
                    verticalAlign:'bottom'
                  }}
                >
                  {iconConfig.saveChaiHeart}
                </StyledIcon>
              }has completed the order
                </Typography>
                <Typography
                  variant="subtitle7"
                  sx={{
                    textAlign: 'center',
                  }}
                >
                  Confirm the customer about the order
                </Typography>
              </Stack>
            ) : (
              <Box />
            )}

            {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EXPLOREMORE ? (
              <Stack
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: 1,
                }}
              >
                <Typography
                  variant="subtitle6"
                  sx={{
                    textAlign: 'center',
                  }}
                >
                  Ask customer for more items to be added in order
                </Typography>
                <Typography
                  variant="subtitle7"
                  sx={{
                    textAlign: 'center',
                  }}
                >
                  Explain the customer about the categories
                </Typography>
              </Stack>
            ) : (
              <Box />
            )}
            {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.UPDATE_POS_APP ? (
              <Stack
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: 1,
                }}
              >
                <Typography
                  variant="subtitle6"
                  sx={{
                    textAlign: 'center',
                  }}
                >
                  POS is redirecting to Cafe App Compatible version
                </Typography>
                <Typography
                  variant="subtitle7"
                  sx={{
                    textAlign: 'center',
                  }}
                >
                  Press Okay to Confirm
                </Typography>
              </Stack>
            ) : (
              <Box />
            )}
            {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IDLE_SCREEN ? (
              <Stack
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: 1,
                }}
              >
                <Typography
                  variant="subtitle6"
                  sx={{
                    textAlign: 'center',
                  }}
                >
                  Seems like it has been a while!
                </Typography>
                <Typography
                  variant="body3"
                  sx={{
                    p: 0.5,
                    textAlign: 'center',
                  }}
                >
                  Are you sure you want to continue the order?
                </Typography>
              </Stack>
            ) : (
              <Box />
            )}
            {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EDC_FAILED 
                        || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.DQR_FAILED ? (
              <Stack
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: 1,
                }}
              >
                <Typography
                  variant="subtitle6"
                  sx={{
                    textAlign: 'center',
                    mb: 5
                  }}
                >
                  {data.heading}
                </Typography>
                <Typography
                  variant="subtitle8"
                  sx={{
                    textAlign: 'center',
                  }}
                >
                  {data.subHeading}
                </Typography>
              </Stack>
            ) : (
              <Box />
            )}
            {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.DELETE_SAVED_CHAI ? (
              <Stack
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: 1,
                }}
              >
                <Typography
                  variant="subtitle2"
                  sx={{
                    textAlign: 'center',
                    width:"80%"
                  }}
                >
                  Do you want to Delete this Chai?
                </Typography>
              </Stack>
            ) : (
              <Box />
            )}
            {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.TAKE_OVER_ORDER ? (
              <Stack
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flex: 1,
                }}
              >
                <Typography
                  variant="subtitle2"
                  sx={{
                    textAlign: 'center',
                    width:"80%"
                  }}
                >
                  Please wait customer is interacting
                </Typography>
              </Stack>
            ) : (
              <Box />
            )}
          </Stack>
        )}

        <Stack
          sx={{
            background: code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.SERVICE_CHARGE ? theme.palette.neutral.white : `linear-gradient(45deg, ${theme.palette.neutral.lightGrey2}, ${theme.palette.neutral.lightGrey2})`,
            width: '100%',
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: '0px 0px 20px 20px',
          }}
        >
          {Constants.CUSTOMER_RESPONSE_POPUP_CODE.UPDATE_CAFE_APP === code ? (
            <Stack
              sx={{
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Typography variant="body4">Cafe App Updating</Typography>
              <Typography variant="body3">Version {data.version}</Typography>
            </Stack>
          ) : (
            <Box />
          )}
          {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IAMDONE ||
            code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EXPLOREMORE ||
            code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.UPDATE_POS_APP ? (
            <Button
              variant="contained"
              sx={{
                width: '50%',
                fontSize: '18px',
              }}
              onClick={() => handleBtnResponse()}
            >
              Okay
            </Button>
          ) : (
            <Box />
          )}
          {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IDLE_SCREEN ? (
            <Stack
              spacing={2}
              sx={{
                width: '100%',
                p: 2,
              }}
              direction="row"
            >
              <Button
                variant="outlined"
                sx={{
                  width: '50%',
                  minHeight: '55px',
                  fontSize: '16px',
                }}
                onClick={() => handleHomeScreen()}
              >
                Go to home
              </Button>

              <Button
                variant="contained"
                sx={{
                  width: '50%',
                  minHeight: '55px',
                  fontSize: '14px',
                }}
                onClick={() => handleBtnResponse()}
              >
                Stay On this order
              </Button>
            </Stack>
          ) : (
            <Box />
          )}
          {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EDC_FAILED 
                        || code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.DQR_FAILED ? (
            <Stack
              spacing={2}
              sx={{
                width: '100%',
                p: 2,
              }}
              direction="row"
              justifyContent='center'
              alignItems='center'
            >
              <Button
                variant="outlined"
                sx={{
                  width: '80%',
                  minHeight: '55px',
                  fontSize: '16px',
                }}
                onClick={() => handleCloseCustomerResponsePopup()}
              >
                {data?.buttonText ? data?.buttonText : "Go with Manual Payment"}
              </Button>
            </Stack>
          ) : (
            <Box />
          )}
          {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.DELETE_SAVED_CHAI ? (
            <Stack
              spacing={2}
              sx={{
                width: '100%',
                p: 2,
              }}
              direction="row"
            >
              <Button
                variant="contained"
                sx={{
                  width: '50%',
                  minHeight: '55px',
                  fontSize: '14px',
                }}
                onClick={() => data.handleDeleteYesResponse()}
              >
                Yes
              </Button>
              <Button
                variant="outlined"
                sx={{
                  width: '50%',
                  minHeight: '55px',
                  fontSize: '16px',
                }}
                onClick={() => handleBtnResponse()}
              >
                No
              </Button>
            </Stack>
          ) : (
            <Box />
          )}
          {code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.TAKE_OVER_ORDER ? (
            <Stack
              spacing={2}
              sx={{
                width: '100%',
                p: 2,
                justifyContent:'center',

              }}
              direction="row"
            >
              <Button
                variant="outlined"
                sx={{
                  width: '80%',
                  minHeight: '55px',
                  fontSize: '14px',
                }}
                onClick={() => handleCloseCustomerResponsePopup()}
              >
                <Stack direction='row' sx={{ alignItems: 'center' }} >
                  <Typography variant='body2'>Takeover this order</Typography>
                  <Box sx={{ width: 20 }} />
                  <StyledIcon sx={{
                    color:theme.palette.leafGreen[500]
                  }}>{iconConfig.doneTick}</StyledIcon>
                </Stack>

              </Button>
            </Stack>
          ) : (
            <Box />
          )}
        </Stack>
        {
          code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.SERVICE_CHARGE ? (
            <Stack
              sx={{
                width: '100%',
                height: '100%',
                background: theme.palette.neutral.white,
                border: '0px solid #222222',
              }}
              flex="1000"
              alignItems="center"
              justifyContent="space-around"
            >
              <Stack sx={{ width: '100%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', mt: -2 }}>
                <Typography variant='subtitle7' sx={{ ml: 3 }}>{tdetail?.serviceCharge > 0 ? 'Remove' : 'Add'} Service Charge</Typography>
                <StyledIcon
                  sx={{ color: theme.palette.neutral.grey, cursor: 'pointer', mr: 2 }}
                  onClick={handleCloseCustomerResponsePopup}
                >
                  {iconConfig.crossIcon}
                </StyledIcon>
              </Stack>
              <MuiOtpInput
                TextFieldsProps={{
                  sx: {
                    width: 60,
                    borderRadius: 1,
                  },
                  inputProps: {
                    inputMode: 'numeric', // Ensure only numeric input is allowed
                    pattern: '[0-9]*',
                    style: {
                      borderColor: theme.palette.neutral.grey,
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderRadius: 9,
                    },
                  }
                }}
                value={otp}
                onChange={handleOtpChange}
              />
              <Typography variant='caption'
                onClick={() => {
                  if (sendOtp && timeCount === 0 && !getOtp) {
                    setTimeCount(59)
                    setSendOtp(false)
                    ComplimentryOrdersActions.requestOtp(employeeContactNumber)
                  }
                }}
                sx={{
                  letterSpacing: 2,
                  mt: -8,
                  color: (sendOtp && timeCount === 0 && !getOtp) ? theme.palette.leafGreen[500] : theme.palette.neutral.black,
                  cursor: (sendOtp && timeCount === 0 && !getOtp) ? 'pointer' : 'not-allowed',
                }}>{!sendOtp ? `RESEND OTP (${timeCount} SECONDS)` : 'RESEND OTP'}</Typography>
              <Stack direction='row' spacing={4} sx={{ alignItems: 'center', justifyContent: 'center', width: '100%' }}>
                <Button
                  variant={allCafeAppProperties?.skipServiceChargeOtp ? "outlined" : "disabled"}
                  onClick={handleSkipOtp}
                  sx={{ background: theme.palette.neutral.white, width: '40%', fontSize: 19, height: 60 }}
                >
                  Skip
                </Button>

                {getOtp ? <Button
                    variant="contained"
                    onClick={handleGetOtp}
                    sx={{ width: '40%', fontSize: 19, height: 60 }}
                  >
                    Get OTP
                </Button> :
                <Button
                  variant={otp?.length === 4 ? "contained" : "disabled"}
                  onClick={handleSubmitOtp}
                  sx={{ width: '40%', fontSize: 19, height: 60 }}
                >
                  Submit
                </Button>}
              </Stack>
            </Stack>
          ) : (<Box />)
        }
      </StyledBox>
    </Box>
  );
}

export default React.memo(CustomerResponsePopups);
