import moment from 'moment/moment';
import {
  setCustomerBasicInfo,
  setFreeLoyaltyTea,
  setLoginVia,
  setLoyaltyPoints,
  setErrorinLogin,
  setEligibleForFreeSecondChai,
  setOtpVerificationDetails,
  setDroolsData,
  clearCustomerSlice,
  setOtpStartTime,
  setOtpEndTime,
  setCustomerLoginEndTime,
  setCustomerLoginStartTime,
  setSavedChais,
  setNewChaiSaved,
} from '../components/customerInfo/CustomerSlice';
// eslint-disable-next-line import/no-cycle
import {
  NavigatePageNumberTo,
  NavigateTo,
  NavigateViaCode,
} from '../components/progressTabButtons/ProgressTabNavigation';
import {
  setDroolsFlowSkipped,
  setExploreMoreOptionDrawer,
  setIsCustomerInteracting,
  setRightSideBar,
  setSelectedBuzzer,
  setShowBuzzerScreen,
} from '../pages/home/<USER>';
import { dispatch, store } from '../redux/store';
import UtilityService from '../services/UtilityService';
import Constants from '../utils/Constants';
// eslint-disable-next-line import/no-cycle
import CartAction from '../sections/cart/CartAction';
import {
  isSubscriptionInCart,
  setComingFromRecharge,
  updateAutoClickOnProceedBtn,
  updateCartItems,
  updateLoyalTeaInCart,
  updateProductsQty,
  updateSpectatorModeCustomization,
  updateSpectatorModeSelectionState,
} from '../sections/cart/CartSlice';
import { testPrinter } from '../services/PrintService';
// eslint-disable-next-line import/no-cycle
import CustomerAction from '../components/customerInfo/customerAction';
import {
  setCreWalletSuggestionResponse,
  setCustomerAvailedWalletOffer,
  setIsRechargingWallet,
} from '../components/payment/PaymentSlice';
import {
  setCafeAppCurrentVersion,
  setConnectionStatus,
  setCurrentPOSVersion,
  setIsTestPrinterPrinted,
} from '../redux/MetadataSlice';
import { progressTabBarConstant } from '../pages/home/<USER>';
import {
  setAllExploreMoreOptionsProductList,
  setClaimedRecomProductData,
  setCurrentMenuCategory,
  setCurrentWalletSuggestion,
  setExploreMoreOptionCartIndexMap,
  setExploreMoreOptionsProducts,
  setIsWalletOrder,
  setRecommendedProductData,
  setRecommendedProductDataIndex,
  setShowRecomAnimation,
  setexoSelectedProductId,
} from '../components/order/OrderSlice';
import { setAppliedCoupon, setShowSubscriptionCoupon, setShowUpsellingRecommProduct } from '../sections/coupon/OffersSlice';
// eslint-disable-next-line import/no-cycle
import PaymentActions from '../components/payment/PaymentActions';
// eslint-disable-next-line import/no-cycle
import OfferAction from '../sections/coupon/OffersAction';
import ProgressTabButtonsAction from '../components/progressTabButtons/ProgressTabButtonsAction';
import { setOfferUnlocked } from '../components/progressTabButtons/ProgressTabButtonsSlice';
import { ProductService } from '../services/ProductService';
import { setCustomerResponsePopup } from '../components/popups/PopupsSlice';
import PackageJson from '../../package.json';
import VersionInfoAction from '../components/RightSideBar/Version_Info/VersionInfoAction';
import { emitMessage } from './cafeAppCommunication';
import { setConnectionSnackbarData } from '../pages/home/<USER>';
// eslint-disable-next-line import/no-cycle
import HomeAction from '../pages/home/<USER>';

export default class CommunicationService {
  static currentCartItems = [];

  static socket = null;

  static pairingStatus = true;

  static client = null;

  static currentUser = {
    userId: 100026,
    unitId: 26018,
    terminalId: 1,
    sessionKeyId:
      'rY63GuwG+N2G7DOtvAWjsyOh+/hIDY4irQwP/ryHm0zfrDe7fdWyMLvL/CsdK6bKNLhx3sgy1LEZRHTav7FG1UmWdJf+J+hORFrzQ6LjCtQ=',
    userName: 'Mohit Malik',
    designation: {
      id: 1009,
      name: 'Admin',
      description: 'General Kettle Admin',
      transactionSystemAccess: true,
      scmSystemAccess: true,
      adminSystemAccess: true,
      clmSystemAccess: true,
      analyticsSystemAccess: true,
      crmSystemAccess: true,
      formsSystemAccess: true,
      channelPartnerSystemAccess: true,
      appInstallerAccess: true,
      maxAllocatedUnits: -1,
      attendanceAccess: false,
      knockApplicationAccess: true,
    },
    screenType: 'POS',
    unitFamily: 'CAFE',
    issuer: 'KETTLE_SERVICE',
    jwtToken:
      'eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5Ijoiclk2M0d1d0crTjJHN0RPdHZBV2pzeU9oKy9oSURZNGlyUXdQL3J5SG0wemZyRGU3ZmRXeU1MdkwvQ3NkSzZiS05MaHgzc2d5MUxFWlJIVGF2N0ZHMVVtV2RKZitKK2hPUkZyelE2TGpDdFE9IiwidW5pdElkIjoyNjAxOCwidGVybWluYWxJZCI6MSwidXNlcklkIjoxMDAwMjYsImlhdCI6MTY4MzAyMzcxMCwiaXNzdWVyIjoiS0VUVExFX1NFUlZJQ0UifQ.pxDRJDExwJWmaW_l7PEgMK0QHwU0Kq20H7kaU1RuXTs',
    googleMerchantId: 'BCR2DN6TVPK7FC3B',
  };

  static sessionId = null;

  static orderType = Constants.ORDER_TYPE.ORDER;

  static subscriber = null;

  static getClient() {
    return CommunicationService.client;
  }

  static setClient(value) {
    CommunicationService.client = value;
  }

  static getPairingStatus() {
    return CommunicationService.pairingStatus;
  }

  static setPairingStatus(value) {
    CommunicationService.pairingStatus = value;
  }

  static getSocket() {
    return CommunicationService.socket;
  }

  static setSocket(value) {
    CommunicationService.socket = value;
  }

  static getOrderType() {
    return CommunicationService.orderType;
  }

  static setOrderType(value) {
    CommunicationService.orderType = value;
  }

  static getCurrentUser() {
    return CommunicationService.currentUser;
  }

  static setCurrentUser(value) {
    CommunicationService.currentUser = value;
  }

  static getSessionId() {
    return `${new Date().getTime()}_${CommunicationService.getSubscriber().unit}_${
      CommunicationService.getSubscriber().terminal
    }`;
  }

  static getSubscriber() {
    const currentUser = CommunicationService.getCurrentUser();
    return {
      unit: currentUser.unitId,
      terminal: currentUser.terminalId,
      type: currentUser.screenType,
    };
  }

  static resetCustomerSocket() {
    return {
      id: null,
      name: null,
      contact: null,
      email: null,
      loyalityPoints: 0,
      contactVerified: false,
      emailVerified: false,
      unitId: UtilityService.unitId,
      newCustomer: false,
      otp: null,
      chaiRedeemed: 0,
      productId: 10,
      otpVerified: false,
    };
  }

  static loginVia(data) {
    dispatch(setCustomerLoginStartTime(UtilityService.getCurrentTime()));
    NavigatePageNumberTo(2);
    dispatch(setLoginVia(data));
    // if (data != null && data.includes('mobile')) {
    //   ProgressTabButtonsAction.updateBoolInPushJson('customerLoggedInViaNum');
    // } else if (data != null && data.includes('faceId')) {
    //   ProgressTabButtonsAction.updateBoolInPushJson('customerLoggedInViaFaceIt');
    // }
    dispatch(setErrorinLogin(false));
  }

  static orderStart() {
    dispatch(setExploreMoreOptionsProducts(null));
    dispatch(setCurrentWalletSuggestion(null));
    dispatch(setCustomerAvailedWalletOffer(false));
    dispatch(setExploreMoreOptionCartIndexMap({}));
    dispatch(setErrorinLogin(false));
    const testPrinterPrinted = store.getState().metadataSlice.isTestPrinterPrinted;
    console.log('Order Started');
    dispatch(setConnectionStatus(true));
    dispatch(updateCartItems([]));
    dispatch(setIsRechargingWallet(false));
    if (!testPrinterPrinted) {
      testPrinter();
      dispatch(setIsTestPrinterPrinted(true));
    }

    NavigateTo(1, false);
    NavigatePageNumberTo(1);
    CustomerAction.sendLoyaltyBurnOfferDetailsToCrm();
  }

  static takeOver() {
    console.log('taking over the Screen from Cafe App');
  }

  static handleUserAvailedWalletSuggestion() {
    dispatch(setCreWalletSuggestionResponse(true));
    dispatch(setRightSideBar({ open: false }));
    dispatch(
      UtilityService.showSnackBar({ open: true, message: 'Customer has availed the wallet offer' })
    );
  }

  static login(data) {
    const customerInfo = data.customerInfo;
    dispatch(setOtpEndTime(UtilityService.getCurrentTime()));
    if (!UtilityService.checkEmpty(customerInfo)) {
      NavigatePageNumberTo(3);
      dispatch(setCustomerBasicInfo(customerInfo));
      if (data.type === Constants.LoginMethodCode.faceid) {
        ProgressTabButtonsAction.updateBoolInPushJson('customerLoggedInViaFaceIt');
      } else {
        ProgressTabButtonsAction.updateBoolInPushJson('customerLoggedInViaNum');
      }
      CustomerAction.getPreviousOrders(customerInfo.id);
      // CustomerAction.getSavedChais(customerInfo.id);
      CustomerAction.getChaayosCashData(customerInfo.id);
      CustomerAction.getCustomerVisitDetail(customerInfo.id);
      dispatch(OfferAction.getRecommendationOffersData(customerInfo.id));
      ProgressTabButtonsAction.setCustomerTypeForGoals(customerInfo);
      // dispatch(setLoyaltyPoints(customerInfo.loyalityPoints));
      const freeLoyaltyTeas = Math.trunc(customerInfo.loyalityPoints / 60);
      dispatch(setFreeLoyaltyTea(freeLoyaltyTeas));
      dispatch(setCustomerLoginEndTime(UtilityService.getCurrentTime()));
      if (freeLoyaltyTeas === 0) {
        ProgressTabButtonsAction.removeLoyalteaRedemptionGoal();
      }
      // if(Math.trunc(customerInfo.loyalityPoints/60)>0)
      // {
      //  dispatch(setRightSideBar({open:true,code: Constants.RIGTHSIDE_BAR.DURING_LOGIN_PROCESS, data: {message: Constants.RIGTHSIDE_BAR.LOGIN_SUCCESSFUL }}))
      // }
      const lastOrderTime =
        customerInfo.lastVisitTime !== null
          ? moment(customerInfo.lastVisitTime).format('YYYY-MM-DD HH:mm:ss')
          : null;
      if (lastOrderTime !== null) {
        const diffInMins = moment().diff(lastOrderTime, 'minutes');
        const diffInDays = moment().diff(lastOrderTime, 'days');
        if (
          customerInfo.eligibleForSignupOffer
          // && customerInfo.orderCount === 1 &&
          // diffInMins > 45 &&
          // diffInDays <= 30
        ) {
          dispatch(setEligibleForFreeSecondChai(true));
          dispatch(setFreeLoyaltyTea(1));
        } else {
          dispatch(setEligibleForFreeSecondChai(false));
        }

      }
      CustomerAction.getCustomerLoyaltyBurnOfferDetails(customerInfo);
      ProgressTabButtonsAction.keyIncrementer(['loggedInUsers', 'totalOrders']);
      ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.login, true);
          dispatch(OfferAction.fetchCartRulesRecommendations({
            customerId: customerInfo.id, 
            dayPart: '',
            unitId: UtilityService.getUnitId(), 
            unitCategory: '', 
            unitRegion: '',
            custType: customerInfo?.newCustomer ? 'NEW':"EXISTING", 
      }))
    }
  }

  static crmAddItemToCart(data) {
    // const currentCartItems= JSON.stringify(store.getState().cartSlice.cart.cartItems);
    // dispatch(CartAction.addCRMItemToCart(currentCartItems,cartItem));
    dispatch(updateCartItems(data?.cartItems));
    dispatch(updateLoyalTeaInCart(data?.loyalTeaInCart));
    dispatch(updateProductsQty(data?.productsQtyMap));
    dispatch(setClaimedRecomProductData(data?.recomOfferClaimedData));
    if (!UtilityService.checkEmpty(data?.recomOfferClaimedData)) {
      dispatch(setAppliedCoupon(Constants.COUPONS.RECOMMENDATION_OFFER));
    }
    CartAction.getFinalTransactionDetail(data.cartItems);
  }
  // crmAddItemToCart () {
  //   return (dispatch,getState) => {
  //     console.log('css');
  //     const currentCartItems= getState().cartSlice.cart.cartItems;
  //     // dispatch(addProduct())
  //   }
  // }

  static screeOpenedOnCrm(data) {
    const isRechargingWallet = store.getState().paymentSlice.isRechargingWallet;
    const {unitMenu} = store.getState().orderSlice;
    const droolsFlowSkipped = store.getState().homeSlice.droolsFlowSkipped;
    if (isRechargingWallet) {
      return;
    }
    if (!UtilityService.checkEmpty(data.offerText) && data.offerText === false) {
      return;
    }
    if (
      data.screenName !== Constants.RIGTHSIDE_BAR.LOYALTEA_SELECTION_SCREEN &&
      data.screenName !== Constants.RIGTHSIDE_BAR.CHAI_SELECTION_SCREEN &&
      data.screenName !== Constants.RIGTHSIDE_BAR.MILESTONE_SCREEN &&
      data.screenName !== Constants.RIGTHSIDE_BAR.UNLOCK_PRODUCT && 
      data?.screenName !== Constants.RIGTHSIDE_BAR.LOYALTY_BURN_REDEMPTION_SELECTION
    ) {
      dispatch(
        setRightSideBar({
          open: true,
          code: Constants.RIGTHSIDE_BAR.DURING_LOGIN_PROCESS,
          data: { message: data.screenName, ...data },
        })
      );
    }
    if (
      data?.screenName === Constants.RIGTHSIDE_BAR.LOYALTEA_SELECTION_SCREEN ||
      data?.screenName === Constants.RIGTHSIDE_BAR.CHAI_SELECTION_SCREEN || 
      data?.screenName === Constants.RIGTHSIDE_BAR.LOYALTY_BURN_REDEMPTION_SELECTION   
    ) {
      if(droolsFlowSkipped){
        dispatch(
          setRightSideBar({
            open: false,
            code: null,
            data: {},
          })
        );
      }     
      NavigateViaCode(progressTabBarConstant.ORDER, false);
      // if(!UtilityService.checkEmpty(data?.droolsFlowSkip) && data?.droolsFlowSkip === true && droolsFlowSkipped){
      //   dispatch(
      //     setRightSideBar({
      //       open: true,
      //       code: Constants.RIGTHSIDE_BAR.ORDER_CATEGORY_DEFAULT_SIDEBAR,
      //       data: {},
      //     })
      //   );
      //   dispatch(setDroolsFlowSkipped(false));
      // }
      let currentMenuCategory = 0;
      unitMenu?.forEach((category,index)=>{
        if(category?.name?.toLowerCase()?.includes(Constants.UNIT_MENU_CATEGORY.COLD)){
            currentMenuCategory = index;
        }
      })
      if(data?.screenName === Constants.RIGTHSIDE_BAR.LOYALTY_BURN_REDEMPTION_SELECTION){
          dispatch(setCurrentMenuCategory(currentMenuCategory));
          dispatch(
            UtilityService.showSnackBar({ open: true, message: 'Customer wants to redeem the cold beverage' })
          );
      }
      if(data?.screenName === Constants.RIGTHSIDE_BAR.LOYALTEA_SELECTION_SCREEN){
        dispatch(
          UtilityService.showSnackBar({ open: true, message: 'Customer wants to redeem the hot beverage' })
        );
    }
    }

    /**
     * Goal Tracking
     */
    if (data.screenName === Constants.RIGTHSIDE_BAR.LOYALTEA_REDEMPTION) {
      const eligibleForFreeSecondChai = store.getState().customerSlice.eligibleForFreeSecondChai;
      if (eligibleForFreeSecondChai) {
        ProgressTabButtonsAction.updateBoolInPushJson('signupOfferRedemptionShown');
        ProgressTabButtonsAction.keyIncrementer(['noOfSecondChaiShown']);
        // ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.secondFcRedeemed, false);
      } else {
        ProgressTabButtonsAction.updateBoolInPushJson('loyaltyOfferRedemptionShown');
        ProgressTabButtonsAction.keyIncrementer(['loyaltyRedemptionsAvailable']);
      }
    }
    // if (data.screenName === Constants.RIGTHSIDE_BAR.WALLET_SUGGESTION) {
    // }

    if (data.screenName === Constants.RIGTHSIDE_BAR.UNLOCK_PRODUCT) {
      if (!UtilityService.checkEmpty(data.offerUnlockedData)) {
        ProgressTabButtonsAction.updateBoolInPushJson('unlockOfferShown');
        ProgressTabButtonsAction.updateBoolInPushJson('unlockedOfferInformedByCRE');
        dispatch(setOfferUnlocked(data.offerUnlockedData));
        ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.surpriseOfferShown, false);
        ProgressTabButtonsAction.setGoalStatus(Constants.GOAL_NAME.surpriseOfferAvailed, false);
        ProgressTabButtonsAction.keyIncrementer(['surprisesApplicable']);
      }
    }

    if (data.screenName === Constants.RIGTHSIDE_BAR.LOYALTEA_INFO) {
      ProgressTabButtonsAction.updateBoolInPushJson('loyaltyOfferShown');
      ProgressTabButtonsAction.keyIncrementer(['loyaltiesShown']);
    }

    if (data.screenName === Constants.RIGTHSIDE_BAR.SECOND_FREE_CHAI_INFO) {
      ProgressTabButtonsAction.updateBoolInPushJson('signUpOfferShown');
      ProgressTabButtonsAction.keyIncrementer(['signUpsShown']);
    }
  }

  static recommendationData(data) {
    const products = {};
    const recommendationDataIndex = {};
    data?.forEach((product, idx) => {
      if (product.productId >= 0) {
        products[product.productId] = {
          productId: product.productId,
          display: true,
          recomReason: product.recomReason,
        };
        recommendationDataIndex[idx] = product.productId;
      }
    });
    dispatch(setRecommendedProductData(products));
    dispatch(setRecommendedProductDataIndex(recommendationDataIndex));
  }

  static recommendation(data) {
    let products = JSON.parse(JSON.stringify(store.getState().orderSlice.recommendedProductData));
    if (!UtilityService.checkEmpty(data)) {
      const recommendationDataIndex = {};

      if (!UtilityService.checkEmpty(products)) {
        Object.keys(products)?.forEach((productId) => {
          products[productId] = {
            productId,
            display: false,
            recomReason: products[productId].recomReason,
          };
        });
      } else {
        products = {};
        data?.forEach((product, idx) => {
          recommendationDataIndex[idx] = product.productId;
        });
        dispatch(setRecommendedProductDataIndex(recommendationDataIndex));
      }
      data?.forEach((product) => {
        products[product.productId] = {
          productId: product.productId,
          display: product.display,
          recomReason: product.recRe,
        };
      });
      dispatch(setRecommendedProductData(products));
    } else if (!UtilityService.checkEmpty(products)) {
      Object.keys(products)?.forEach((productId) => {
        products[productId] = {
          productId,
          display: false,
          recomReason: products[productId].recomReason,
        };
      });
      dispatch(setRecommendedProductData(products));
    }
  }

  static faceLoginError() {
    dispatch(setLoginVia('Error in login in via face'));
    dispatch(setErrorinLogin(true));
    NavigatePageNumberTo(2);
  }

  static contactLoginError() {
    dispatch(setLoginVia('Error in login in via mobile'));
    dispatch(setErrorinLogin(true));
    NavigatePageNumberTo(2);
  }

  // static navigateToOrder() {
  //   NavigateViaCode(progressTabBarConstant.ORDER);
  // }

  static errorInOtpVerification(data) {
    if (
      data.otpSource === Constants.OTPConstants.CONTACT_SIGNUP ||
      data.otpSource === Constants.OTPConstants.FACE_SIGNUP ||
      data.otpSource === Constants.OTPConstants.FACE_UPDATE
    ) {
      dispatch(setErrorinLogin(true));
      NavigatePageNumberTo(2);
    } else {
      const otpVerificationDetails = store.getState().customerSlice.otpVerificationDetails;
      dispatch(
        setOtpVerificationDetails({
          ...otpVerificationDetails,
          otpLoader: null,
        })
      );
    }
  }

  static reVerifyingOtp(data) {
    if (
      data.otpSource === Constants.OTPConstants.CONTACT_SIGNUP ||
      data.otpSource === Constants.OTPConstants.FACE_SIGNUP ||
      data.otpSource === Constants.OTPConstants.FACE_UPDATE
    ) {
      dispatch(setErrorinLogin(false));
      NavigatePageNumberTo(2);
    } else {
      const otpVerificationDetails = store.getState().customerSlice.otpVerificationDetails;
      dispatch(
        setOtpVerificationDetails({
          otpLoader: true,
          preOtpVerifiedData: otpVerificationDetails.preOtpVerifiedData,
          reason: otpVerificationDetails.reason,
          heading: otpVerificationDetails.heading,
        })
      );
    }
  }

  static showLoyalteaOtp() {
    dispatch(setOtpStartTime(UtilityService.getCurrentTime()));
    const { eligibleForFreeSecondChai } = store.getState().customerSlice;
    dispatch(
      setOtpVerificationDetails({
        otpLoader: true,
        preOtpVerifiedData: {},
        reason: Constants.OTPConstants.SECOND_FREE_CHAI,
        heading: eligibleForFreeSecondChai ? 'Second Free Chai redemption' : 'Loyalty redemption',
      })
    );
  }

  static openWalletSuggestionPayment(data) {
    dispatch(
      UtilityService.showSnackBar({ open: true, message: 'Customer has availed the wallet offer' })
    );

      dispatch(PaymentActions.walletSuggestionPayment('customer'));

    dispatch(setCreWalletSuggestionResponse(true));
    dispatch(setIsWalletOrder(true));
    dispatch(setRightSideBar({ open: false }));
    dispatch(setCustomerAvailedWalletOffer(true));
    //  Goal tracking
    ProgressTabButtonsAction.updateBoolInPushJson('walletPurchasedByCustomer');
  }

  static updateDroolsData(data) {
    dispatch(setDroolsData(data));
    if (data != null && data.GAME_SECTION != null && !data.GAME_SECTION.includes('UNLOCK_OFFER')) {
      ProgressTabButtonsAction.removeSurpriseGoals();
    }
    // if (data != null && data.SELECT_SECTION != null && data.SELECT_SECTION.length === 0) {
    //   ProgressTabButtonsAction.removeSelectSuggestionGoal();
    // }
  }

  static startOtherOrders() {
    dispatch(clearCustomerSlice());
  }

  static customerLoginOtpVerification() {
    dispatch(setOtpStartTime(UtilityService.getCurrentTime()));
    dispatch(setLoginVia('Verifying OTP'));
    dispatch(setErrorinLogin(false));
    NavigatePageNumberTo(2);
  }

  static buyChaayosSelect(data) {
    if (data) {
      const { unitSubscriptionProducts } = store.getState().orderSlice;
      CartAction.handleBuyMembership(unitSubscriptionProducts[Constants.COUPONS.CHAAYOS_SELECT]);
    }
  }

  static setInitialSpectatorModeLayout = async (data) => {
    if (
      UtilityService.checkEmpty(data) ||
      UtilityService.getParentIdForMilkSelection(data.productId) !== Constants.DESI_CHAI_ID
    ) {
      return;
    }
    const { productBasicDetail, productCustomization } = store.getState().orderSlice;
    const customization = productCustomization[data.productId];
    const parentIdForMilkSelection = UtilityService.getParentIdForMilkSelection(data.productId);
    const productDetail = productBasicDetail.products[data.productId];
    const selectedDimension = ProductService.getDefaultDimension(customization);
    let selectedMilkType;
    let selectedFreeAddons;
    let selectedPaidAddons;
    const selectedVariants = {};
    const toggleState = [true, false, false, false, false, false];

    // if (parentIdForMilkSelection > -1) {
    //   selectedMilkType = data.productId
    // }
    // customization.prices[selectedDimension]?.variant.forEach((variant) => {
    //   selectedVariants[variant.name] = { ...variant.options[0], variantName: variant.name };
    // });

    dispatch(setRightSideBar({open:false}));

    const processedItem = await CartAction.getProcessedItem({
      productData: { productDetail, customization },
      selectedMilkType,
      selectedDimension,
      selectedFreeAddons,
      selectedPaidAddons,
      selectedVariants,
      isCustomisable: true,
      loyalTea: data?.hasBeenRedeemed,
      recProd: false,
    });
    dispatch(setIsCustomerInteracting(true));
    // if(!UtilityService.checkEmpty(selectedMilkType))
    // {
    //   toggleState[0] = true;
    // }
    // else if(!UtilityService.checkEmpty(selectedVariants))
    // {
    //   toggleState[1] = true;
    // }
    // else if(!UtilityService.checkEmpty(customization.prices[selectedDimension]?.addon)){
    //     toggleState[3] =true;
    // }
    // else{
    //   toggleState[4] = true;
    // }
    dispatch(updateSpectatorModeSelectionState(toggleState));
    dispatch(updateSpectatorModeCustomization(processedItem));
  };

  static handleCustomerResponsePopup(bool, data) {
    const exploreMoreOptionsProduct = store.getState().orderSlice.exploreMoreOptionsProducts;
    if (
      data.message === Constants.CUSTOMER_RESPONSE_POPUP_CODE.EXPLOREMORE &&
      !UtilityService.checkEmpty(exploreMoreOptionsProduct)
    ) {
      dispatch(setShowRecomAnimation(true));
    } else {
      const customerPopup = store.getState().popupsSlice.customerResponsePopup;
      if (customerPopup?.open) {
        dispatch(setCustomerResponsePopup({ open: false, code: '', data: null }));
      }
      if (data.message === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IAMDONE) {
        if (HomeAction.getCodeForSelectedTab() === progressTabBarConstant.ORDER) {
          dispatch(updateAutoClickOnProceedBtn(true));
        }
        return;
      }
      dispatch(
        setCustomerResponsePopup({
          open: bool,
          code: data?.message,
          data,
        })
      );
    }
  }

  static setVersionDetails(data) {
    const { cafeAppCurrentVersion } = store.getState().metadataSlice;
    if (!UtilityService.checkEmpty(data[Constants.SCREEN_TYPE.POS].version)) {
      dispatch(setCurrentPOSVersion(data[Constants.SCREEN_TYPE.POS].version));
    }
    dispatch(setCafeAppCurrentVersion(data[Constants.SCREEN_TYPE.CAFE_APP].version));
    if (UtilityService.checkEmpty(cafeAppCurrentVersion)) {
      dispatch(
        VersionInfoAction.getLatestVersionInfo({
          cafeAppCurrentVersion: data[Constants.SCREEN_TYPE.CAFE_APP].version,
        })
      );
    }
    if (
      !UtilityService.checkEmpty(data[Constants.SCREEN_TYPE.POS].version) &&
      PackageJson.version !== data[Constants.SCREEN_TYPE.POS].version
    ) {
      dispatch(VersionInfoAction.redirectToLatestPOS(data[Constants.SCREEN_TYPE.POS].version));
    }
  }

  static redeemptionOtpVerified(data) {
    dispatch(setOtpEndTime(UtilityService.getCurrentTime()));
    dispatch(setCustomerBasicInfo(data));
    const tdetail = store.getState().cartSlice.cart.transactionDetail;
    emitMessage({ COUPON_APPLIED: { transactionDetail: tdetail } });
  }

  static loginProcessStarted() {
    dispatch(setShowBuzzerScreen(false));
    dispatch(setLoginVia('Customer entering Phone number'));
  }

  static actionPerformedOnCrm = () => {
    const isIdleScreenPopup = store.getState().popupsSlice.customerResponsePopup;
    if (isIdleScreenPopup?.code === Constants.CUSTOMER_RESPONSE_POPUP_CODE.IDLE_SCREEN) {
      dispatch(setCustomerResponsePopup({ open: false, code: '', data: null }));
      clearInterval(isIdleScreenPopup?.data?.idleScreen);
    }
  };

  /**
   * on cafe app disconnection & wifi is not Sunshine
   */
  static showConnectivitySteps = (msg, name, status) => {
    dispatch(
      setConnectionSnackbarData({ open: true, message: msg, autoHideDuration: null, name, status })
    );
  };

  static hideConnectivitySteps = () => {
    dispatch(setConnectionSnackbarData({ open: false }));
  };

  static onConnectionStatusChange = (connectionStatus) => {
    const hotspotWifi = `POS_${UtilityService.getUnitId()}_${UtilityService.getTerminalId()}`;

    if (!connectionStatus && !UtilityService.checkEmpty(UtilityService.lastWifiSsidPos)) {
      if (UtilityService.lastWifiSsidPos !== Constants.CAFE_WIFI_SSID) {
        CommunicationService.showConnectivitySteps(
          'Connect Cafe App to',
          hotspotWifi,
          connectionStatus
        );
      } else {
        CommunicationService.showConnectivitySteps(
          'For connectivity connect Cafe App to',
          Constants.CAFE_WIFI_SSID,
          connectionStatus
        );
      }
    }

    if (connectionStatus) {
      if (
        !UtilityService.checkEmpty(UtilityService.lastWifiSsidPos) &&
        UtilityService.lastWifiSsidPos === Constants.CAFE_WIFI_SSID &&
        !UtilityService.checkEmpty(UtilityService.lastWifiSsidCafeApp) &&
        UtilityService.lastWifiSsidCafeApp === hotspotWifi
      ) {
        CommunicationService.showConnectivitySteps(
          'For faster connectivity connect Cafe App to',
          Constants.CAFE_WIFI_SSID,
          connectionStatus
        );
      } else {
        CommunicationService.hideConnectivitySteps();
      }
    }
  };

  static handleWalletOfferApplied = (data) => {
    if (data.applied === true) {
      dispatch(
        UtilityService.showSnackBar({
          open: true,
          snackType: Constants.SNACK_TYPE.SUCCESS,
          message: 'Customer has taken Wallet Offer',
        })
      );
      dispatch(setCustomerAvailedWalletOffer(true));
      dispatch(setIsWalletOrder(true));
      dispatch(setCreWalletSuggestionResponse(true));
      dispatch(setComingFromRecharge(false));
    } else {
      dispatch(
        UtilityService.showSnackBar({
          open: true,
          snackType: Constants.SNACK_TYPE.ERROR,
          message: 'Customer has Removed Wallet Offer',
        })
      );
      dispatch(setIsWalletOrder(false));
      dispatch(setCustomerAvailedWalletOffer(false));
      dispatch(setCreWalletSuggestionResponse(false));
      dispatch(setComingFromRecharge(false));
    }
  };

  static handleExploreMoreOptions = (data) => {
    const recomData = [];
    if (!UtilityService.checkEmpty(data)) {
      data.forEach((product) => {
        recomData.push({ ...product, quantity: 0 });
      });
    }
    dispatch(setExploreMoreOptionsProducts(recomData));
  };

  static handleExploreMoreOptionsClose = () => {
    // dispatch(setexoSelectedProductId(null));
    // dispatch(setExploreMoreOptionDrawer(null));
    // dispatch(setRightSideBar({ open: false }));
    dispatch(setShowRecomAnimation(false));
  };

  static handleSavedChaiUpdated = (data) => {
    dispatch(setSavedChais(data));
  };

  static handleSuccessMetricEvents = (data) => {
    if (data?.eventName === Constants.SUCCESS_METRIC_EVENTS.NEW_CHAI_SAVED && data?.eventData) {
      dispatch(setNewChaiSaved(true));
    }
  };

  static handelAllExploreMoreOptionsProductList(data) {
    const exploreMoreProductsMap = {};
    data?.forEach((product) => {
      exploreMoreProductsMap[product.productId] = product;
    });
    dispatch(setAllExploreMoreOptionsProductList(exploreMoreProductsMap));
  }

  static payViaWallet() {
    dispatch(PaymentActions.payViaWallet());
  }

  static setWalletSuggestionAbTestingData(data){
    UtilityService.WalletSuggestionAbTestingData = data;
  }

  static showUpsellingRecommendation = async(data) =>{
    const { productImageMap } = store.getState().orderSlice;
    let showProduct = false;
    data?.forEach((product)=>{
      if(!showProduct){
        productImageMap[product?.productId].forEach((prodImg) => {
          if (prodImg.imageType === Constants.IMAGE_TYPES.RECOMMENDATION_IMAGE_1200X1200) {
            showProduct = true;
          }
        });
        if(!CartAction.isInventoryAvalable(product?.productId)
          || UtilityService.checkEmpty(CartAction.priceExits(product?.productId))
        ){
          showProduct = false
        }
      }
    })



    if(showProduct){
      store.dispatch(setRightSideBar({
        open:true,
        code: Constants.RIGTHSIDE_BAR.UPSELLING_RECOMENDATION,
        data
      }))
    }
    return showProduct;
  }

  static closeRightSideBar(data){
    const { rightSideBar } = store.getState().homeSlice;
    if(rightSideBar?.code === data?.message){
      dispatch(setRightSideBar({ open: false }));
    }

  }

  static openRightSideBar(data){
    dispatch(setRightSideBar({
      open:true,
      code: data?.code,
      data:data?.data
    }))
  }


}
